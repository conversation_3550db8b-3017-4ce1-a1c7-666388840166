const fs = require('fs');
const path = require('path');
const prettier = require('./patent-client/node_modules/prettier');

// Function to remove comments and format a file
async function cleanAndFormatFile(filePath) {
  try {
    // Read the file content
    let content = fs.readFileSync(filePath, 'utf8');

    // Store original content length for comparison
    const originalLength = content.length;

    // Remove multi-line comments (/* ... */)
    content = content.replace(/\/\*[\s\S]*?\*\//g, '');

    // Remove single-line comments (// ...) but not URLs (http://)
    content = content.replace(/([^:])\/\/.*$/gm, '$1');

    // Remove empty lines that might be left after removing comments
    content = content.replace(/^\s*[\r\n]/gm, '');

    // Format the code with Prettier
    try {
      // Get Prettier config (if any)
      const prettierConfig = await prettier.resolveConfig(filePath);

      // Format the code
      content = await prettier.format(content, {
        ...prettierConfig,
        filepath: filePath,
      });

      // Write the formatted content back to the file
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Cleaned and formatted: ${filePath}`);
      return true;
    } catch (formattingError) {
      console.error(`Error formatting ${filePath}:`, formattingError.message);

      // If formatting fails, at least save the comment-free version
      if (content.length !== originalLength) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`Removed comments from (but couldn't format): ${filePath}`);
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error(`Error processing file ${filePath}:`, error.message);
    return false;
  }
}

// Function to recursively process all JS/JSX files in a directory
async function processDirectory(directory) {
  const items = fs.readdirSync(directory);
  let totalProcessed = 0;

  for (const item of items) {
    const itemPath = path.join(directory, item);
    const stats = fs.statSync(itemPath);

    if (stats.isDirectory()) {
      // Recursively process subdirectories
      totalProcessed += await processDirectory(itemPath);
    } else if (stats.isFile() && (itemPath.endsWith('.js') || itemPath.endsWith('.jsx'))) {
      // Process JS/JSX files
      if (await cleanAndFormatFile(itemPath)) {
        totalProcessed++;
      }
    }
  }

  return totalProcessed;
}

// Main function
async function main() {
  const targetDir = process.argv[2] || './patent-client/src';

  if (!fs.existsSync(targetDir)) {
    console.error(`Directory not found: ${targetDir}`);
    process.exit(1);
  }

  console.log(`Cleaning and formatting JavaScript/JSX files in: ${targetDir}`);
  const totalProcessed = await processDirectory(targetDir);
  console.log(`Completed! Processed ${totalProcessed} files.`);
}

main().catch(error => {
  console.error('An error occurred:', error);
  process.exit(1);
});
