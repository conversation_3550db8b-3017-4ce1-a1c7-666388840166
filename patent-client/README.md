# PatentPionner Dashboard Application

This application consists of a React frontend and Express.js backend with MongoDB integration.

## Project Structure

- `patentpionner-dashboard/`: Frontend React application
- `backend/`: Express.js backend API with MongoDB

## Setup Instructions

### Prerequisites

- Node.js (v14+ recommended)
- MongoDB (local installation or MongoDB Atlas)

### Backend Setup

1. Navigate to the backend directory:
   ```
   cd backend
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Configure MongoDB:
   - Edit the `.env` file in the backend directory
   - Update `MONGODB_URI` with your MongoDB connection string

4. Seed the database with an admin user:
   ```
   npm run data:import
   ```
   This will create an admin user with the following credentials:
   - Username: admin
   - Password: admin123

5. Start the backend server:
   ```
   npm run dev
   ```
   The server will run on http://localhost:5000

### Frontend Setup

1. Navigate to the frontend directory:
   ```
   cd patentpionner-dashboard
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the frontend development server:
   ```
   npm run dev
   ```
   The application will open in your browser at http://localhost:5173

### Running Both Frontend and Backend

You can run both servers simultaneously using:
```
cd patentpionner-dashboard
npm run dev:all
```

## API Endpoints

### Authentication
- `POST /api/users/login` - Login with username and password
- `POST /api/users/register` - Register a new user

### Courses
- `GET /api/courses` - Get all courses
- `GET /api/courses/:id` - Get a specific course
- `POST /api/courses` - Create a new course (admin only)
- `PUT /api/courses/:id` - Update a course (admin only)
- `DELETE /api/courses/:id` - Delete a course (admin only)

### Purchases
- `GET /api/purchases` - Get all purchases (admin only)
- `GET /api/purchases/my` - Get current user's purchases
- `GET /api/purchases/:id` - Get a specific purchase
- `POST /api/purchases` - Create a new purchase

## Default Admin Credentials
- Username: admin
- Password: admin123