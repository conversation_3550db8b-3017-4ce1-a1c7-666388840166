{"name": "patentpionner-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "start": "vite", "backend": "cd ../backend && npm run dev", "dev:all": "concurrently \"npm run dev\" \"npm run backend\"", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.2", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.2.0", "@styled-icons/fa-solid": "^10.47.0", "@styled-icons/material": "^10.47.0", "@tailwindcss/vite": "^4.1.4", "axios": "^1.8.4", "formik": "^2.4.6", "jwt-decode": "^4.0.0", "lucide-react": "^0.503.0", "react": "^18.3.1", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-icon": "^1.0.0", "react-icons": "^5.5.0", "react-quill": "^2.0.0", "react-redux": "^9.2.0", "react-router-dom": "^6.16.0", "react-toastify": "^11.0.5", "styled-components": "^6.0.8", "tailwindcss": "^4.1.4", "yup": "^1.6.1"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "concurrently": "^8.2.2", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "prettier": "^3.5.3", "vite": "^5.2.0"}}