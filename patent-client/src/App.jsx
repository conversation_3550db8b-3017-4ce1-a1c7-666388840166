import React, { useState, useEffect } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { ThemeProvider } from "styled-components";
import { theme } from "./styles/GlobalStyles";
import GlobalStyles from "./styles/GlobalStyles";
import { AuthProvider, useAuth } from "./contexts/AuthContext";
import { ToastProvider } from "./contexts/ToastContext";
import Layout from "./components/layout/Layout";
import LandingLayout from "./components/Layout";
import Dashboard from "./components/dashboard/Dashboard";
import CourseList from "./components/courses/CourseList";
import MyCourses from "./components/courses/MyCourses";
import Courses from "./components/courses/Courses";
import CourseForm from "./components/courses/CourseForm";
import UserList from "./components/users/UserList";
import PurchaseList from "./components/purchases/PurchaseList";
import Settings from "./components/settings/Settings";
import Profile from "./components/settings/Profile";
import AdminRoute from "./components/auth/AdminRoute";
import {
  BookList,
  BookDetailPage,
  EditBookPage,
  NewBookPage,
  MyBooks,
} from "./components/books";
import {
  InsightsList,
  InsightForm,
  InsightView,
} from "./components/insights";
import Home from "./pages/Home";
import Shop from "./pages/Shop";
import Contact from "./pages/Contact";
import Course from "./pages/Course";
import Insights from "./pages/Insights";
import About from "./pages/About";
import Signup from "./pages/Signup";
import LoginLanding from "./pages/login";
import VerificationPage from "./pages/VerificationPage";
import VerifyEmailPage from "./pages/VerifyEmailPage";
import VerifyEmailRedirect from "./pages/VerifyEmailRedirect";
import ForgotPassword from "./pages/ForgotPassword";
import ResetPassword from "./pages/ResetPassword";
import AdminShipment from "./components/shipment/Shipment";
import MyShipments from "./components/shipment/MyShipments";
import CourseDetail from "./pages/CourseDetail";
import PatentMuseum from "./pages/PatentMuseum";
import Faq from "./pages/Faq";
const ProtectedRoute = ({ children }) => {

  const { user, loading } = useAuth();
  if (loading) return <div>Loading...</div>;
  if (!user) {
    return <Navigate to="/login" replace />;
  }
  return children;
};
function App() {
  const [cartItems, setCartItems] = useState(() => {
    const savedCart = localStorage.getItem("patentpionner_cart");
    return savedCart ? JSON.parse(savedCart) : [];
  });
  useEffect(() => {
    localStorage.setItem("patentpionner_cart", JSON.stringify(cartItems));
  }, [cartItems]);
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === "patentpionner_cart") {
        try {
          const newCart = JSON.parse(e.newValue || "[]");
          setCartItems(newCart);
        } catch (error) {
          // console.error("Error parsing cart data from localStorage:", error);
        }
      }
    };
    window.addEventListener("storage", handleStorageChange);
    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);
  const syncCartWithLocalStorage = () => {
    const savedCart = localStorage.getItem("patentpionner_cart");
    if (savedCart) {
      try {
        const parsedCart = JSON.parse(savedCart);
        setCartItems(parsedCart);
      } catch (error) {
        // console.error("Error parsing cart from localStorage:", error);
      }
    }
  };
  useEffect(() => {
    syncCartWithLocalStorage();
    const intervalId = setInterval(syncCartWithLocalStorage, 1000);
    return () => clearInterval(intervalId);
  }, []);
  const addToCart = (book) => {
    setCartItems((prevCartItems) => {
      const existingItem = prevCartItems.find((item) => item.id === book.id);
      if (existingItem) {
        const updatedCart = prevCartItems.map((item) =>
          item.id === book.id ? { ...item, quantity: item.quantity + 1 } : item,
        );
        return updatedCart;
      } else {
        const updatedCart = [...prevCartItems, { ...book, quantity: 1 }];
        setTimeout(() => {
          const itemAddedEvent = new CustomEvent("itemAddedToCart", {
            detail: { item: book },
          });
          window.dispatchEvent(itemAddedEvent);
          window.dispatchEvent(new CustomEvent("cartUpdated"));
          window.dispatchEvent(new CustomEvent("openCart"));
        }, 10);
        return updatedCart;
      }
    });
  };
  const removeFromCart = (id) => {
    setCartItems(cartItems.filter((item) => item.id !== id));
    const updatedCart = cartItems.filter((item) => item.id !== id);
    localStorage.setItem("patentpionner_cart", JSON.stringify(updatedCart));
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent("cartUpdated"));
    }, 10);
  };
  const [isClearing, setIsClearing] = useState(false);
  const clearCart = () => {
    if (isClearing) {
      return;
    }
    setIsClearing(true);
    try {
      localStorage.setItem("patentpionner_cart", JSON.stringify([]));
      setCartItems([]);
      window.dispatchEvent(new CustomEvent("cartUpdated"));
      setTimeout(() => {
        setIsClearing(false);
      }, 300);
    } catch (error) {
      // console.error("Error in clearCart:", error);
      localStorage.setItem("patentpionner_cart", JSON.stringify([]));
      setIsClearing(false);
    }
  };
  return (
    <ThemeProvider theme={theme}>
      <AuthProvider>
        <ToastProvider>
          <Router>
            <Routes>
              <Route
                element={
                  <LandingLayout
                    cartItems={cartItems}
                    removeFromCart={removeFromCart}
                    addToCart={addToCart}
                    clearCart={clearCart}
                  />
                }
              >
                <Route path="/" element={<Home />} />
                <Route
                  path="/book"
                  element={<Shop addToCart={addToCart} cartItems={cartItems} />}
                />
                <Route path="/contact" element={<Contact />} />
                <Route path="/insights" element={<Insights />} />
                <Route path="/about" element={<About />} />
                <Route path="/login" element={<LoginLanding />} />
                <Route path="/signup" element={<Signup />} />
                <Route path="/verify" element={<VerificationPage />} />
                <Route path="/verify-email" element={<VerifyEmailPage />} />
                <Route
                  path="/verify-email/:token"
                  element={<VerifyEmailRedirect />}
                />
                <Route path="/course" element={<Course />} />
                <Route path="/patent-museum" element={<PatentMuseum />} />
                <Route path="/faq" element={<Faq />} />

                <Route path="/course-detail/:id" element={<CourseDetail />} />
                <Route path="/course-detail" element={<CourseDetail />} />
              </Route>
              <Route path="/login" element={<LoginLanding />} />
              <Route path="/forgot-password" element={<ForgotPassword />} />
              <Route path="/reset-new-password" element={<ResetPassword />} />
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <Dashboard />
                    </Layout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/courses"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <CourseList />
                    </Layout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/courses/my"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <MyCourses />
                    </Layout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/courses/:id"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <Courses />
                    </Layout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/courses/new"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <CourseForm />
                    </Layout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/courses/:id/edit"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <CourseForm />
                    </Layout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/users"
                element={
                  <AdminRoute>
                    <Layout>
                      <UserList />
                    </Layout>
                  </AdminRoute>
                }
              />
              {}
              <Route
                path="/books"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <BookList />
                    </Layout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/books/my"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <MyBooks />
                    </Layout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/books/new"
                element={
                  <AdminRoute>
                    <Layout>
                      <NewBookPage />
                    </Layout>
                  </AdminRoute>
                }
              />
              <Route
                path="/books/:id"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <BookDetailPage />
                    </Layout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/books/:id/edit"
                element={
                  <AdminRoute>
                    <Layout>
                      <EditBookPage />
                    </Layout>
                  </AdminRoute>
                }
              />
              <Route
                path="/purchases"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <PurchaseList />
                    </Layout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/shipment"
                element={
                  <AdminRoute>
                    <Layout>
                      <AdminShipment />
                    </Layout>
                  </AdminRoute>
                }
              />
              <Route
                path="/my-shipments"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <MyShipments />
                    </Layout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/profile"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <Profile />
                    </Layout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="admin/insights"
                element={
                  <AdminRoute>
                    <Layout>
                      <InsightsList />
                    </Layout>
                  </AdminRoute>
                }
              />
              <Route
                path="/insights/new"
                element={
                  <AdminRoute>
                    <Layout>
                      <InsightForm />
                    </Layout>
                  </AdminRoute>
                }
              />
              <Route
                path="/admin/insights/:id"
                element={
                  <AdminRoute>
                    <Layout>
                      <InsightView />
                    </Layout>
                  </AdminRoute>
                }
              />
              <Route
                path="/insights/:id/edit"
                element={
                  <AdminRoute>
                    <Layout>
                      <InsightForm />
                    </Layout>
                  </AdminRoute>
                }
              />
            </Routes>
          </Router>
        </ToastProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}
export default App;
