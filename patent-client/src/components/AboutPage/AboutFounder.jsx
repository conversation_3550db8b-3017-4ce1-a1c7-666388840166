import React from "react";
import Founder from "../../assets/Founder.png";
import <PERSON> from "../../assets/Arrow.png";
import Linkedin from "../../assets/linkedin.svg";
import Mail from "../../assets/mail.svg";
import Phone from "../../assets/phone.svg";

const AboutFounder = () => {
  return (
    <section className="py-12 md:py-16 lg:py-20 container mx-auto px-4 md:px-4 lg:px-0">
      {/* Changed lg:flex-row to xl:flex-row to keep column layout at 1024px */}
      <div className="flex flex-col xl:flex-row items-center gap-2 md:gap-16 lg:gap-10">
        {/* Text content */}
        <div className="w-full xl:w-[742px] text-center xl:text-left relative">
          <div className="hidden xl:block absolute h-full">
            <img
              src={Arrow || "/placeholder.svg"}
              alt=""
              className="w-[250px] md:w-[250px] lg:w-[280px] ml-[380px] md:ml-[320px] lg:ml-[465px]"
            />
          </div>
          <div className="w-full xl:w-[742px]">
            <p className="text-gray-500 mb-2">About Founder</p>
            <h3 className="text-3xl md:text-4xl font-bold mb-3 text-gray-900">
              Emmanuel Coffy, Esq.
            </h3>
            <div className="flex justify-center xl:justify-start gap-3 mb-6">
              <a
                href="#"
                aria-label="LinkedIn"
                className="text-[#009F9F] hover:text-[#018A8A]"
              >
                <img src={Linkedin || "/placeholder.svg"} alt="" />
              </a>
              <a
                href="mailto:<EMAIL>"
                aria-label="Email"
                className="hover:text-[#018A8A]"
              >
                <img src={Mail || "/placeholder.svg"} alt="" />
              </a>
              <a
                href="tel:+1234567890"
                aria-label="Phone"
                className="text-[#009F9F] hover:text-[#018A8A]"
              >
                <img src={Phone || "/placeholder.svg"} alt="" />
              </a>
            </div>
            <p className="text-gray-700 mb-4 leading-relaxed">
              Emmanuel Coffy, Esq., is a distinguished patent attorney, expert
              witness, and strategic IP advisor. With a rich background as a
              former U.S. patent examiner and digital design engineer, he
              leverages his extensive expertise to empower businesses to
              maximize the value of their IP portfolios.
            </p>
            <p className="text-gray-700 leading-relaxed">
              Emmanuel's tailored IP strategies and authoritative insights make
              him a trusted advisor for startups and Fortune 500 companies
              alike, ensuring that their intellectual property assets are
              well-protected and aligned with their business objectives.
            </p>
          </div>
        </div>

        {/* Image */}
        <div className="w-full flex justify-center xl:justify-end mt-8 xl:mt-0">
          <div className="w-[280px] h-[350px] sm:w-[350px] sm:h-[450px] md:w-[420px] md:h-[520px] lg:w-[489px] lg:h-[620px] relative">
            <img
              src={Founder || "/placeholder.svg"}
              alt="Emmanuel Coffy, Esq."
              className="w-full h-full object-cover object-center rounded-full"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutFounder;
