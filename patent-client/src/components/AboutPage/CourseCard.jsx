import { Building, Clock, BadgeCheck, ShoppingCart, Check } from "lucide-react";
import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import { useToast } from "../../contexts/ToastContext";
import { API_BASE_URL } from "../../config/config";
const CourseCard = ({
  course,
  showBuyButton = true,
  isOwned: propIsOwned = false,
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { addToast } = useToast();
  const [isInCart, setIsInCart] = useState(false);
  const [isOwned, setIsOwned] = useState(propIsOwned);
  useEffect(() => {
    setIsOwned(propIsOwned);
  }, [propIsOwned]);
  useEffect(() => {
    const checkCartStatus = () => {
      const cart = JSON.parse(
        localStorage.getItem("patentpionner_cart") || "[]",
      );
      const inCart = cart.some(
        (item) => item.id === course.id && item.type === "course",
      );
      setIsInCart(inCart);
    };
    checkCartStatus();
    const handleCartUpdate = () => {
      checkCartStatus();
    };
    window.addEventListener("itemAddedToCart", handleCartUpdate);
    window.addEventListener("cartUpdated", handleCartUpdate);
    window.addEventListener("storage", handleCartUpdate);
    return () => {
      window.removeEventListener("itemAddedToCart", handleCartUpdate);
      window.removeEventListener("cartUpdated", handleCartUpdate);
      window.removeEventListener("storage", handleCartUpdate);
    };
  }, [course.id]);
  useEffect(() => {
    const handlePurchaseSuccess = () => {
      const checkOwnership = async () => {
        if (!user) return;
        try {
          const response = await fetch(`${API_BASE_URL}/purchases/my`, {
            headers: {
              Authorization: `Bearer ${user.accessToken || user.token}`,
            },
          });
          if (response.ok) {
            const data = await response.json();
            const purchases = Array.isArray(data) ? data : data.purchases || [];
            const owned = purchases.some((purchase) => {
              const purchaseCourseId =
                purchase.courseId && typeof purchase.courseId === "object"
                  ? purchase.courseId._id
                  : purchase.courseId;
              return purchaseCourseId === course.id;
            });
            setIsOwned(owned);
          }
        } catch (err) {
          console.error("Error checking course ownership:", err);
        }
      };
      checkOwnership();
    };
    window.addEventListener("purchaseSuccess", handlePurchaseSuccess);
    return () =>
      window.removeEventListener("purchaseSuccess", handlePurchaseSuccess);
  }, [user, course.id]);
  if (course.type === "cta") {
    return (
      <div className="bg-[#009F9F] rounded-lg p-4 sm:p-5 md:p-6 flex flex-col justify-center items-center text-center text-white h-full">
        <h3 className="text-xl sm:text-2xl font-bold mb-4 sm:mb-6">
          {course.title.toUpperCase()}
        </h3>
        <Link
          to={course.link || "#"}
          className="bg-white text-[#009F9F] font-semibold py-2 px-4 sm:py-3 sm:px-6 rounded-md hover:bg-gray-100 transition duration-300 text-sm sm:text-base"
        >
          {course.buttonText || "Learn More"}
        </Link>
      </div>
    );
  }
  const handleBuyNow = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!user) {
      localStorage.setItem("pending_purchase_course", JSON.stringify(course));
      const cartItem = {
        id: course.id,
        title: course.title,
        price: course.price,
        type: "course",
        quantity: 1,
        imageUrl: course.imageUrl,
      };
      const existingCart = JSON.parse(
        localStorage.getItem("patentpionner_cart") || "[]",
      );
      const itemExists = existingCart.some(
        (item) => item.id === cartItem.id && item.type === "course",
      );
      if (!itemExists) {
        const updatedCart = [...existingCart, cartItem];
        localStorage.setItem("patentpionner_cart", JSON.stringify(updatedCart));
      //   setTimeout(() => {
      //     window.dispatchEvent(
      //       new CustomEvent("itemAddedToCart", {
      //         detail: { item: cartItem },
      //       }),
      //     );
      //     window.dispatchEvent(new CustomEvent("cartUpdated"));
      //   }, 30);
      }
      addToast(
        "Please register or login first to purchase this course",
        "info",
      );
      // navigate("/signup");
      return;
    }
    const cartItem = {
      id: course.id,
      title: course.title,
      price: course.price,
      type: "course",
      quantity: 1,
      imageUrl: course.imageUrl,
    };
    const existingCart = JSON.parse(
      localStorage.getItem("patentpionner_cart") || "[]",
    );
    const itemExists = existingCart.some(
      (item) => item.id === cartItem.id && item.type === "course",
    );
    if (!itemExists) {
      const updatedCart = [...existingCart, cartItem];
      localStorage.setItem("patentpionner_cart", JSON.stringify(updatedCart));
      // setTimeout(() => {
      //   const itemAddedEvent = new CustomEvent("itemAddedToCart", {
      //     detail: { item: cartItem },
      //   });
      //   window.dispatchEvent(itemAddedEvent);
      //   window.dispatchEvent(new CustomEvent("cartUpdated"));
      // }, 30);
      window.dispatchEvent(new CustomEvent("openCart"));
      setIsInCart(true);
    } else {
      addToast("This course is already in your cart", "info");
      window.dispatchEvent(new CustomEvent("openCart"));
    }
  };
  return (
    <div className="bg-[#F7F7F8] rounded-lg p-4 sm:p-5 md:p-6 h-full flex flex-col shadow-sm hover:shadow-md transition-shadow duration-300">
      <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-3">{course.title}</h3>

      <div className="flex flex-wrap gap-2 sm:gap-4 mb-3 sm:mb-4">
        <div className="flex flex-wrap gap-2 sm:gap-3">
          <div className="flex items-center gap-1 text-gray-600 text-xs sm:text-sm">
            <Building size={16} className="min-w-4" />
            <span>{course.level || "Beginner"}</span>
          </div>
          
          {(course.hours > 0 || course.duration) && (
            <div className="flex items-center gap-1 text-gray-600 text-xs sm:text-sm">
              <Clock size={16} className="min-w-4" />
              <span>
                {course.hours
                  ? `${course.hours} ${course.hours === 1 ? "Hour" : "Hours"}`
                  : course.duration || ""}
              </span>
            </div>
          )}
          {(course.certificate === true || course.certificate === "true") && (
            <div className="flex items-center gap-1 text-gray-600 text-xs sm:text-sm">
              <BadgeCheck size={16} className="min-w-4" />
              <span>Certificate</span>
            </div>
          )}
        </div>
      </div>
      <p className="text-gray-700 text-xs sm:text-sm mb-4 sm:mb-6 flex-grow line-clamp-3">
        {course.description}
      </p>
      <div className="mt-auto">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-0">
          {course.price !== undefined && (
            <div className="text-base sm:text-lg font-bold text-[#009F9F]">
              $
              {typeof course.price === "number"
                ? course.price.toFixed(2)
                : course.price}
            </div>
          )}
          <div className="flex flex-wrap gap-2 sm:gap-3 items-center w-full sm:w-auto justify-start sm:justify-end">
            {isOwned && (
              <span className="bg-green-100 text-green-800 text-xs font-medium p-1 rounded flex items-center">
                <Check size={14} />
              </span>
            )}
            <Link
              to={course.link || "#"}
              className="text-[#009F9F] text-xs sm:text-sm font-semibold hover:text-[#018A8A]"
            >
              View Course
            </Link>
            {showBuyButton &&
              course.price !== undefined &&
              (isInCart ? (
                <button
                  onClick={() =>
                    window.dispatchEvent(new CustomEvent("openCart"))
                  }
                  className="bg-blue-500 text-white px-2 sm:px-3 py-1 rounded-md flex items-center gap-1 hover:bg-blue-600 transition-colors text-xs sm:text-sm"
                >
                  <Check size={14} />
                  In Cart
                </button>
              ) : (
                <button
                  onClick={handleBuyNow}
                  className="bg-[#009F9F] text-white px-2 sm:px-3 py-1 rounded-md flex items-center gap-1 hover:bg-[#018A8A] transition-colors text-xs sm:text-sm"
                >
                  <ShoppingCart size={14} />
                  Buy Now
                </button>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
};
export default CourseCard;
