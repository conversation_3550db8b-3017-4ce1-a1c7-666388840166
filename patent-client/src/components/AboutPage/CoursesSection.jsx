import { useState, useEffect } from "react";
import CourseCard from "./CourseCard";
import { useToast } from "../../contexts/ToastContext";
import { API_BASE_URL } from "../../config/config";
// Keep the CTA card for use with dynamic data
export const ctaCard = {
  title: "Ready to Take the Next Step Toward Patent Bar Success?",
  level: "",
  duration: "",
  certificate: false,
  description: "",
  link: "/course-detail",
  type: "cta",
  buttonText: "Claim Your Spot Today",
};

// Fallback static courses in case API fails
export const staticCourses = [
  {
    title: "Patent Bar Exam Bootcamp",
    level: "Beginner",
    duration: "35 Hours",
    certificate: true,
    description:
      "A fast-track program to help you pass the USPTO Patent Bar Exam, featuring focused lessons, practice questions, and test-day strategies.",
    link: "#",
    type: "course",
  },
  {
    title: "MPEP Mastery Course",
    level: "Beginner",
    duration: "35 Hours",
    certificate: true,
    description:
      "Master the MPEP with walkthroughs, keyword navigation, and real examples to quickly apply the right rules during the exam.",
    link: "#",
    type: "course",
  },
  {
    title: "USPTO Rules & Procedures 101",
    level: "Beginner",
    duration: "35 Hours",
    certificate: true,
    description:
      "Gain a solid understanding of patent law with essential USPTO procedures, including filing, prosecution, and appeals, explained clearly.",
    link: "#",
    type: "course",
  },
  ctaCard,
  {
    title: "Exam Strategy & Question Tactics",
    level: "Beginner",
    duration: "35 Hours",
    certificate: true,
    description:
      "Discover strategies for tackling multiple-choice questions, eliminating wrong options, and optimizing your exam time with practice sets.",
    link: "#",
    type: "course",
  },
];

const CoursesSection = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  // We still need setError for error handling
  const [, setError] = useState(null);
  const { addToast } = useToast();
 
  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${API_BASE_URL}/courses`);

        if (!response.ok) {
          throw new Error(`Failed to fetch courses: ${response.status}`);
        }

        const responseData = await response.json();
        const data = Array.isArray(responseData)
          ? responseData
          : responseData && responseData.courses
            ? responseData.courses
            : [];

        // Format courses for display
        const formattedCourses = data.map((course) => ({
          id: course._id,
          title: course.title,
          level: course.level || "Beginner",
          hours: course.hours || 0,
          duration: course.hours
            ? `${course.hours} ${course.hours === 1 ? "Hour" : "Hours"}`
            : "35 Hours",
          certificate: course.certificate !== undefined ? course.certificate : true,
          description: course.description,
          link: `/course-detail/${course._id}`,
          type: "course",
          price: course.price,
          imageUrl: course.image
        }));

        // Insert CTA card after the third course or at the end if fewer courses
        if (formattedCourses.length >= 3) {
          formattedCourses.splice(3, 0, ctaCard);
        } else {
          formattedCourses.push(ctaCard);
        }

        setCourses(formattedCourses);
        setError(null);
      } catch (err) {
        console.error("Error fetching courses:", err);
        setError("Failed to load courses");
        setCourses(staticCourses);
        addToast("Using demo courses due to connection issues", "warning");
      } finally {
        setLoading(false);
      }
    };

    fetchCourses();
  }, [addToast]);

  return (
    <section className="bg-[#242B2B] py-8 sm:py-10 md:py-12 px-4">
      <div className="container mx-auto">
        <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-2 sm:mb-3 md:mb-4 text-center md:text-left">
          Start mastering your craft
        </h2>
        <p className="text-gray-300 mb-8 sm:mb-10 md:mb-12 text-center md:text-left text-sm sm:text-base">
          Gain the skills, strategy, and confidence to pass the Patent Bar and
          thrive in the field.
        </p>

        {loading ? (
          <div className="flex justify-center items-center py-8 sm:py-10 md:py-12">
            <div className="inline-block animate-spin rounded-full h-6 w-6 sm:h-7 sm:w-7 md:h-8 md:w-8 border-t-2 border-b-2 border-white mr-2"></div>
            <span className="text-white text-sm sm:text-base">Loading courses...</span>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-5">
            {courses.map((course, index) => (
              <CourseCard
                key={course.id || index}
                course={course}
                showBuyButton={course.type !== "cta"}
              />
            ))}
          </div>
        )}
      </div>
    </section>
  );
};
export default CoursesSection;
