import React from "react";
import { FilePenLine, ListChecks, Target, CircleDot } from "lucide-react";
const goals = [
  { icon: FilePenLine, text: "Understand key USPTO rules and procedures." },
  { icon: ListChecks, text: "Apply knowledge to realistic patent scenarios." },
  { icon: Target, text: "Use effective strategies for exam success." },
  { icon: CircleDot, text: "Feel confident and ready for the next step." },
];
const GoalsSection = () => {
  return (
    <section className="container mx-auto  py-12 px-4 md:px-4 lg:px-0">
      {}
      <h2 className="text-3xl font-bold mb-2">Our Goal</h2>
      <p className="text-gray-600 mb-8">
        By the end of these courses, you'll be able to...
      </p>
      {}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {goals.map((goal, index) => (
          <div key={index} className="bg-gray-50 p-6 rounded-lg">
            <div className="w-16 h-16 bg-[#009F9F] rounded-lg flex items-center justify-center mb-6">
              <goal.icon className="w-8 h-8 text-white" />
            </div>
            <p className="font-medium">{goal.text}</p>
          </div>
        ))}
      </div>
    </section>
  );
};
export default GoalsSection;
