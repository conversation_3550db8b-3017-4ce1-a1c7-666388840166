import React from "react";
import TestimonialImage from "../../assets/TestimonialImg.png";
const testimonial = {
  name: "<PERSON>",
  image: TestimonialImage,
  rating: 5,
  text: "I had a fantastic experience with <PERSON> when he drafted my patent application. It was approved in no time! Fast forward five years and I faced an office action on a different application. I immediately reached out to <PERSON> again to handle the response, and I knew he would deliver exceptional work—and he certainly did!",
};
const TestimonialCard = () => {
  return (
    <div className="bg-white p-6 rounded-lg border border-gray-100 shadow-sm">
      <div className="flex items-center mb-4">
        <div className="w-10 h-10 rounded-full bg-gray-200 overflow-hidden mr-3">
          <img
            src={testimonial.image}
            alt={testimonial.name}
            className="w-full h-full object-cover"
          />
        </div>
        <div>
          <p className="font-medium">{testimonial.name}</p>
          <div className="flex text-[#009F9F] mt-1">
            {[...Array(testimonial.rating)].map((_, i) => (
              <span key={i} className="text-lg">
                ★
              </span>
            ))}
          </div>
        </div>
      </div>
      <p className="text-sm text-gray-700">{testimonial.text}</p>
    </div>
  );
};
export default TestimonialCard;
