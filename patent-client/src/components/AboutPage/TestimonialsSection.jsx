import React from "react";
import TestimonialCard from "./TestimonialCard";
import { Button } from "../../styles/StyledComponents";
const testimonials = [1, 2, 3, 4];
const TestimonialsSection = ({ color, textcolor, hideButton }) => {
  return (
    <section
      className={`m-auto ${color ? `bg-${color}` : "bg-[#F7F7F8]"} py-12 px-4 md:px-4 lg:px-0`}
    >
      <h2
        className={`text-2xl sm:text-3xl md:text-4xl lg:text-[40px] font-bold ${textcolor ? `text-${textcolor}` : "text-[#000000]"} px-4 md:px-4 lg:px-0 text-center mb-6`}
      >
        Here are some kind words from our <br className="hidden sm:inline" />{" "}
        students around the world
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {testimonials.map((item) => (
          <TestimonialCard key={item} />
        ))}
      </div>
      {!hideButton && (
        <div className="flex justify-center mt-10">
          <button className="bg-[#009F9F] hover:bg-[#018A8A] text-white p-[16px] rounded">
            Discover Our Courses
          </button>
        </div>
      )}
    </section>
  );
};
export default TestimonialsSection;
