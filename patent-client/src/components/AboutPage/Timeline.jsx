import { useEffect, useRef, useState } from "react";
import { GraduationCap, Users, Globe, BookOpen, Rocket } from "lucide-react";
const timelineData = [
  {
    year: "2021",
    title: "First Success",
    description:
      "Launched our flagship USPTO Exam Prep Course. Over 200 students successfully completed the program, setting a strong foundation for future growth.",
    icon: GraduationCap,
    position: "right",
  },
  {
    year: "2022",
    title: "Community Growth",
    description:
      "Built a thriving community of 1,500+ learners. Introduced free resources, webinars, and patent drafting workshops to widen access.",
    icon: Users,
    position: "left",
  },
  {
    year: "2023",
    title: "Recognized Excellence",
    description:
      "Achieved an 87% USPTO exam pass rate. Expanded internationally, supporting students across 15+ countries.",
    icon: Globe,
    position: "right",
  },
  {
    year: "2024",
    title: "Innovation Leap",
    description:
      "Introduced AI-driven study tools, micro-courses on Patent Searching and Patent Drafting, and launched a mentorship program with industry experts.",
    icon: BookOpen,
    position: "left",
  },
  {
    year: "2025",
    title: "Leading the Future",
    description:
      "With a growing global presence and continuous innovation, Patent Pioneer is on a mission to become the most trusted platform for patent education worldwide.",
    icon: Rocket,
    position: "right",
  },
];
export default function Timeline() {
  const [activeIndex, setActiveIndex] = useState(-1);
  const timelineRefs = useRef([]);
  const containerRef = useRef(null);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);
    };
    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => {
      window.removeEventListener("resize", checkScreenSize);
    };
  }, []);
  useEffect(() => {
    const options = {
      root: null,
      rootMargin: "0px",
      threshold: 0.5,
    };
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        const index = timelineRefs.current.findIndex(
          (ref) => ref === entry.target,
        );
        if (entry.isIntersecting && index > activeIndex) {
          setActiveIndex(index);
        }
      });
    }, options);
    timelineRefs.current.forEach((ref) => {
      if (ref) observer.observe(ref);
    });
    return () => {
      timelineRefs.current.forEach((ref) => {
        if (ref) observer.unobserve(ref);
      });
    };
  }, [activeIndex]);
  return (
    <div
      ref={containerRef}
      className="bg-[#F7F7F8] py-12 sm:py-16 md:py-24 px-4 sm:px-6"
    >
      <div className="container mx-auto">
        {}
        <div className="mb-12 sm:mb-16 text-center">
          <h2 className="text-2xl sm:text-3xl md:text-[32px] font-bold text-[#000000] mb-2">
            The Patent Pioneer Story
          </h2>
          <p className="text-base sm:text-lg md:text-[20px] text-[#54585C]">
            Milestones that mark our commitment to shaping the future <br/> of patent
            professionals.
          </p>
        </div>
        {}
        <div className="relative">
          {}
          <div className="absolute left-4 md:left-1/2 transform md:-translate-x-1/2 h-[calc(100%-1rem)] md:h-[1420px] w-0.5 bg-gray-200">
            {}
            <div
              className="absolute top-0 left-0 w-full bg-[#009F9F] transition-all duration-1000 ease-out"
              style={{
                height:
                  activeIndex >= 0
                    ? `${((activeIndex + 1) / timelineData.length) * 100}%`
                    : "0%",
              }}
            />
          </div>
          {}
          <div className="relative">
            {timelineData.map((item, index) => {
              const isActive = index <= activeIndex;
              const IconComponent = item.icon;
              return (
                <div
                  key={item.year}
                  ref={(el) => (timelineRefs.current[index] = el)}
                  className={`relative mb-16 sm:mb-20 md:mb-24 ${isMobile ? "ml-8" : ""}`}
                >
                  {}
                  <div
                    className={`absolute ${
                      isMobile
                        ? "left-[-20px] top-0"
                        : item.position === "left"
                          ? "right-1/2"
                          : "left-1/2"
                    } z-10`}
                  >
                    <div
                      className={`px-4 sm:px-7 py-1.5 sm:py-2 rounded text-xs font-semibold transition-colors duration-500 ${
                        isActive
                          ? "bg-[#009F9F] text-white"
                          : "bg-gray-300 text-gray-600"
                      }`}
                    >
                      {item.year}
                    </div>
                  </div>
                  {}
                  {isMobile ? (
                    <div className="mt-10 pl-12 mb-6">
                      <div
                        className={`transition-colors duration-500 ${isActive ? "text-[#009F9F]" : "text-gray-400"} mb-3`}
                      >
                        <IconComponent className="w-8 h-8" />
                      </div>
                      <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2">
                        {item.title}
                      </h3>
                      <p className="text-sm sm:text-base text-gray-600">
                        {item.description}
                      </p>
                    </div>
                  ) : (
                    <div
                      className={`flex items-start ${item.position === "left" ? "flex-row-reverse" : ""}`}
                    >
                      {}
                      <div
                        className={`w-1/2 ${isTablet ? "pt-16" : "pt-24"} ${
                          item.position === "left"
                            ? "pl-1 sm:pl-2 md:pl-3"
                            : "pr-1 sm:pr-2 md:pr-3"
                        } flex ${
                          item.position === "left"
                            ? "justify-start"
                            : "justify-end"
                        }`}
                      >
                        <div
                          className={`transition-colors duration-500 ${isActive ? "text-[#009F9F]" : "text-gray-400"}`}
                        >
                          <IconComponent
                            className={`${isTablet ? "w-12 h-12" : "w-10 h-12 sm:w-15 sm:h-12 md:w-[100px] md:h-[100px]"}`}
                          />
                        </div>
                      </div>
                      {}
                      <div
                        className={`w-1/2 ${isTablet ? "pt-16" : "pt-24"} ${
                          item.position === "left"
                            ? "pr-1 sm:pr-2 md:pr-3 text-right"
                            : "pl-1 sm:pl-2 md:pl-3"
                        }`}
                      >
                        <div
                          className={`transition-opacity duration-500 ${isActive ? "opacity-100" : "opacity-50"}`}
                        >
                          <h3
                            className={`${isTablet ? "text-lg" : "text-lg sm:text-xl"} font-semibold text-gray-900 mb-2`}
                          >
                            {item.title}
                          </h3>
                          <p className="text-sm sm:text-base text-gray-600">
                            {item.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
