// src/components/BlogCard.jsx
import React from "react";
import { Link } from "react-router-dom";
const BlogCard = ({ image, title, description, date, link }) => {
  return (
    <Link
      to={link}
      className="group bg-white rounded-lg overflow-hidden border border-gray-300 transform transition-all duration-300 ease-in-out hover:shadow-lg hover:scale-105 "
    >
      {}
      <div className="relative m-4">
        <img
          src={image}
          alt={title}
          className="w-full h-[200px] object-cover  rounded-md group-hover:opacity-80 transition-all duration-300"
        />
      </div>
      {}
      <div className="p-4">
        <h3 className="text-xl font-bold text-left text-gray-800 line-clamp-2 group-hover:text-[#009F9F] transition-all duration-300">
          {title}
        </h3>
        <p className="text-md text-left text-gray-600 my-2 line-clamp-2 group-hover:text-gray-700 transition-all duration-300">
          {description}
        </p>
        <p className="text-sm text-left text-gray-500">{date}</p>
      </div>
    </Link>
  );
};
export default BlogCard;
