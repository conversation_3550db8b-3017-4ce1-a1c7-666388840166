// src/components/BlogSection.jsx
import React, { useState, useEffect } from "react";
import Blog<PERSON>ard from "./BlogCard";
import Button from "./Button";
import { useNavigate } from "react-router-dom";
import { fetchBlogs } from "../api/blogs";
const BlogSection = ({ blogpage = false, heading, description }) => {
  const [blogs, setBlogs] = useState([]);
  const [currentBlogs, setCurrentBlogs] = useState(blogpage ? 6 : 3);
  const [isShowingAll, setIsShowingAll] = useState(false);
  const navigate = useNavigate();
  useEffect(() => {
    const getBlogs = async () => {
      const fetchedBlogs = await fetchBlogs();
      setBlogs(fetchedBlogs);
    };
    getBlogs();
  }, []);
  const handleLoadMore = () => {
    if (isShowingAll) {
      setCurrentBlogs(6);
    } else {
      setCurrentBlogs(blogs.length);
    }
    setIsShowingAll(!isShowingAll);
  };
  const handleShowAll = () => {
    navigate("/insights");
  };
  return (
    <section className="bg-gray-100 py-20 px-4">
      <div className="container  h-full mx-auto text-center">
        {}
        {heading && description && (
          <div>
            <h2 className="text-3xl md:text-4xl font-bold mb-8">{heading}</h2>
            <p className="text-gray-600 mb-12">{description}</p>
          </div>
        )}
        {}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {blogs.slice(0, currentBlogs).map((blog, index) => (
            <BlogCard
              key={index}
              image={blog.image}
              title={blog.title}
              description={blog.description}
              date={blog.date}
            />
          ))}
        </div>
        {}
        <div className="mt-6">
          {blogpage ? (
            <Button
              text={isShowingAll ? "Show Less" : "Load More"}
              secondary
              onClick={handleLoadMore}
            />
          ) : (
            <Button text="View All" onClick={handleShowAll} />
          )}
        </div>
      </div>
    </section>
  );
};
export default BlogSection;
