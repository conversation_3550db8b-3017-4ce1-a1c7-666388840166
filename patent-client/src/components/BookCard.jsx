// src/components/BookCard.jsx
import React, { useState } from "react";
import { ShoppingCart, Check } from "lucide-react";
import BookDetailModal from "./BookDetailModal";
import { useToast } from "../contexts/ToastContext";
const BookCard = ({ book, addToCart, isOwned = false, onPurchaseError }) => {
  const [showModal, setShowModal] = useState(false);
  const { addToast } = useToast();
  const handleShowModal = () => {
    setShowModal(true);
  };
  const handleCloseModal = () => {
    setShowModal(false);
  };
  const handleAddToCart = () => {
    try {
      const cartItems = JSON.parse(localStorage.getItem('patentpionner_cart') || '[]');
      const isInCart = cartItems.some(item => item.id === book.id);
      
      if (isInCart) {
        addToast(`${book.title} is already in your cart`, "info");
        setShowModal(false);
        return;
      }
      addToCart(book);
      addToast(`${book.title} added to cart`, "success");
    } catch (error) {
      if (onPurchaseError) {
        onPurchaseError(error, book.title);
      } else {
        addToast(`Error adding to cart: ${error.message}`, "error");
      }
    }
  };
  return (
    <>
      <div
        className="border w-full sm:w-full md:w-full border-gray-300 rounded-lg overflow-hidden hover:shadow-lg transform transition-all duration-300 ease-in-out hover:scale-105 cursor-pointer flex flex-col h-auto min-h-[380px] sm:min-h-[400px] md:min-h-[421px]"
        onClick={handleShowModal}
      >
        <div className="relative m-4 rounded-lg">
          <div className="bg-gray-100 w-full rounded-md flex justify-center items-center p-4 h-[180px] sm:h-[200px] md:h-[234px]">
            <img
              src={book.image}
              alt={book.title}
              className="w-auto max-w-full h-[140px] sm:h-[160px] md:h-[190px] object-contain"
            />
          </div>
          <div className="absolute bottom-4 right-2 flex gap-2">
            {isOwned && (
              <div
                className="p-2 rounded-full bg-green-600 cursor-default"
                onClick={(e) => {
                  e.stopPropagation();
                  addToast(`You already own "${book.title}"`, "info");
                }}
              >
                <Check className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
              </div>
            )}
            <div
              className="p-2 rounded-full bg-[#009F9F] hover:bg-[#018A8A] cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                handleAddToCart();
              }}
            >
              <ShoppingCart className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
            </div>
          </div>
        </div>
        <div className="p-4 flex-grow flex flex-col ">
          <div>
            <p className="text-base sm:text-lg md:text-[24px] font-semibold text-[#009F9F]">${book.price}</p>
            <h3 className="text-xs sm:text-sm md:text-[18px]  font-bold line-clamp-1 mt-1">{book.title}</h3>
          </div>
          <p className="text-xs sm:text-sm text-[#54585C] md:text-[16px] line-clamp-2 mt-2">
            {book.description}
          </p>
        </div>
      </div>
      {showModal && (
        <BookDetailModal
          show={showModal}
          book={book}
          closeModal={handleCloseModal}
          isOwned={isOwned}
          addToCart={addToCart}
          onPurchaseError={onPurchaseError}
        />
      )}
    </>
  );
};
export default BookCard;
