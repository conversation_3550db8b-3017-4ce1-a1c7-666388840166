import React, { useEffect, useState, useRef } from "react";
import { useToast } from "../contexts/ToastContext";

const BookDetailModal = ({ show, book, closeModal, isOwned = false, addToCart, onPurchaseError }) => {
  const [isVisible, setIsVisible] = useState(false);
  const { addToast } = useToast();
  const modalRef = useRef(null);
  
  useEffect(() => {
    if (show) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  }, [show]);
  
  // Handle click outside to close modal
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        closeModal();
      }
    };
    
    // Add event listener when modal is visible
    if (isVisible) {
      document.addEventListener("mousedown", handleClickOutside);
    }
    
    // Clean up event listener
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isVisible, closeModal]);
  
  const handleAddToCart = () => {
    try {
      // Check if the book is already in the cart
      const cartItems = JSON.parse(localStorage.getItem('patentpionner_cart') || '[]');
      const isInCart = cartItems.some(item => item.id === book.id);
      
      if (isInCart) {
        addToast(`${book.title} is already in your cart`, "info");
        closeModal();
        return;
      }
      
      addToCart(book);
      addToast(`${book.title} added to cart`, "success");
      closeModal();
    } catch (error) {
      if (onPurchaseError) {
        onPurchaseError(error, book.title);
      } else {
        addToast(`Error adding to cart: ${error.message}`, "error");
      }
    }
  }
  
  const formatBookType = (type) => {
    if (!type) return "";
    
    if (type.toLowerCase() === "softcopy") {
      return "Soft Copy";
    } else if (type.toLowerCase() === "hardcopy") {
      return "Hard Copy";
    }
    
    return type;
  };
  
  return (
    <div
      className={`fixed inset-0 bg-black/50 z-[100] flex justify-center items-center p-4 overflow-y-auto transition-opacity duration-500 ${
        isVisible ? "opacity-100" : "opacity-0 pointer-events-none"
      }`}
    >
      <div
        ref={modalRef}
        className={`bg-white p-4 sm:p-6 md:p-8 rounded-lg shadow-lg w-full max-w-4xl transition-transform duration-500 ${
          isVisible ? "scale-100" : "scale-95"
        }`}
      >
        <div className="flex flex-col md:flex-row gap-4 md:gap-8">
          <div className="w-full md:w-96 h-full md:h-auto bg-gray-100 rounded-md flex justify-center items-center p-2 md:p-4">
            <img
              src={book.image}
              alt={book.title}
              className="max-h-60 md:h-72 w-full object-contain rounded-md"
            />
          </div>
          <div className="flex flex-col gap-4 md:gap-8 flex-1">
            <div>
              <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-2 md:mb-4">
                {book.title}
              </h3>
              <p className="text-sm md:text-md text-gray-700 mb-2 md:mb-4">{book.description}</p>
              <div className="flex flex-wrap items-center gap-2 md:gap-4">
                <p className="text-lg md:text-xl font-semibold text-[#009F9F]">
                  ${book.price}
                </p>
                <p className="bg-[#9f87001b] text-[#d09500] px-3 py-1 md:px-4 md:py-2 rounded-full text-xs md:text-sm">
                  {formatBookType(book.type)}
                </p>
              </div>
            </div>
            {isOwned ? (
              <button className="bg-green-600 text-white px-4 py-2 md:px-6 md:py-2 rounded-md w-full cursor-default">
                You Own This Book
              </button>
            ) : (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleAddToCart();
                }}
                className="bg-[#009F9F] text-white px-4 py-2 md:px-6 md:py-2 rounded-md w-full cursor-pointer"
              >
                Get A Copy Now
              </button>
            )}
          </div>
        </div>
        <button
          onClick={closeModal}
          className="absolute top-2 right-2 md:top-4 md:right-4 text-[#000000] hover:text-gray-800 cursor-pointer rounded-full w-8 h-8 flex items-center justify-center"
        >
          X
        </button>
      </div>
    </div>
  );
};

export default BookDetailModal;