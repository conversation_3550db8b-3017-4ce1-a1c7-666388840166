import React, { useEffect, useState } from "react";
import Button from "./Button";
import * as bookService from "../services/bookService";
import { useNavigate } from "react-router-dom";

const BookPromoSection = () => {
  const [promoBook, setPromoBook] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  let navigate = useNavigate();

  useEffect(() => {
    const fetchBook = async () => {
      try {
        setLoading(true);
        const response = await bookService.getAllBooks(1, 1);
        let book = null;
        if (Array.isArray(response)) {
          book = response[0];
        } else if (response && response.books && response.books.length > 0) {
          book = response.books[0];
        }
        setPromoBook(book);
        setError(null);
      } catch (err) {
        setError("Failed to load book for promotion.");
      } finally {
        setLoading(false);
      }
    };
    fetchBook();
  }, []);

  return (
    <section className="py-20 px-4 bg-white">
      <div className="container h-full mx-auto flex flex-col-reverse md:flex-row items-center justify-between gap-10">
        <div className="md:w-1/2 text-center md:text-left">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            {loading ? "Loading..." : error ? "Book" : promoBook ? promoBook.title : "Book"}
          </h2>
          <p className="text-gray-600 text-base mb-4">
            Explore <strong>our featured book</strong>, your essential guide to
            understanding and leveraging intellectual property. Whether you're an
            inventor, entrepreneur, or business owner, this book provides
            invaluable insights into protecting and maximizing the value of your
            ideas.
          </p>
          <p className="text-gray-800 font-semibold mb-6">
            Click below to learn more and purchase your copy today.
          </p>
          <Button onClick={() => navigate("/book")} text="Get A Copy Now">
            Get A Copy Now
          </Button>
        </div>
        <div className="md:w-1/2 flex justify-end items-center min-h-[320px]">
          {loading ? (
            <div className="text-gray-500">Loading...</div>
          ) : error ? (
            <div className="text-red-500">{error}</div>
          ) : promoBook && promoBook.image ? (
            <img
              src={promoBook.image}
              alt={promoBook.title}
              className="w-[220px] md:w-[300px] h-auto object-contain rounded-lg shadow"
            />
          ) : (
            <img
              src={require("../assets/Book.png")}
              alt="Book"
              className="w-[320px] md:w-[400px] h-auto object-contain rounded-lg shadow"
            />
          )}
        </div>
      </div>
    </section>
  );
};
export default BookPromoSection;
