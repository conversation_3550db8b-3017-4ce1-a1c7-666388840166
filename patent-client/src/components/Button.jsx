import React from "react";
import { Link } from "react-router-dom";
const Button = ({ text, secondary, onClick, to, disabled }) => {
  const baseStyles = `px-6 py-2 font-medium transition rounded-md duration-200 ease-in-out w-full md:w-auto relative z-10`;
  const primaryStyles = disabled
    ? "bg-gray-300 text-white cursor-not-allowed"
    : "bg-[#009F9F] text-white hover:bg-[#018A8A]";
  const secondaryStyles = disabled
    ? "bg-gray-100 text-gray-400 border border-gray-300 cursor-not-allowed"
    : "bg-white text-[#009F9F] border border-[#009F9F] hover:bg-cyan-50 hover:text-[#018A8A]";
  const classes = `${baseStyles} ${secondary ? secondaryStyles : primaryStyles}`;
  if (to) {
    return (
      <Link to={to}>
        <button className={classes} disabled={disabled}>
          {text}
        </button>
      </Link>
    );
  }
  return (
    <button onClick={onClick} disabled={disabled} className={classes}>
      {text}
    </button>
  );
};
export default Button;
