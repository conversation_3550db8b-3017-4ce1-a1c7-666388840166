// src/components/CartIcon.jsx
import React, { useState, useEffect, useRef } from "react";
import { LucideShoppingCart } from "lucide-react";
import CartModal from "./CartModal";
import { useToast } from "../contexts/ToastContext";
const toastTracker = {
  lastToastTime: 0,
  lastItemId: null,
  lastItemType: null,
  isToastActive: false,
};
const CartIcon = ({ cartItems, removeFromCart, clearCart }) => {
  const [showCart, setShowCart] = useState(false);
  const { addToast } = useToast();
  const toastTimeoutRef = useRef(null);
  const toggleCart = () => setShowCart(!showCart);
  useEffect(() => {
    const handleOpenCart = () => {
      setShowCart(true);
    };
    window.addEventListener("openCart", handleOpenCart);
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get("cart") === "open") {
      setShowCart(true);
    }
    return () => {
      window.removeEventListener("openCart", handleOpenCart);
    };
  }, []);
  useEffect(() => {
    const handleItemAdded = (event) => {
      const item = event.detail.item;
      const now = Date.now();
      if (
        toastTracker.isToastActive ||
        now - toastTracker.lastToastTime < 3000 ||
        (item.id === toastTracker.lastItemId &&
          item.type === toastTracker.lastItemType)
      ) {
        return;
      }
      toastTracker.lastToastTime = now;
      toastTracker.lastItemId = item.id;
      toastTracker.lastItemType = item.type;
      toastTracker.isToastActive = true;
      if (toastTimeoutRef.current) {
        clearTimeout(toastTimeoutRef.current);
      }
      addToast(`${item.title} added to cart`, "success");
      toastTimeoutRef.current = setTimeout(() => {
        toastTracker.isToastActive = false;
        toastTimeoutRef.current = null;
      }, 3000);
    };
    window.addEventListener("itemAddedToCart", handleItemAdded);
    return () => {
      window.removeEventListener("itemAddedToCart", handleItemAdded);
      if (toastTimeoutRef.current) {
        clearTimeout(toastTimeoutRef.current);
      }
    };
  }, [addToast]);
  useEffect(() => {
    const handleCartUpdated = () => {
      try {
        const savedCart = localStorage.getItem("patentpionner_cart");
        if (savedCart && savedCart === "[]" && cartItems.length > 0) {
          setShowCart(false);
        }
      } catch (error) {
        console.error("Error checking cart data:", error);
      }
    };
    window.addEventListener("cartUpdated", handleCartUpdated);
    return () => {
      window.removeEventListener("cartUpdated", handleCartUpdated);
    };
  }, [cartItems]);
  useEffect(() => {
    const handleCartCleared = () => {
      if (
        cartItems.length === 0 ||
        localStorage.getItem("patentpionner_cart") === "[]"
      ) {
        setShowCart(false);
      }
    };
    window.addEventListener("cartUpdated", handleCartCleared);
    return () => {
      window.removeEventListener("cartUpdated", handleCartCleared);
    };
  }, [cartItems]);
  return (
    <div>
      <button onClick={toggleCart} className="relative cursor-pointer">
        <LucideShoppingCart className="w-6 h-6 text-gray-700" />
        <span className="absolute top-0 right-0 bg-red-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
          {cartItems.length}
        </span>
      </button>
      {showCart && (
        <CartModal
          cartItems={cartItems}
          closeModal={toggleCart}
          removeFromCart={removeFromCart}
          clearCart={clearCart}
        />
      )}
    </div>
  );
};
export default CartIcon;
