import React from "react";
import { LucideTrash, LucideShoppingCart } from "lucide-react";
const CartItem = ({ item, removeFromCart }) => {
  return (
    <div className="flex items-center p-4 border rounded-lg border-gray-300">
      <div className="flex-shrink-0">
        {item.image || item.imageUrl ? (
          <img
            src={item.image || item.imageUrl}
            alt={item.title}
            className="w-16 h-16 object-cover rounded-md"
            style={{ minWidth: "64px", minHeight: "64px" }}
          />
        ) : (
          <div
            className="w-16 h-16 bg-gray-100 rounded-md flex items-center justify-center"
            style={{ minWidth: "64px", minHeight: "64px" }}
          >
            <LucideShoppingCart className="w-8 h-8 text-[#009F9F]" />
          </div>
        )}
      </div>
      <div className="flex flex-col ml-4 flex-grow overflow-hidden">
        <p className="text-sm font-medium text-gray-800 truncate">
          {item.title}
        </p>
        <p className="text-lg font-bold text-[#009F9F] mt-1">${item.price}</p>
      </div>
      <div
        className="cursor-pointer p-2 rounded-full bg-red-100 hover:bg-red-200 flex-shrink-0 ml-2"
        onClick={() => {
          removeFromCart(item.id);
          setTimeout(() => {
            window.dispatchEvent(new CustomEvent("cartUpdated"));
          }, 10);
        }}
      >
        <LucideTrash className="w-4 h-4 text-red-600" />
      </div>
    </div>
  );
};
export default CartItem;
