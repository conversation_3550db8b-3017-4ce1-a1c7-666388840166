import { useState, useEffect, useRef } from "react";
import CartItem from "./CartItem";
import { Plus, X } from "lucide-react";
import promoCodes from "../api/promoCodes";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import CheckoutModal from "./stripe/CheckoutModal";
import * as bookService from "../services/bookService";
import * as courseService from "../services/courseService";
import apiRequest from "../services/api";
import { useToast } from "../contexts/ToastContext";
const CartModal = ({ cartItems, closeModal, removeFromCart, clearCart }) => {
  const [promoCode, setPromoCode] = useState("");
  const [discount, setDiscount] = useState(0);
  const [error, setError] = useState("");
  const [isPromoInputVisible, setIsPromoInputVisible] = useState(false);
  const [promoApplied, setPromoApplied] = useState(false);
  const [loading, setLoading] = useState(false);
  const [checkoutModalOpen, setCheckoutModalOpen] = useState(false);
  const [paymentIntent, setPaymentIntent] = useState(null);
  const [hasHardcopyBooks, setHasHardcopyBooks] = useState(false);
  const modalRef = useRef(null);
  const navigate = useNavigate();
  const { user } = useAuth();
  const { addToast } = useToast();

  // Handle click outside to close modal
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        closeModal();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [closeModal]);

  // Check if cart contains any hardcopy books
  useEffect(() => {
    const checkForHardcopyBooks = () => {
      const hasHardcopy = cartItems.some(item =>
        !item.type || item.type !== "course" && item.type === "hardcopy"
      );
      setHasHardcopyBooks(hasHardcopy);
    };

    checkForHardcopyBooks();
  }, [cartItems]);

  const handleApplyPromo = () => {
    const validPromo = promoCodes.find((promo) => promo.code === promoCode);
    if (validPromo) {
      if (validPromo.type === "percentage") {
        const percentageDiscount =
          (validPromo.discount / 100) *
          cartItems.reduce((acc, item) => acc + item.price, 0);
        setDiscount(percentageDiscount);
      } else if (validPromo.type === "flat") {
        setDiscount(validPromo.discount);
      }
      setError("");
      setPromoApplied(true);
      setIsPromoInputVisible(false);
    } else {
      setDiscount(0);
      setError("Invalid promo code. Please try again.");
    }
  };

  const handleRemovePromo = () => {
    setPromoCode("");
    setDiscount(0);
    setPromoApplied(false);
    setIsPromoInputVisible(false);
  };

  const togglePromoInput = () => {
    setIsPromoInputVisible(!isPromoInputVisible);
    if (isPromoInputVisible) {
      setPromoCode("");
      setError("");
    }
  };

  const handleCheckout = async () => {
    if (!user) {
      closeModal();
      addToast("Please log in to complete your purchase", "info");
      navigate("/login?redirect=shop");
      return;
    }
    try {
      setLoading(true);
      if (cartItems.length === 1) {
        const item = cartItems[0];
        if (item.type === "course") {
          const paymentData = await courseService.createStripeCheckoutForCourse(
            item.id,
          );
          setPaymentIntent(paymentData);
          setCheckoutModalOpen(true);
        } else {
          const paymentData = await bookService.createStripeCheckoutForBook(
            item.id,
          );
          setPaymentIntent(paymentData);
          setCheckoutModalOpen(true);
        }
      } else if (cartItems.length > 1) {
        const bookItems = cartItems.filter(
          (item) => !item.type || item.type !== "course",
        );
        const courseItems = cartItems.filter((item) => item.type === "course");
        const bookIds = bookItems.map((item) => item.id);
        const courseIds = courseItems.map((item) => item.id);
        if (bookIds.length > 0 && courseIds.length > 0) {
          const paymentData = await apiRequest(
            "/stripe/mixed-payment-intent",
            "POST",
            {
              bookIds,
              courseIds,
              totalAmount: totalWithDiscount,
            },
          );
          setPaymentIntent(paymentData);
          setCheckoutModalOpen(true);
        } else if (bookIds.length > 0) {
          const paymentData = await bookService.createBulkStripeCheckout(
            bookIds,
            totalWithDiscount,
          );
          setPaymentIntent(paymentData);
          setCheckoutModalOpen(true);
        } else if (courseIds.length > 0) {
          const paymentData = await apiRequest(
            "/stripe/bulk-course-payment-intent",
            "POST",
            {
              courseIds,
              totalAmount: totalWithDiscount,
            },
          );
          setPaymentIntent(paymentData);
          setCheckoutModalOpen(true);
        }
      }
    } catch (err) {
      console.error("Failed to initiate checkout:", err);
      addToast("Failed to initiate checkout. Please try again.", "error");
    } finally {
      setLoading(false);
    }
  };
  const handleCheckoutSuccess = async () => {
    console.log("Payment successful, clearing cart...");
    setCheckoutModalOpen(false);
    const purchasedItems = [...cartItems];
    try {
      closeModal();
      if (typeof clearCart === "function") {
        clearCart();
      } else {
        localStorage.setItem("patentpionner_cart", JSON.stringify([]));
        if (typeof removeFromCart === "function") {
          const itemIds = purchasedItems.map((item) => item.id);
          for (const id of itemIds) {
            removeFromCart(id);
          }
        }
        window.dispatchEvent(new CustomEvent("cartUpdated"));
      }
      addToast("Your purchase was successful!", "success");
    } catch (error) {
      console.error("Error clearing cart:", error);
      addToast(
        "Your purchase was successful! Please refresh if items remain in cart.",
        "success",
      );
    }
    try {
      const courseItems = purchasedItems.filter(
        (item) => item.type === "course",
      );
      if (courseItems.length > 0) {
        try {
          for (const item of courseItems) {
            try {
              await courseService.purchaseCourse(item.id);
            } catch (purchaseError) {
              console.error(
                `Fallback purchase failed for course ${item.id}:`,
                purchaseError,
              );
            }
          }
          window.dispatchEvent(new CustomEvent("purchaseSuccess"));
          if (courseItems.length === 1) {
            window.location.href = `/course-detail/${courseItems[0].id}`;
          } else {
            window.location.href = "/courses/my";
          }
          return;
        } catch (courseError) {
          console.error("Error verifying course ownership:", courseError);
        }
      }
      if (window.location.pathname.includes("/dashboard")) {
        window.location.reload();
      } else {
        window.location.href = "/dashboard";
      }
    } catch (error) {
      console.error("Error verifying purchase ownership:", error);
    }
    setPaymentIntent(null);
  };
  const handleCheckoutError = (errorMessage) => {
    addToast(`Payment failed: ${errorMessage}`, "error");
  };
  const handleCloseCheckoutModal = () => {
    setCheckoutModalOpen(false);
    setPaymentIntent(null);
  };
  const totalAmount = cartItems.reduce((acc, item) => acc + item.price, 0);
  const totalWithDiscount = totalAmount - discount;
  return (
    <div className="fixed inset-0 bg-black/50 z-200 flex justify-end items-center transition-all duration-300 ease-in-out">
      <div
        ref={modalRef}
        className="bg-white w-full sm:w-96 p-6 h-[100%] max-h-screen overflow-y-auto transition-all duration-300 transform"
      >
        <div className="flex justify-between items-center">
          <h3 className="text-xl font-bold">{`Cart (${cartItems.length})`}</h3>
          <button
            onClick={closeModal}
            className="text-gray-600 hover:text-gray-800 cursor-pointer"
          >
            <X size={18} />
          </button>
        </div>
        {cartItems.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center mt-10">
            <p className="text-gray-600 text-lg">Your cart is empty.</p>
            <button
              onClick={() => {
                closeModal();
                window.location.href = "/book";
              }}
              className="mt-6 bg-[#009F9F] text-white px-6 py-2 rounded-md"
            >
              Visit Shop
            </button>
          </div>
        ) : (
          <>
            <div className="flex flex-col mt-4 gap-2">
              {cartItems.map((item) => (
                <CartItem
                  key={item.id}
                  item={item}
                  removeFromCart={removeFromCart}
                />
              ))}
            </div>
            <div className="mt-6">
              <div className="flex justify-between">
                <p className="text-gray-700">Subtotal</p>
                <p className="font-semibold">${totalAmount.toFixed(2)}</p>
              </div>
              <div className="flex justify-between mt-2 items-center">
                <p className="text-gray-700">Discount</p>
                {promoApplied ? (
                  <div className="flex items-center gap-2">
                    <button
                      onClick={handleRemovePromo}
                      className="text-red-500 p-2 rounded-full bg-gray-100 flex items-center"
                    >
                      <X size={18} />
                      <span className="text-gray-700 font-semibold mx-2">
                        {promoCode}
                      </span>
                    </button>
                    <p className="font-semibold text-green-500">
                      -${discount.toFixed(2)}
                    </p>
                  </div>
                ) : (
                  <button
                    onClick={togglePromoInput}
                    className="font-semibold text-gray-500 underline flex items-center cursor-pointer"
                  >
                   {isPromoInputVisible ? "" : <Plus size={18} />}
                    <span className="ml-2">
                      {isPromoInputVisible ? "Cancel" : "Apply Code"}
                    </span>
                  </button>
                )}
              </div>
              {isPromoInputVisible && !promoApplied && (
                <div className="flex flex-col">
                  <div className="mt-4 gap-2 flex items-center">
                    <input
                      type="text"
                      placeholder="Enter code"
                      value={promoCode}
                      onChange={(e) => setPromoCode(e.target.value)}
                      className="border p-2 w-full rounded-md"
                    />
                    <button
                      onClick={handleApplyPromo}
                      className="bg-[#009F9F] text-white px-4 py-2 rounded-md cursor-pointer"
                    >
                      Apply
                    </button>
                  </div>
                  {error && <p className="text-red-600 mt-2">{error}</p>}
                </div>
              )}
              <div className="flex justify-between mt-4">
                <p className="text-gray-700 text-lg">Total</p>
                <p className="font-semibold text-lg">
                  ${totalWithDiscount.toFixed(2)}
                </p>
              </div>
            </div>
            <div className="mt-6 text-center">
              <button
                onClick={handleCheckout}
                className="bg-[#009F9F] text-white px-6 py-2 rounded-md w-full cursor-pointer"
                disabled={loading}
              >
                {loading ? "Processing..." : "Go To Checkout"}
              </button>
            </div>
            {}
            {paymentIntent && (
              <CheckoutModal
                isOpen={checkoutModalOpen}
                onClose={handleCloseCheckoutModal}
                clientSecret={paymentIntent.clientSecret}
                onSuccess={handleCheckoutSuccess}
                onError={handleCheckoutError}
                bookTitle={
                  cartItems.length === 1
                    ? cartItems[0].title
                    : `${cartItems.length} items`
                }
                amount={totalWithDiscount.toFixed(2)}
                cartItems={cartItems}
                requiresShipping={hasHardcopyBooks}
              />
            )}
          </>
        )}
      </div>
    </div>
  );
};
export default CartModal;
