import React, { useState, useEffect } from "react";
import axios from "axios";
import toast from "react-hot-toast";
import Button from "./Button";
import countryCodes from "../data/mock/countryCodes.json";
const ContactForm = ({ direction, background, extraclass }) => {
  const [form, setForm] = useState({
    name: "",
    email: "",
    countryCode: "+1",
    phone: "",
    message: "",
    accepted: false,
  });
  const [errors, setErrors] = useState({});
  useEffect(() => {
    const detectCountry = async () => {
      try {
        const res = await axios.get("https://ipapi.co/json");
        const userCountry = res.data.country;
        const matched = countryCodes.find((c) => c.code === userCountry);
        if (matched) {
          setForm((prev) => ({ ...prev, countryCode: matched.dial_code }));
        }
      } catch (err) {
        console.error("Country detection failed", err);
      }
    };
    detectCountry();
  }, []);
  const validate = () => {
    const newErrors = {};
    if (!form.name.trim()) newErrors.name = "Name is required";
    if (!form.email.trim()) newErrors.email = "Email is required";
    else if (
      form.email.includes("tempmail") ||
      form.email.includes("mailinator") ||
      form.email.includes("10minutemail")
    )
      newErrors.email = "Temporary emails are not allowed";
    if (!form.phone.trim()) newErrors.phone = "Phone number is required";
    if (!form.message.trim()) newErrors.message = "Message is required";
    if (!form.accepted) newErrors.accepted = "You must accept terms";
    return newErrors;
  };
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    if (name === "phone") {
      if (/[^0-9]/.test(value)) return;
    }
    setForm((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    const validationErrors = validate();
    setErrors(validationErrors);
    if (Object.keys(validationErrors).length > 0) return;
    toast.success(
      "We have received your query, and our team will contact you soon. Thank you.",
      { duration: 6000 },
    );
    setForm({
      name: "",
      email: "",
      countryCode: form.countryCode,
      phone: "",
      message: "",
      accepted: false,
    });
    setErrors({});
  };
  return (
    <form
      onSubmit={handleSubmit}
      className={`${background || "bg-white"} rounded-lg max-w-5xl mx-auto p-4 md:p-8 ${
        direction || "grid md:grid-cols-2 gap-6 text-left"
      }
      ${extraclass}`}
    >
      <div className="space-y-4">
        {}
        <div>
          <label className="block mb-1 text-sm font-medium text-gray-700">
            Full Name
          </label>
          <input
            name="name"
            value={form.name}
            onChange={handleChange}
            placeholder="Enter your name"
            className="w-full bg-white border border-gray-300 px-4 py-2 rounded-md text-gray-800 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#009F9F]"
          />
          {errors.name && (
            <p className="text-red-600 text-sm mt-1">{errors.name}</p>
          )}
        </div>
        {}
        <div>
          <label className="block mb-1 text-sm font-medium text-gray-700">
            Email
          </label>
          <input
            name="email"
            type="email"
            value={form.email}
            onChange={handleChange}
            placeholder="Enter your email"
            className="w-full bg-white border border-gray-300 px-4 py-2 rounded-md text-gray-800 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#009F9F]"
          />
          {errors.email && (
            <p className="text-red-600 text-sm mt-1">{errors.email}</p>
          )}
        </div>
        {}
        <div>
          <label className="block mb-1 text-sm font-medium text-gray-700">
            Phone Number
          </label>
          <div className="flex flex-col md:flex-row gap-2">
            <select
              name="countryCode"
              value={form.countryCode}
              onChange={handleChange}
              className="border border-gray-300 px-3 py-2 rounded-md bg-white text-gray-800 focus:ring-2 focus:ring-[#009F9F]"
            >
              {countryCodes.map((c) => (
                <option key={c.code} value={c.dial_code}>
                  {c.flag} {c.code} ({c.dial_code})
                </option>
              ))}
            </select>
            <input
              name="phone"
              value={form.phone}
              onChange={handleChange}
              placeholder="Enter your number"
              className="w-full bg-white border border-gray-300 px-4 py-2 rounded-md text-gray-800 placeholder-gray-400 focus:ring-2 focus:ring-[#009F9F]"
            />
          </div>
          {errors.phone && (
            <p className="text-red-600 text-sm mt-1">{errors.phone}</p>
          )}
        </div>
      </div>
      {}
      <div className="flex flex-col justify-between gap-4">
        <div>
          <label className="block mb-1 text-sm font-medium text-gray-700">
            Description
          </label>
          <textarea
            name="message"
            rows="6"
            value={form.message}
            onChange={handleChange}
            placeholder="Message"
            className="w-full bg-white border border-gray-300 px-4 py-2 rounded-md resize-none text-gray-800 placeholder-gray-400 focus:ring-2 focus:ring-[#009F9F]"
          />
          {errors.message && (
            <p className="text-red-600 text-sm mt-1">{errors.message}</p>
          )}
        </div>
        <div className="bg-yellow-50 border border-gray-200 text-black p-4 rounded text-center text-sm">
          🔒 reCAPTCHA will be enabled here once configured.
        </div>
      </div>
      {}
      <div className="md:col-span-2 mt-4 space-y-4">
        <div className="flex items-start gap-2 text-sm text-gray-700 leading-relaxed">
          <input
            type="checkbox"
            name="accepted"
            checked={form.accepted}
            onChange={handleChange}
            className="mt-1 bg-white"
          />
          <p>
            I agree with the{" "}
            <a href="#" className="text-[#009F9F] underline">
              Terms & Conditions
            </a>{" "}
            and the{" "}
            <a href="#" className="text-[#009F9F] underline">
              Privacy & Cookies Policy
            </a>{" "}
            of UENI and any applicable Terms and Conditions of Patent Pioneer
            Institute™. This site is protected by reCAPTCHA and the Google{" "}
            <a
              href="https://policies.google.com/privacy"
              className="text-[#009F9F] underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              Privacy Policy
            </a>{" "}
            and{" "}
            <a
              href="https://policies.google.com/terms"
              className="text-[#009F9F] underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              Terms of Service
            </a>{" "}
            apply.
          </p>
        </div>
        {errors.accepted && (
          <p className="text-red-600 text-sm">{errors.accepted}</p>
        )}
        <div className="flex flex-col-reverse sm:flex-row sm:justify-between items-center gap-4 mt-6">
          {(form.name || form.email || form.phone || form.message) && (
            <button
              type="button"
              onClick={() =>
                setForm({
                  name: "",
                  email: "",
                  phone: "",
                  message: "",
                  accepted: false,
                  countryCode: form.countryCode,
                })
              }
              className="text-sm text-gray-600 underline hover:text-gray-800 transition"
            >
              Clear Form
            </button>
          )}
          <div className="w-full">
            <button
              type="submit"
              className="w-full bg-[#009F9F] hover:bg-[#008585] text-white py-3 px-4 rounded-md font-medium transition-colors duration-200"
            >
              Send Message
            </button>
          </div>
        </div>
      </div>
    </form>
  );
};
export default ContactForm;
