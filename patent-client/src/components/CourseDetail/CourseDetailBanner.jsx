import React, { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { ShoppingCart, Clock, BadgeCheck, Users } from "lucide-react";
import courseDetailImg from "../../assets/courseDetail.png";
import { API_BASE_URL } from "../../config/config.js";
const CourseDetailBanner = ({ course, onBuyNow }) => {
  const { user } = useAuth();
  const [isOwned, setIsOwned] = useState(false);
  useEffect(() => {
    const checkCourseOwnership = async () => {
      if (!user || !course) {
        setIsOwned(false);
        return;
      }
      try {
        const response = await fetch(`${API_BASE_URL}/purchases/my/courses`, {
          headers: {
            Authorization: `Bearer ${user.accessToken || user.token}`,
          },
        });
        if (response.ok) {
          const userCoursesData = await response.json();
          const courseIds = Array.isArray(userCoursesData)
            ? userCoursesData.map((c) => c._id)
            : [];
          setIsOwned(courseIds.includes(course._id));
        }
      } catch (err) {
        console.error("Error checking course ownership:", err);
        setIsOwned(false);
      }
    };
    checkCourseOwnership();
    const handlePurchaseSuccess = () => checkCourseOwnership();
    window.addEventListener("purchaseSuccess", handlePurchaseSuccess);
    return () =>
      window.removeEventListener("purchaseSuccess", handlePurchaseSuccess);
  }, [user, course]);
  return (
    <div className="py-12 pt-40 md:py-16 px-4 max-w-6xl mx-auto">
      <div className="w-full max-w-[906px] mx-auto flex flex-col justify-center">
        <div className="text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl text-[#000000] font-bold leading-tight">
            {course ? course.title : "Patent Bar Exam Bootcamp"}
          </h1>
          <p className="text-[#54585C] text-base md:text-xl mt-4 max-w-3xl mx-auto">
            {course
              ? course.description
              : "A fast-track program to help you pass the USPTO Patent Bar Exam, featuring focused lessons, practice questions, and test-day strategies."}
          </p>
          {}
          <div className="flex flex-wrap justify-center gap-6 mt-6">
            {course?.hours > 0 && (
              <div className="flex items-center gap-2 text-gray-600">
                <Clock size={18} />
                <span>
                  {course.hours} {course.hours === 1 ? "Hour" : "Hours"}
                </span>
              </div>
            )}
            {course?.certificate && (
              <div className="flex items-center gap-2 text-gray-600">
                <BadgeCheck size={18} />
                <span>Certificate Included</span>
              </div>
            )}
            {course?.level && (
              <div className="flex items-center gap-2 text-gray-600">
                <Users size={18} />
                <span>{course.level} Level</span>
              </div>
            )}
          </div>
          {}
          {course && (
            <div className="mt-6 flex flex-col sm:flex-row items-center justify-center gap-4">
              <div className="text-2xl font-bold text-[#009F9F]">
                ${course?.price?.toFixed(2) || "199.99"}
              </div>
              <div className="flex gap-3">
              {isOwned || (
                <button
                  onClick={onBuyNow}
                  className="bg-[#009F9F] text-white px-6 py-2 rounded-md flex items-center gap-2 hover:bg-[#018A8A] transition-colors"
                >
                  <ShoppingCart size={18} />
                  Buy Now
                </button>
                 )}
                {isOwned && (
                  <button
                    onClick={() => (window.location.href = "/courses/my")}
                    className="bg-gray-100 text-gray-900 px-6 py-2 rounded-md flex items-center gap-2 hover:bg-gray-200 transition-colors"
                  >
                    View Course
                  </button>
                )}
              </div>
              {}
            </div>
          )}
        </div>
        <div className="mt-8 flex justify-center">
          <div className="relative w-full rounded-lg overflow-hidden shadow-lg">
            <img
              src={course?.image || courseDetailImg}
              alt={course?.title || "Course banner"}
              className="w-full h-auto object-cover"
            />
           

            {/* {course?.modules && Array.isArray(course.modules) && course.modules.length > 0 &&
             course.modules[0].type === "video" ? (
              <video
                src={course.modules[0].content}
                controls
                className="w-full h-auto"
                poster={courseDetailImg}
              >
                Your browser does not support the video tag.
              </video>
            ) : course?.modules && typeof course.modules === 'object' && course.modules.content ? (
              // Handle case where modules might not be an array but an object with content
              <video
                src={course.modules.content}
                controls
                className="w-full h-auto"
                poster={courseDetailImg}
              >
                Your browser does not support the video tag.
              </video>
            ) : (
              <div className="bg-gray-100 p-8 text-center">
                <p className="text-gray-600">No video content available</p>
              </div>
            )} */}
          </div>
        </div>
      </div>
    </div>
  );
};
export default CourseDetailBanner;
