import React from "react";
import youtube from "../../assets/youtube.png";
import smile from "../../assets/smile.png";
const CourseFeatures = () => {
  const features = [
    {
      id: 1,
      icon: youtube,
      title: "Daily Video Lessons",
      description:
        "Easy-to-follow and short practical lessons designed to help you implement what you learn immediately.",
    },
    {
      id: 2,
      icon: smile,
      title: "Hands-On Practice",
      description:
        "Apply what you learn immediately by working on growth experiments focused on your current role and tailored to your business goals.",
    },
  ];
  return (
    <div className="bg-[#F7F7F8] w-full py-12 md:py-20">
      <div className="py-20 max-w-6xl mx-auto px-4 text-center">
        <div className="w-full md:w-[683px] mx-auto">
          <h2 className="text-2xl md:text-3xl font-bold mb-8">Get access to</h2>
          <div className="space-y-[8px]">
            {features.map((feature) => (
              <div
                key={feature.id}
                className="bg-white rounded-lg border border-gray-100 p-[24px] md:p-[24px]"
              >
                <div className="flex flex-col md:flex-row items-start gap-[17px]">
                  <div className="w-full md:w-[90px] text-left mb-4 md:mb-0 flex justify-center md:justify-start">
                    <img
                      src={feature.icon}
                      alt={feature.title}
                      className="w-[64px] h-[64px] md:w-[96px] md:h-[96px]"
                    />
                  </div>
                  <div className="flex-1 text-left">
                    <h3 className="text-[16px] md:text-[16px] font-semibold mb-2 text-[#000000]">
                      {feature.title}
                    </h3>
                    <p className="text-[14px] md:text-[16px] text-[#66737F]">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <button className="bg-[#009F9F] hover:bg-[#009F9F] text-white font-medium p-[16px] rounded text-[16px] md:text-[16px] w-full max-w-xs mx-auto mt-8">
            Yes I Want $17 Bootcamp
          </button>
        </div>
      </div>
    </div>
  );
};
export default CourseFeatures;
