import React, { useState } from "react";
import { Plus, Minus } from "lucide-react";
const FAQSection = () => {
  const [openIndex, setOpenIndex] = useState(0);
  const faqData = [
    {
      id: 1,
      question: "What is the Patent Bar Exam?",
      answer:
        "The Patent Bar Exam is a licensing exam that qualifies individuals to represent inventors before the United States Patent and Trademark Office (USPTO). This course helps you prepare for the exam by covering its structure and key content.",
    },
    {
      id: 2,
      question: "Do I need prior knowledge of patent law to take this course?",
      answer:
        "No, this course is designed for beginners and assumes no prior knowledge of patent law. We start with the fundamentals and gradually build up to more complex topics.",
    },
    {
      id: 3,
      question: "Can I take this course at my own pace?",
      answer:
        "Yes, all course materials are available immediately upon enrollment, and you can progress through them at your own pace. There are no deadlines or time restrictions.",
    },
    {
      id: 4,
      question: "Who is this course for?",
      answer:
        "This course is designed for aspiring patent agents, patent attorneys, inventors, engineers, scientists, and anyone interested in learning about patent law and passing the Patent Bar Exam.",
    },
    {
      id: 5,
      question: "How long is the course?",
      answer:
        "The course consists of 6 days of content, but you can complete it at your own pace. Most students finish the course within 2-4 weeks, spending about 1-2 hours per day.",
    },
    {
      id: 6,
      question: "What will I learn in this course?",
      answer:
        "You'll learn the fundamentals of patent law, the structure of the Patent Bar Exam, how to navigate the MPEP, key USPTO rules and procedures, and strategies for passing the exam.",
    },
  ];
  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };
  return (
    <div className="bg-white w-full py-12 md:py-20">
      <div className="max-w-6xl mx-auto px-4">
        <div className="w-full sm:w-[90%] md:w-[683px] mx-auto">
          <h2 className="text-2xl md:text-3xl font-bold mb-8 text-center">
            FAQs
          </h2>
          <div className="space-y-4">
            {faqData.map((faq, index) => (
              <div
                key={faq.id}
                className="border border-gray-200 rounded-lg overflow-hidden"
              >
                <div
                  className={`p-4 flex justify-between items-center cursor-pointer ${index === 0 && openIndex === null ? "bg-[#F7F7F8]" : openIndex === index ? "bg-[#F7F7F8]" : "bg-[#F7F7F8]"}`}
                  onClick={() => toggleFAQ(index)}
                >
                  <h3 className="text-[16px] font-bold text-[#000000]">
                    {faq.question}
                  </h3>
                  <button className="">
                    {openIndex === index ? (
                      <Minus className="text-[#009F9F]" size={20} />
                    ) : (
                      <Plus className="text-[#000000]" size={20} />
                    )}
                  </button>
                </div>
                {(index === 0 && openIndex === null) || openIndex === index ? (
                  <div className="pr-4 pl-3 pb-4 bg-[#F7F7F8]">
                    <p className="text-[14px] sm:text-[16px] text-[#66737F]">
                      {faq.answer}
                    </p>
                  </div>
                ) : null}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
export default FAQSection;
