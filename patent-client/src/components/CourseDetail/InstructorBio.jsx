import React from "react";
import instructorImg from "../../assets/instructor.png";
import tick from "../../assets/tick.svg";
import arrow2 from "../../assets/arrow2.svg";
import Linkedin from "../../assets/linkedin.svg";
import Mail from "../../assets/mail.svg";
import Phone from "../../assets/phone.svg";
const InstructorBio = () => {
  const achievements = [
    "I led a 100+ people tech company and was an executive at Xaxis, a $1.5B programmatic advertising company.",
    "I launched multi-million-dollar products and developed strategies to grow margins by 40%-50%",
    "I launched and scaled markets like the U.S. (0 to $21M) and Australia ($5M to $10M)",
    "I helped 100+ startups and scaleups expand into new markets, validate product ideas, and design their operations to scale",
    "Over the years, I've done business across the world: Australia, Canada, China, EU, LATAM, Saudi Arabia, Singapore, U.A.E., and the U.S.",
    "Prior to all of this, I worked as Management Consultant at PwC and in Strategy and International Business Development at Bombardier Aerospace.",
  ];
  return (
    <div className="bg-white w-full py-16 md:py-20">
      <div className="container   mx-auto px-4">
        <div className="flex flex-col lg:flex-row gap-[80px] lg:gap-[130px] items-start">
          {}
          <div className="w-full lg:w-3/5">
            <p className="text-gray-700 mb-6">
              Hi, I'm Alia, a tech leader, entrepreneur, and advisor. For the
              past 8 years, I was Global MD at plista, a 100-people adtech
              company owned by WPP and operating in 15 markets. I'm also Advisor
              at Stun and Awe, and Mentor at Greentech Alliance and APX (Axel
              Springer & Porsche).
            </p>
            <div className="space-y-4">
              {achievements.map((achievement, index) => (
                <div key={index} className="flex items-start gap-3">
                  <img
                    src={tick}
                    alt=""
                    className="w-5 h-5 mt-1 flex-shrink-0"
                  />
                  <p className="text-[#66737F]">{achievement}</p>
                </div>
              ))}
            </div>
            <div className="mt-8">
              <button className="bg-[#009F9F] hover:bg-[#008a8a] text-white font-medium py-4 px-6 rounded text-base w-full sm:w-auto">
                Yes I Want $17 Bootcamp
              </button>
            </div>
          </div>
          {}
          <div className="w-full lg:w-1/2 mt-8 lg:mt-0">
            <div className="relative flex flex-col items-center sm:items-start">
              {}
              <div className="absolute left-0 top-[120px] transform -translate-x-1/4 z-10 hidden md:block">
                <img src={arrow2} alt="" className="w-[230px] mr-[230px]" />
              </div>
              <div className="rounded-lg overflow-hidden">
                <img
                  src={instructorImg}
                  alt="Alia Johnson"
                  className="w-full max-w-[500px] h-auto sm:h-[483px] rounded-lg object-cover"
                />
              </div>
              <div className="mt-10 text-center sm:text-left">
                <h2 className="text-3xl font-bold mb-3">Alia Johnson</h2>
                <div className="flex gap-4 items-center justify-center sm:justify-start">
                  <a href="#" className="text-[#009F9F]">
                    <img src={Linkedin} alt="" />
                  </a>
                  <a href="#" className="text-[#009F9F]">
                    <img src={Mail} alt="" />
                  </a>
                  <a href="#" className="text-[#009F9F]">
                    <img src={Phone} alt="" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default InstructorBio;
