import React from "react";
import tick from "../../assets/tick.svg";
import { FileText, Video, Image } from "lucide-react";
const PatentCourseOutline = ({ course }) => {
  const defaultCourseOutlineData = [
    {
      id: 1,
      title: "Course Overview & Patent Bar Exam Structure",
      day: "Day 1",
      topics: [
        "Introduction to the Patent Bar Exam, requirements, and expectations.",
        "Overview of the MPEP and its importance for the exam.",
      ],
    },
    {
      id: 2,
      title: "Patent Law Fundamentals",
      day: "Day 2",
      topics: [
        "Understanding patents, types of patents (utility, design, plant).",
        "Patentability requirements: Novelty, non-disclosures, and utility.",
      ],
    },
    {
      id: 3,
      title: "Patent Application Process",
      day: "Day 3",
      topics: [
        "Overview of filing a patent application with the USPTO.",
        "Key documents and forms used during patent prosecution.",
      ],
    },
    {
      id: 4,
      title: "USPTO Rules & Procedures (Part 1)",
      day: "Day 4",
      topics: [
        "Introduction to critical USPTO rules and guidelines for filing, examination, and issuance of patents.",
        "Focus on the application process.",
      ],
    },
    {
      id: 5,
      title: "USPTO Rules & Procedures (Part 2)",
      day: "Day 5",
      topics: [
        "Continuing with rules on examiner communication, amendments, and post-grant procedures.",
      ],
    },
    {
      id: 6,
      title: "Review & Practice Quiz",
      day: "Day 6",
      topics: [
        "Review key concepts from Week 1.",
        "Practice quiz on patent law fundamentals and USPTO procedures.",
      ],
    },
  ];
  const getModuleIcon = (type) => {
    switch (type) {
      case "video":
        return <Video size={18} className="text-[#009F9F]" />;
      case "image":
        return <Image size={18} className="text-[#009F9F]" />;
      case "text":
      default:
        return <FileText size={18} className="text-[#009F9F]" />;
    }
  };
  const hasModules =
    course && Array.isArray(course.modules) && course.modules.length > 0;
  return (
    <div className="bg-[#F7F7F8] w-full">
      <div className="py-12 container  mx-auto px-4 text-center">
        <div className="w-full sm:w-[90%] md:w-[683px] mx-auto">
          <h2 className="text-2xl md:text-3xl font-bold mb-2">
            Here's Exactly What You'll Learn
          </h2>
          <p className="text-gray-600 mb-8 max-w-lg mx-auto text-sm md:text-base">
            {course?.description ||
              "Everything you need to master patent law, advance your career, or boost your business."}
          </p>
          <div className="space-y-[8px]">
            {hasModules
              ? course.modules.map((module, index) => (
                  <div
                    key={module._id || index}
                    className="bg-white rounded-lg border border-gray-100 p-[16px] sm:p-[24px] md:p-[24px] min-h-[120px] h-auto"
                  >
                    <div className="flex items-center gap-2 mb-3 text-left">
                      {getModuleIcon(module.type)}
                      <h3 className="text-[16px] md:text-[18px] font-semibold text-[#000000]">
                        {module.title}
                      </h3>
                    </div>
                    <div className="flex flex-col md:flex-row">
                      <div className="w-full md:w-[90px] text-left mb-2 md:mb-0">
                        <span className="text-[16px] md:text-[18px] text-[#009F9F] font-bold">
                          Module {index + 1}:
                        </span>
                      </div>
                      <div className="flex-1 text-left">
                        <div className="flex items-start gap-2">
                          <img
                            src={tick}
                            alt=""
                            className="w-5 h-5 mt-1 flex-shrink-0"
                          />
                          <p className="text-[14px] sm:text-[16px] md:text-[16px] text-[#66737F]">
                            {module.type === "text"
                              ? module.content?.substring(0, 120) +
                                (module.content?.length > 120 ? "..." : "")
                              : `${module.type.charAt(0).toUpperCase() + module.type.slice(1)} content`}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              : defaultCourseOutlineData.map((section) => (
                  <div
                    key={section.id}
                    className="bg-white rounded-lg border border-gray-100 p-[16px] sm:p-[24px] md:p-[24px] min-h-[152px] h-auto"
                  >
                    <h3 className="text-[16px] md:text-[16px] font-semibold mb-3 text-left text-[#000000]">
                      {section.title}
                    </h3>
                    <div className="flex flex-col md:flex-row">
                      <div className="w-full md:w-[90px] text-left mb-2 md:mb-0">
                        <span className="text-[20px] md:text-[24px] text-[#009F9F] font-bold">
                          {section.day}:
                        </span>
                      </div>
                      <div className="flex-1 text-left space-y-2">
                        {section.topics.map((topic, topicIndex) => (
                          <div
                            key={topicIndex}
                            className="flex items-start gap-2"
                          >
                            <img
                              src={tick}
                              alt=""
                              className="w-5 h-5 mt-1 flex-shrink-0"
                            />
                            <p className="text-[14px] sm:text-[16px] md:text-[16px] text-[#66737F]">
                              {topic}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
          </div>
          {}
          {!course && (
            <button className="bg-[#009F9F] hover:bg-[#008A8A] text-white font-medium p-[16px] rounded text-[16px] md:text-[16px] w-full max-w-xs mx-auto mt-8 transition-colors">
              Enroll Now
            </button>
          )}
        </div>
      </div>
    </div>
  );
};
export default PatentCourseOutline;
