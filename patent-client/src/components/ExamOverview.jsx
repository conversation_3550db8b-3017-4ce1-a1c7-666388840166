// src/components/ExamOverview.jsx
import React from "react";
import AboutImage from "../assets/aboutimage.png";
import Button from "./Button";
const ExamOverview = () => {
  return (
    <section className="z-1 py-20 px-4 flex flex-col md:flex-row items-center justify-between max-w-6xl md:mx-auto">
      <div className="md:w-1/2 w-full">
        <h2 className="text-4xl font-bold mb-4">Master the USPTO Exam</h2>
        <p className="text-base mb-6 text-gray-700">
          Elevate your USPTO exam preparation with our comprehensive and
          engaging study solutions. Our tailored services combine advanced
          gamification, expert guidance, and a wealth of resources to help you
          excel. Ready to take the next step toward becoming a patent attorney?
        </p>
        <p className="text-base font-bold text-gray-700 mb-4">
          Click below to explore our USPTO exam preparation services.
        </p>
        <Button text="Start Preparation" />
      </div>
      <div className="md:w-1/2 flex justify-center mt-10 md:mt-0 relative">
        <div className="max-w-96 h-96 rounded-full overflow-hidden">
          <img
            src={AboutImage}
            alt="USPTO Section Visual"
            className="object-cover w-full h-full"
          />
        </div>
        <div className="absolute -bottom-6 left-16 bg-[#018A8A] text-white w-32 h-32 md:w-40 md:h-40 rounded-full flex flex-col items-center justify-center text-center shadow-lg">
          <h3 className="text-2xl md:text-3xl font-bold">500+</h3>
          <p className="text-xs md:text-sm">Certified Users</p>
        </div>
      </div>
    </section>
  );
};
export default ExamOverview;
