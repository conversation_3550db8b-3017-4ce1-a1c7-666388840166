// src/components/Footer.jsx
import React from "react";
import Logo from "../assets/Logo.png";
import { Mail, Phone } from "lucide-react";
const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white ">
      <div className="mx-auto px-6 py-10 grid md:grid-cols-4 gap-6 max-w-6xl">
        <div className="flex items-start gap-3">
          <img
            src={Logo}
            alt="Patent Pioneer Logo"
            className="w-42 h-42 object-contain"
          />
        </div>
        <div>
          <h4 className="text-xl font-semibold mb-4 border-l-4 pl-2 border-[#009F9F]">
            Company
          </h4>
          <ul className="space-y-4 text-md">
            <li>Home</li>
            <li>USPTO Exam</li>
            <li>About Us</li>
            <li>Shop</li>
          </ul>
        </div>
        <div>
          <h4 className="text-xl font-semibold mb-4 border-l-4 pl-2 border-[#009F9F]">
            Explore More
          </h4>
          <ul className="space-y-4 text-md">
            <li>Blog</li>
            <li>LinkedIn</li>
            <li>Facebook</li>
            <li>Youtube</li>
            <li>Instagram</li>
          </ul>
        </div>
        <div>
          <h4 className="text-xl font-semibold mb-4 border-l-4 pl-2 border-[#009F9F]">
            Customer Support
          </h4>
          <ul className="space-y-4 text-md">
            <li>Contact Us</li>
            <li>FAQ’s</li>
            <li className="flex items-center gap-2">
              <Mail className="w-4 h-4 text-[#009F9F]" />
              <a
                href="mailto:<EMAIL>"
                className="hover:underline"
              >
                <EMAIL>
              </a>
            </li>
            <li className="flex items-center gap-2">
              <Phone className="w-4 h-4 text-[#009F9F]" />
              <a href="tel:9739962947" className="hover:underline">
                (*************
              </a>
            </li>
            <li className="flex items-center gap-2">
              <Phone className="w-4 h-4 text-[#009F9F]" />
              <a href="tel:9739962951" className="hover:underline">
                (*************
              </a>
            </li>
          </ul>
        </div>
      </div>
      <div className="bg-[#009F9F] flex justify-center ">
        <div className="max-w-6xl w-full text-white text-md py-4 px-6 flex flex-col md:flex-row justify-between items-center">
          <p>© 2025 patentpioneer. All Rights Reserved.</p>
          <div className="flex gap-4 mt-2 md:mt-0">
            <a href="#" className="hover:underline">
              Merchant Policies
            </a>
            <span className="text-white">|</span>
            <a href="#" className="hover:underline">
              Legal Notice
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};
export default Footer;
