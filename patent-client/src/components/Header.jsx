import React, { useState, useEffect, useRef } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Menu, X, User, LogOut, LayoutDashboard } from "lucide-react";
import Button from "./Button";
import Logo from "../assets/Logo.png";
import CartIcon from "./CartIcon";
import { useAuth } from "../contexts/AuthContext";

const Header = ({ cartItems, removeFromCart, clearCart }) => {
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [showHeader, setShowHeader] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [showProfileDropdown, setShowProfileDropdown] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const profileDropdownRef = useRef(null);

  useEffect(() => {
    const handleScroll = () => {
      if (window.innerWidth >= 1024 || showMobileMenu) return; // Changed from 768 to 1024 for tablet support
      const currentScrollY = window.scrollY;
      setShowHeader(currentScrollY < lastScrollY);
      setLastScrollY(currentScrollY);
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, [lastScrollY, showMobileMenu]);

  useEffect(() => {
    if (showMobileMenu) {
      document.body.classList.add("overflow-hidden");
    } else {
      document.body.classList.remove("overflow-hidden");
    }
  }, [showMobileMenu]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        profileDropdownRef.current &&
        !profileDropdownRef.current.contains(event.target)
      ) {
        setShowProfileDropdown(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    logout();
    setShowProfileDropdown(false);
    navigate("/");
  };

  const goToDashboard = () => {
    navigate("/dashboard");
    setShowProfileDropdown(false);
  };

  const isActive = (path) => {
    if (path === "/course") {
      return location.pathname.startsWith("/course");
    }
    return location.pathname === path;
  };

  return (
    <>
      {showMobileMenu && (
        <div className="fixed inset-0 bg-black/50 bg-opacity-50 z-[90] lg:hidden transition-opacity duration-300 ease-in-out" />
      )}
      <header
        className={`bg-white shadow  px-4 py-4 w-full fixed md:sticky z-[100] transition-all duration-300 ease-in-out ${
          showHeader ? "top-0" : "-top-40"
        }`}
      >
        <div className="container mx-auto flex justify-between items-center">
          <div>
            <Link to="/">
              <img
                src={Logo}
                alt="PatentPath Logo"
                className="w-16 h-16 object-contain"
              />
            </Link>
          </div>

          {/* Desktop Navigation - Only visible on large screens */}
          <div className="hidden lg:flex items-center gap-[32px]">
            <nav className="flex space-x-6 items-center">
              <Link
                to="/"
                className={`hover:text-[#009F9F] ${
                  isActive("/") ? "text-[#009F9F] font-semibold" : ""
                }`}
              >
                Home
              </Link>
              <Link
                to="/about"
                className={`hover:text-[#009F9F] ${
                  isActive("/about") ? "text-[#009F9F] font-semibold" : ""
                }`}
              >
                About Us
              </Link>
              <Link
                to="/course"
                className={`hover:text-[#009F9F] ${
                  isActive("/course") ? "text-[#009F9F] font-semibold" : ""
                }`}
              >
                Courses
              </Link>
              <Link
                to="/book"
                className={`hover:text-[#009F9F] ${
                  isActive("/book") ? "text-[#009F9F] font-semibold" : ""
                }`}
              >
                Books{" "}
              </Link>
              <Link
                to="/patent-museum"
                className={`hover:text-[#009F9F] ${
                  isActive("/patent-museum")
                    ? "text-[#009F9F] font-semibold"
                    : ""
                }`}
              >
                {" "}
                Patent Museum{" "}
              </Link>
              <Link
                to="/insights"
                className={`hover:text-[#009F9F] ${
                  isActive("/insights") ? "text-[#009F9F] font-semibold" : ""
                }`}
              >
                Insights
              </Link>
              <Link
                to="/faq"
                className={`hover:text-[#009F9F] ${
                  isActive("/faq") ? "text-[#009F9F] font-semibold" : ""
                }`}
              >
                FAQ
              </Link>
              <Link
                to="/contact"
                className={`hover:text-[#009F9F] ${
                  isActive("/contact") ? "text-[#009F9F] font-semibold" : ""
                }`}
              >
                Contact Us
              </Link>
            </nav>

            <CartIcon
              cartItems={cartItems}
              removeFromCart={removeFromCart}
              clearCart={clearCart}
            />

            {user ? (
              <div className="relative" ref={profileDropdownRef}>
                <div
                  className="flex items-center gap-2 cursor-pointer"
                  onClick={() => setShowProfileDropdown(!showProfileDropdown)}
                >
                  <span className="hidden sm:block text-sm font-medium text-gray-700 max-w-[120px] truncate">
                    {user.firstName ||
                      user.username ||
                      user.email?.split("@")[0]}
                  </span>
                  <div className="p-1 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center">
                    {user.firstName ? (
                      <div className="w-7 h-7 rounded-full bg-[#009F9F] text-white flex items-center justify-center text-xs font-bold">
                        {user.firstName.charAt(0).toUpperCase()}
                      </div>
                    ) : (
                      <User className="w-5 h-5 text-gray-700" />
                    )}
                  </div>
                </div>

                {showProfileDropdown && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                    <div className="px-4 py-3 text-sm text-gray-700 border-b border-gray-200">
                      <div className="flex items-center gap-2 mb-1">
                        <div className="w-8 h-8 rounded-full bg-[#009F9F] text-white flex items-center justify-center text-sm font-bold overflow-hidden">
                          {(
                            user.firstName ||
                            user.username ||
                            user.email?.split("@")[0]
                          )
                            ?.charAt(0)
                            .toUpperCase()}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-semibold truncate">
                            {user.firstName ||
                              user.username ||
                              user.email?.split("@")[0]}
                          </p>
                          <p className="text-xs text-gray-500 truncate">
                            {user.email}
                          </p>
                        </div>
                      </div>
                      <Link
                        to="/profile"
                        className="text-xs text-[#009F9F] hover:underline mt-1 block"
                        onClick={() => setShowProfileDropdown(false)}
                      >
                        View Profile
                      </Link>
                    </div>

                    <button
                      onClick={goToDashboard}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                    >
                      <LayoutDashboard className="w-4 h-4 mr-2" />
                      Dashboard
                    </button>

                    <button
                      onClick={handleLogout}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                    >
                      <LogOut className="w-4 h-4 mr-2" />
                      Logout
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <Button text="Login" to="/login" />
            )}
          </div>

          {/* Mobile and Tablet Navigation */}
          <div className="flex lg:hidden items-center gap-4">
            <div className="p-2 rounded hover:bg-gray-100 group cursor-pointer">
              <CartIcon
                cartItems={cartItems}
                removeFromCart={removeFromCart}
                clearCart={clearCart}
              />
            </div>
            <button
              onClick={() => setShowMobileMenu(!showMobileMenu)}
              className="p-2 rounded hover:bg-gray-100"
            >
              {showMobileMenu ? (
                <X className="w-6 h-6 text-gray-700" />
              ) : (
                <Menu className="w-6 h-6 text-gray-700" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile and Tablet Menu */}
        <div
          className={`lg:hidden overflow-hidden transition-all duration-300 mt-[10px] ease-in-out transform origin-top ${
            showMobileMenu
              ? "max-h-[500px] opacity-100 scale-y-100 pb-4"
              : "max-h-0 opacity-0 scale-y-95"
          } bg-white px-6 shadow`}
        >
          <nav className="flex flex-col gap-3 pt-4">
            <Link
              to="/"
              onClick={() => setShowMobileMenu(false)}
              className={isActive("/") ? "text-[#009F9F] font-semibold" : ""}
            >
              Home
            </Link>
            <Link
              to="/course"
              onClick={() => setShowMobileMenu(false)}
              className={
                isActive("/course") ? "text-[#009F9F] font-semibold" : ""
              }
            >
              Courses
            </Link>
            <Link
              to="/about"
              onClick={() => setShowMobileMenu(false)}
              className={
                isActive("/about") ? "text-[#009F9F] font-semibold" : ""
              }
            >
              About Us
            </Link>
            <Link
              to="/book"
              onClick={() => setShowMobileMenu(false)}
              className={
                isActive("/book") ? "text-[#009F9F] font-semibold" : ""
              }
            >
              Books
            </Link>
            <Link
              to="/patent-museum"
              onClick={() => setShowMobileMenu(false)}
              className={
                isActive("/patent-museum") ? "text-[#009F9F] font-semibold" : ""
              }
            >
              Patent Museum
            </Link>
            <Link
              to="/insights"
              onClick={() => setShowMobileMenu(false)}
              className={
                isActive("/insights") ? "text-[#009F9F] font-semibold" : ""
              }
            >
              Insights
            </Link>
            <Link
              to="/faq"
              onClick={() => setShowMobileMenu(false)}
              className={
                isActive("/faq") ? "text-[#009F9F] font-semibold" : ""
              }
            >
              FAQ
            </Link>
            <Link
              to="/contact"
              onClick={() => setShowMobileMenu(false)}
              className={
                isActive("/contact") ? "text-[#009F9F] font-semibold" : ""
              }
            >
              Contact Us
            </Link>
            {user ? (
              <div className="border-t border-gray-200 pt-3 mt-2">
                <div className="px-2 py-3 text-sm text-gray-700">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-10 h-10 rounded-full bg-[#009F9F] text-white flex items-center justify-center text-base font-bold">
                      {(
                        user.firstName ||
                        user.username ||
                        user.email?.split("@")[0]
                      )
                        ?.charAt(0)
                        .toUpperCase()}
                    </div>
                    <div className="overflow-hidden">
                      <p className="font-semibold truncate">
                        {user.firstName ||
                          user.username ||
                          user.email?.split("@")[0]}
                      </p>
                      <p className="text-xs text-gray-500 truncate max-w-[200px]">
                        {user.email}
                      </p>
                    </div>
                  </div>
                  <Link
                    to="/profile"
                    className="text-xs text-[#009F9F] hover:underline block mb-2"
                    onClick={() => setShowMobileMenu(false)}
                  >
                    View Profile
                  </Link>
                </div>
                <Link
                  to="/dashboard"
                  onClick={() => setShowMobileMenu(false)}
                  className="flex items-center px-2 py-2 text-gray-700 hover:bg-gray-100"
                >
                  <LayoutDashboard className="w-4 h-4 mr-2" />
                  Dashboard
                </Link>
                <button
                  onClick={handleLogout}
                  className="w-full text-left flex items-center px-2 py-2 text-gray-700 hover:bg-gray-100"
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  Logout
                </button>
              </div>
            ) : (
              <Button text="Login" to="/login" />
            )}
          </nav>
        </div>
      </header>
    </>
  );
};

export default Header;
