import React, { useState, useEffect } from "react";
import TestimonialImg from "../assets/TestimonialImg.png";
import herobg from "../assets/herobg.png";
import heroImg from "../assets/heroImg.png";
import imagebg2 from "../assets/imagebg2.png";
import imagebg1 from "../assets/imagebg1.svg";
import { Link } from "react-router-dom";
const HeroSection = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const testimonials = [
    {
      id: 0,
      name: "<PERSON>",
      image: TestimonialImg,
      text: "<PERSON> drafted my patent application, which was quickly issued. Some five years later, when an office action was issued against another application.",
    },
    {
      id: 1,
      name: "<PERSON>",
      image: TestimonialImg,
      text: "The patent strategy session helped me understand how to protect my invention. The guidance was clear and the process was smoother than I expected.",
    },
    {
      id: 2,
      name: "<PERSON>",
      image: TestimonialImg,
      text: "Working with PatentPath made the complex patent filing process straightforward. Their expertise in the field is evident in every step of the journey.",
    },
  ];
  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };
  const prevTestimonial = () => {
    setCurrentTestimonial(
      (prev) => (prev - 1 + testimonials.length) % testimonials.length,
    );
  };
  const goToTestimonial = (index) => {
    setCurrentTestimonial(index);
  };
  useEffect(() => {
    const intervalId = setInterval(() => {
      nextTestimonial();
    }, 5000);
    return () => clearInterval(intervalId);
  }, []);
  return (
    <section className="h-[820px] min-h-[918px] md:min-h-[700px] lg:min-h-[820px] relative overflow-hidden">
      {}
      <img src={herobg} alt="" className="absolute h-full w-full " />
      <div className="container mx-auto relative h-full">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 lg:gap-16 py-4 px-4 md:px-0 lg:px-0 md:py-20 h-full">
          {}
          <div className="text-white flex flex-col gap-[24px] mt-8 pt-18 sm:pt-16 md:pt-0 md:mt-0">
            <h2 className="relative text-3xl sm:text-4xl font-bold mb-4 z-10">
              Navigate Your Path To
              <br />
              Patent Success
            </h2>
            <p className="relative text-[15px] sm:text-[16px] text-[#E7EBEF] mb-4 z-10 max-w-xl">
              Embark on your journey to patent success with PatentPath. Our
              customized strategies, from idea protection to patent filing,
              equip you with the tools for innovative achievement.
            </p>
            <div className="flex flex-wrap gap-4 mb-8 lg:mb-12">
              <Link
                to="/course"
                className="bg-white text-[#009F9F] px-4 sm:px-6 py-2 sm:py-3 rounded-md font-medium hover:bg-gray-100 transition-colors"
              >
                See Our Courses
              </Link>
              <Link
                to="/contact"
                className="border border-white text-white px-4 sm:px-6 py-2 sm:py-3 rounded-md font-medium hover:bg-white hover:text-[#009F9F] transition-colors"
              >
                Send Message
              </Link>
            </div>
            {}
            <div className="relative w-full max-w-[420px]">
              {}
              <div className="flex flex-col gap-2">
                {}
                <div className="flex items-start gap-2 sm:gap-3">
                  {}
                  <img
                    src={testimonials[currentTestimonial].image}
                    alt={testimonials[currentTestimonial].name}
                    className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-full flex-shrink-0"
                  />
                  {}
                  <div>
                    <p className="text-[16px] text-[#FFFFFF] font-semibold">
                      {testimonials[currentTestimonial].name}
                    </p>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <svg
                          key={i}
                          className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-400 fill-current"
                          viewBox="0 0 20 20"
                        >
                          <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
                        </svg>
                      ))}
                    </div>
                  </div>
                </div>
                {}
                <p className="text-sm sm:text-base text-[#E7EBEF] mt-2 min-h-[60px] sm:min-h-[80px]">
                  "{testimonials[currentTestimonial].text}"
                </p>
                {}
                <div className="flex space-x-2 mt-4">
                  {testimonials.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => goToTestimonial(index)}
                      className={`h-1 flex-1 rounded-full transition-opacity cursor-pointer ${
                        index === currentTestimonial
                          ? "bg-white"
                          : "bg-white opacity-30"
                      }`}
                      aria-label={`Go to testimonial ${index + 1}`}
                    />
                  ))}
                </div>
              </div>
              {}
              <div className="hidden">
                <button
                  onClick={prevTestimonial}
                  aria-label="Previous testimonial"
                >
                  Previous
                </button>
                <button onClick={nextTestimonial} aria-label="Next testimonial">
                  Next
                </button>
              </div>
            </div>
          </div>
          {}
          <div className="relative hidden lg:flex items-center justify-center h-full">
            {}
            <div className="relative">
              {}
              <img
                src={imagebg1}
                alt=""
                className="absolute -top-[60px] -right-[40px] w-[149px] h-[149px]"
              />
              {}
              <img
                src={heroImg}
                alt="USPTO Building"
                className="relative z-20 max-w-full w-[480px] md:w-[460px] lg:w-[480px] xl:w-[580px] h-auto max-h-[728px] rounded-lg"
              />
              {}
              <img
                src={imagebg2}
                alt=""
                className="absolute z-51 bottom-[250px] -left-[54px] w-[149px] h-[149px]"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
export default HeroSection;
