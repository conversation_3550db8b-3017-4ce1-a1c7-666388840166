import React from "react";
const MapWithInfo = ({ location }) => {
  return (
    <div className="relative h-full w-full">
      <iframe
        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d96744.92362201144!2d-74.35252565214216!3d40.73388900134723!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c3ac5e7c8eda87%3A0xa16429423fa5244f!2sThe%20Map%20Building%2C%20515%20Valley%20St%2C%20Maplewood%2C%20NJ%2007040%2C%20USA!5e0!3m2!1sen!2s!4v1744893357295!5m2!1sen!2s"
        width="100%"
        height="100%"
        allowFullScreen=""
        loading="lazy"
        className="absolute inset-0"
      ></iframe>
      {}
      <div className="absolute top-4 sm:top-6 md:top-10 right-4 sm:right-6 md:right-10 bg-white p-3 sm:p-4 rounded-lg shadow-md max-w-[200px] sm:max-w-[250px] z-10">
        <h3 className="font-semibold text-gray-900 text-sm sm:text-base">
          {location.name}
        </h3>
        <p className="text-xs sm:text-sm text-gray-600 mt-1">
          {location.address}
          <br />
          {location.city}
          <br />
          {location.state} {location.zip}
          <br />
          {location.country}
        </p>
        {location.phone && (
          <p className="text-xs sm:text-sm text-gray-800 mt-2 sm:mt-4">
            {location.phone}
          </p>
        )}
        <a
          href={location.mapUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="mt-2 inline-flex items-center text-xs sm:text-sm text-[#009F9F] font-medium"
        >
          Directions →
        </a>
      </div>
    </div>
  );
};
export default MapWithInfo;
