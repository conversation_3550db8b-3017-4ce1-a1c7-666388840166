import React, { useState } from "react";
import TestimonialImage from "../assets/TestimonialImg.png";
import Icon1 from "../assets/icon1.svg";
import Icon2 from "../assets/icon2.svg";
import { Link } from "react-router-dom";
const MasterExamSection = () => {
  const [activeTab, setActiveTab] = useState(0);
  const tabs = [
    {
      title: "Master the skills that matter to you",
      content:
        "A fast-track program to help you pass the USPTO Patent Bar Exam, featuring focused lessons, practice questions, and test-day strategies.",
    },
    {
      title: "Connect with effective methods",
      content: "Content for connecting with effective methods.",
    },
    {
      title: "Increase your learning skills",
      content: "Content for increasing your learning skills.",
    },
  ];
  return (
    <section className="bg-white overflow-hidden py-20 px-4">
      <div className="container  rlative h-full mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          {}
          <div className="relative">
            {}
            <div className="absolute -top-20 left-4 w-[108px] h-[108px] ">
              <img
                src={Icon1}
                alt="Decorative icon"
                className="w-full h-full relative"
              />
            </div>
            <div className="space-y-4 relative z-0">
              {tabs.map((tab, index) => (
                <div
                  key={index}
                  onClick={() => setActiveTab(index)}
                  className={`p-6 rounded-lg cursor-pointer transition-colors duration-300 ${
                    activeTab === index
                      ? "bg-gradient-to-b from-[#E6FFFF] to-[#F7F7F8] shadow-md"
                      : "bg-white border border-gray-200 hover:bg-gray-50"
                  }`}
                >
                  <h3 className="font-semibold text-lg text-gray-900 mb-2">
                    {tab.title}
                  </h3>
                  {activeTab === index && (
                    <p className="text-sm text-gray-600">{tab.content}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
          {}
          <div className="relative">
            <div className="relative z-0">
              <p className="text-sm text-[#54585C] mb-2 text-[20px]">
                Guaranteed and certified
              </p>
              <h2 className="text-3xl lg:text-[40px] font-bold text-[#000000] mb-4">
                Master the USPTO Exam
              </h2>
              <p className="text-[#54585C] mb-6 text-[20px] leading-relaxed">
                Elevate your USPTO exam preparation with our comprehensive and
                engaging study solutions. Our tailored services combine advanced
                gamification, expert guidance, and a wealth of resources to help
                you excel. Ready to take the next step toward becoming a patent
                attorney?
              </p>
              <div className="flex items-center mb-8">
                <div className="flex -space-x-2 mr-3">
                  <img
                    className="inline-block h-10 w-10 rounded-full ring-2 ring-white"
                    src={TestimonialImage}
                    alt="Expert 1"
                  />
                  <img
                    className="inline-block h-10 w-10 rounded-full ring-2 ring-white"
                    src={TestimonialImage}
                    alt="Expert 2"
                  />
                </div>
                <p className="text-[20px] text-gray-700">
                  See Our{" "}
                  <a
                    href="#"
                    className="font-semibold text-gray-900 underline hover:text-[#009F9F]"
                  >
                    Experts
                  </a>
                </p>
              </div>
              <div className="relative inline-block z-0">
                <Link
                  to="/book"
                  className="inline-block bg-[#009F9F] text-white font-semibold py-5 px-8 rounded-md hover:bg-[#018A8A] transition duration-300"
                >
                  Start Preparation
                </Link>
                {}
                <div className="absolute top-[3%] left-[165px] w-[120px] h-[80px]">
                  <img
                    src={Icon2}
                    alt="Decorative icon"
                    className="w-full h-full relative -z-[10]"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
export default MasterExamSection;
