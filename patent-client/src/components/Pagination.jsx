import React, { useState } from "react";
import Button from "./Button";
const Pagination = ({
  totalBooks,
  booksPerPage,
  paginate,
  currentPage,
  setCurrentPage,
}) => {
  const totalPages = Math.ceil(totalBooks / booksPerPage);
  const canLoadMore = currentPage < totalPages;
  const [isShowingAll, setIsShowingAll] = useState(false);
  const handleShowMore = () => {
    paginate(currentPage + 1);
  };
  const handleShowLess = () => {
    setCurrentPage(1);
    setIsShowingAll(false);
  };
  return (
    <div className="pagination flex justify-center mx-4 mt-4">
      {canLoadMore && !isShowingAll ? (
        <Button
          text="Load More"
          secondary
          onClick={handleShowMore}
          className="w-full md:w-auto"
        />
      ) : (
        <>
          <Button
            text={isShowingAll ? "Show Less" : "Load More"}
            secondary
            onClick={isShowingAll ? handleShowLess : handleShowMore}
            className="w-full md:w-auto"
          />
        </>
      )}
    </div>
  );
};
export default Pagination;
