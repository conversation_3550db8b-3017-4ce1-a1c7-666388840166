import React from 'react'

const PatentMuseumBanner = () => {
  return (
    <div className="shop-container relative overflow-hidden">
    <section className="banner overflow-hidden bg-[#009F9F] pt-40 py-16 px-4 md:py-20 gap-4 text-white text-center relative">
        {/* Top-right SVG */}
        <svg
          width="277"
          height="291"
          viewBox="0 0 277 291"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="absolute bottom-30 -right-20 md:bottom-20 md:right-0 opacity-10 transform scale-60 md:scale-100 z-0"
        >
          <circle
            cx="238.543"
            cy="53.3657"
            r="187.562"
            stroke="black"
            strokeWidth="100"
          />
        </svg>

        {/* Bottom-right SVG */}
        <svg
          width="277"
          height="291"
          viewBox="0 0 277 291"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="absolute top-60 -left-20 md:top-20 md:left-0 opacity-10 rotate-180 transform scale-60 md:scale-100 z-0"
        >
          <circle
            cx="238.543"
            cy="53.3657"
            r="187.562"
            stroke="black"
            strokeWidth="100"
          />
        </svg>

        <div className="flex justify-center items-center max-w-4xl mx-auto">
          <div className="text-center">
            <h2 className="relative text-4xl font-bold mb-4 z-10">Explore the History of<br/> Innovation</h2>
            <p className="relative text-xl mb-4 z-10">
            Discover how patent certificates have evolved across time and borders
            </p>
          </div>
        </div>
      </section>
      </div>
  )
}

export default PatentMuseumBanner