"use client"

import { useState } from "react"
import { X } from "lucide-react"
import certificate from "../../assets/certificate.png"

const PatentMuseumCertificate = () => {
  const [selectedPeriod, setSelectedPeriod] = useState("1790s")
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const timelineData = [
    {
      period: "1790s",
      title: "Letters Patent signed by <PERSON>",
      issuedBy: "Secretary of State, Attorney General, and President",
      description:
        "The very first U.S. patent was handwritten, elegant, and signed personally by <PERSON>. It granted exclusive rights to <PERSON> for a method of producing potash. ",
      image: certificate, // Update this path to your actual image path
    },
    {
      period: "19th Century",
      title: "19th Century Patent Certificate",
      issuedBy: "Patent Office",
      description:
        "During the 19th century, patent certificates became more standardized as the patent system evolved. These certificates featured more detailed illustrations and specifications.",
      image: certificate, // Update with actual image
    },
    {
      period: "20th Century",
      title: "20th Century Patent Certificate",
      issuedBy: "United States Patent Office",
      description:
        "The 20th century saw further standardization of patent certificates with modern printing techniques and more formal layouts.",
      image: certificate, // Update with actual image
    },
    {
      period: "Today",
      title: "Modern Patent Certificate",
      issuedBy: "United States Patent and Trademark Office",
      description:
        "Today's patent certificates are digitally produced with advanced security features and standardized formats, though they maintain some traditional design elements.",
      image: certificate, // Update with actual image
    },
  ]

  const currentItem = timelineData.find((item) => item.period === selectedPeriod) || timelineData[0]

  // Handle dialog open/close
  const openDialog = () => setIsDialogOpen(true)
  const closeDialog = () => setIsDialogOpen(false)

  return (
    <div className="py-12 px-4 md:px-4 lg:px-0 container mx-auto">
      <h2 className="text-center text-4xl mb-8 font-bold tracking-tight">
        Patent Certificate Evolution in the
        <span className="text-[#008282] block mt-1">United State</span>
      </h2>

      <div className="flex flex-col md:flex-col lg:flex-col xl:flex-row gap-6 md:gap-10 mb-10">
        {/* Timeline navigation */}
        <div className="w-full md:w-full lg:w-full xl:w-[150px] flex flex-row md:flex-row lg:flex-row xl:flex-col justify-center md:justify-center lg:justify-center xl:justify-start overflow-x-auto md:overflow-x-auto lg:overflow-x-auto xl:overflow-visible xl:mr-[60px] xl:mt-[80px] xl:pt-5">
          <div className="flex flex-row md:flex-row lg:flex-row xl:flex-col">
            {timelineData.map((item) => (
              <button
                key={item.period}
                className={`text-[16px] p-2 text-left whitespace-nowrap ${
                  selectedPeriod === item.period ? "text-gray-600 font-medium  " : "text-[#54585C] border-transparent"
                }`}
                onClick={() => setSelectedPeriod(item.period)}
              >
                {item.period}
              </button>
            ))}
          </div>
        </div>

        {/* Content area */}
        <div className="flex-1 flex flex-col md:flex-col lg:flex-col xl:flex-row gap-8 md:gap-8 lg:gap-8 xl:gap-18 items-center md:items-center lg:items-center xl:items-start xl:ml-[200px] lg:ml-0 md:ml-0 ml-0">
          {/* Certificate image - clickable to open dialog */}
          <div className="cursor-pointer inline-block mx-auto md:mx-auto lg:mx-auto xl:mx-0" onClick={openDialog}>
            <img
              src={currentItem.image || "/placeholder.svg"}
              alt={`${currentItem.period} Patent Certificate`}
              className="w-full max-w-[446px] h-auto md:h-auto lg:h-auto xl:h-[344px] object-contain"
            />
          </div>

          {/* Description */}
          <div className="flex-1 mt-6 md:mt-6 lg:mt-6 xl:mt-[60px] max-w-[500px] text-center md:text-center lg:text-center xl:text-left">
            <h3 className="text-[24px] mb-3 font-semibold leading-tight">{currentItem.title}</h3>
            <p className="mb-3">
              <span className="font-bold">Issued By:</span>{" "}
              <span className="text-gray-600 text-[16px]">{currentItem.issuedBy}</span>
            </p>
            <p className="text-gray-600 text-[16px] leading-[1.6] tracking-[0.015em]">{currentItem.description}</p>
          </div>
        </div>
      </div>

      {/* Custom Dialog/Popover */}
      {isDialogOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-[90vw] md:max-w-[1412px] max-h-[90vh] overflow-auto">
            <div className="p-4 border-b flex justify-between items-center">
              <h3 className="text-lg font-medium">Patent Certificate</h3>
              <button onClick={closeDialog} className="rounded-sm opacity-70 hover:opacity-100 focus:outline-none">
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </button>
            </div>

            <div className="flex flex-col md:flex-col lg:flex-col xl:flex-row p-6">
              {/* Timeline navigation in dialog */}
              <div className="w-full md:w-full lg:w-full xl:w-[150px] flex flex-row md:flex-row lg:flex-row xl:flex-col justify-center md:justify-center lg:justify-center xl:justify-start overflow-x-auto md:overflow-x-auto lg:overflow-x-auto xl:overflow-visible xl:mr-[60px] xl:mt-[80px] xl:pt-5">
                <div className="flex flex-row md:flex-row lg:flex-row xl:flex-col">
                  {timelineData.map((item) => (
                    <button
                      key={item.period}
                      className={`text-[16px] p-2 text-left whitespace-nowrap ${
                        selectedPeriod === item.period
                          ? "text-gray-600 font-medium "
                          : "text-[#54585C] border-transparent "
                      }`}
                      onClick={() => setSelectedPeriod(item.period)}
                    >
                      {item.period}
                    </button>
                  ))}
                </div>
              </div>

              <div className="flex-1 flex flex-col md:flex-col lg:flex-col xl:flex-row gap-8 md:gap-8 lg:gap-8 xl:gap-14 items-center md:items-center lg:items-center xl:items-start xl:ml-[100px] lg:ml-0 md:ml-0 ml-0">
                <div className="inline-block mx-auto md:mx-auto lg:mx-auto xl:mx-0">
                  <img
                    src={currentItem.image || "/placeholder.svg"}
                    alt={`${currentItem.period} Patent Certificate`}
                    className="w-full max-w-[446px] h-auto object-contain border-4 border-[#1b4d4d] rounded"
                  />
                </div>

                <div className="flex-1 mt-6 md:mt-6 lg:mt-6 xl:mt-[40px] max-w-[494px] text-center md:text-center lg:text-center xl:text-left">
                  <h3 className="text-2xl mb-4 font-semibold">{currentItem.title}</h3>
                  <p className="mb-2">
                    <span className="font-bold">Issued By:</span>{" "}
                    <span className="text-gray-600 text-[16px]">{currentItem.issuedBy}</span>
                  </p>

                  <p className="leading-relaxed text-gray-600 text-[16px] max-w-[494px]" style={{ lineHeight: "140%" }}>
                    {currentItem.description}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default PatentMuseumCertificate
