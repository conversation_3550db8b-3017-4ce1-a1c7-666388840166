// src/components/ReviewCard.jsx
import React from "react";
const ReviewCard = ({ name, image, review, stars = 5 }) => {
  const renderStars = () => {
    const filledStars = "★".repeat(stars);
    const emptyStars = "☆".repeat(5 - stars);
    return filledStars + emptyStars;
  };
  return (
    <div className="bg-gray-100 p-6 rounded-lg border border-gray-200 w-full md:w-1/3">
      <div className="flex items-center gap-3 mb-3">
        <img
          src={image}
          alt={name}
          className="w-16 h-16 rounded-full object-cover"
        />
        <div className="flex flex-col">
          <h4 className="font-semibold text-md">{name}</h4>
          <div className="text-yellow-500 text-md tracking-wide">
            {renderStars()}
          </div>
        </div>
      </div>
      <p className="text-sm mt-2 text-gray-700">{review}</p>
    </div>
  );
};
export default ReviewCard;
