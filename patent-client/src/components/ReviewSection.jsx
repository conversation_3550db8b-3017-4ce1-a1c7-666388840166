import React from "react";
import ReviewCard from "./ReviewCard";
import Review1 from "../assets/reviewimage1.jpg";
import Review2 from "../assets/reviewimage2.jpg";
import Review3 from "../assets/reviewimage3.jpg";
const reviews = [
  {
    name: "<PERSON>",
    image: Review1,
    stars: 5,
    review:
      "<PERSON> drafted my patent application, which was quickly issued. Some five years later, when an office action was issued against another application, I searched for and found <PERSON> to draft the response for me. I was confident he would do the best job and he did.",
  },
  {
    name: "Lina <PERSON>",
    image: Review2,
    stars: 5,
    review:
      "I had a fantastic experience with <PERSON> when he drafted my patent application. It was approved in no time! Fast forward five years, and I faced an office action on a different application. I immediately reached out to <PERSON> again to handle the response, and I knew he would deliver exceptional work—and he certainly did!",
  },
  {
    name: "<PERSON>",
    image: Review3,
    stars: 5,
    review:
      "I just finished writing a book and hired a graphic designer to illustrate it. Mr. <PERSON><PERSON> drafted a contract for us both to sign and it was excellent! He created several clauses in the contract that addressed issues that I would not have even considered. He definitely knows how to think outside the box.",
  },
];
const ReviewSection = ({ className = "", heading, subtext }) => {
  return (
    <section className={`relative z-20 px-4 ${className}`}>
      <div className="max-w-6xl mx-auto">
        {}
        {(heading || subtext) && (
          <div className="text-center mb-10">
            {heading && (
              <h2 className="text-3xl md:text-4xl font-bold mb-2">{heading}</h2>
            )}
            {subtext && (
              <p className="text-gray-600 max-w-xl mx-auto">{subtext}</p>
            )}
          </div>
        )}
        {}
        <div className="flex flex-col md:flex-row justify-center gap-4">
          {reviews.map((r, i) => (
            <ReviewCard
              key={i}
              name={r.name}
              image={r.image}
              stars={r.stars}
              review={r.review}
            />
          ))}
        </div>
      </div>
    </section>
  );
};
export default ReviewSection;
