import { useState } from "react"
import Button from "./Button"
import Why1 from "../assets/Whywe1.png"
import Why2 from "../assets/WhyWe2.png"
import Why3 from "../assets/WhyWe3.jpg"
import arrowLeft from "../assets/arrowLeft.svg"
import arrowRight from "../assets/arrowRight.svg"
// import imagebg4 from "../assets/imagebg4.png"
// import imagebg3 from "../assets/imagebg3.png"

const slides = [
  {
    image: Why1,
    title: "Expert Patent Exam Preparation in Maplewood",
    desc: "Patent Pioneer Institute, based in Maplewood, specializes in comprehensive patent exam preparation, helping aspiring patent attorneys pass the USPTO exam.",
  },
  {
    image: Why2,
    title: "Tailored Learning Experiences for Patent Exam Success",
    desc: "We stand out with our unique approach to USPTO exam preparation, combining advanced AI personalization with gamification and a robust mobile app.",
  },
  {
    image: Why3,
    title: "Unlock Your Potential With Patent Exam Mastery",
    desc: "The Patent Pioneer Institute offers a cutting-edge educational experience that goes beyond traditional learning methods. Our emphasis on interactive tools makes a difference.",
  },
]

const WhyWeSection = () => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [animate, setAnimate] = useState(false)

  const handleSlide = (direction) => {
    setAnimate(true)
    setTimeout(() => {
      setCurrentIndex((prev) =>
        direction === "next" ? (prev + 1) % slides.length : (prev - 1 + slides.length) % slides.length,
      )
      setAnimate(false)
    }, 300)
  }

  const centerIndex = currentIndex

  return (
    <section className="bg-gray-100 py-16  px-4">
      <div className="container mx-auto flex flex-col items-center text-center ">
        <h2 className="text-3xl md:text-4xl font-bold mb-4">Start Your Journey to Patent Exam Success</h2>
        <p className="text-gray-600 max-w-2xl mb-12">
          Get the expert support, personalized tools, and proven strategies you need to pass the USPTO patent exam with
          confidence.
        </p>

        {/* Desktop Layout */}
        <div className="hidden md:flex w-full justify-center items-center">
          <div className="flex w-full  flex-col lg:flex-row" >
            {/* Left side - Image */}
            <div className="w-full lg:w-1/2 relative flex justify-center mb-8 lg:mb-0 ">
              <div className=" w-[320px] h-[320px] lg:w-[400px] lg:h-[400px] rounded-full overflow-hidden">
                <img
                  src={slides[centerIndex].image || "/placeholder.svg"}
                  alt={slides[centerIndex].title}
                  className="w-full h-full object-cover "
                />
                {/* Decorative elements */}
                {/* <img
                  src={imagebg4 || "/placeholder.svg"}
                  alt=""
                  className="absolute top-16 left-[150px] w-24 lg:w-24 hidden lg:block -translate-x-1/4 -translate-y-1/4 z-50"
                />
                <img
                  src={imagebg3 || "/placeholder.svg"}
                  alt=""
                  className="absolute bottom-20 right-[100px] z-10  w-24 lg:w-24  hidden lg:block"
                /> */}
              </div>
            </div>

            {/* Right side - Content */}
            <div className="w-full lg:w-1/2 flex flex-col items-center lg:items-start text-center lg:text-left">
              <div className="flex items-center gap-2 mb-4">
                <button
                  onClick={() => handleSlide("prev")}
                  className="p-2 rounded-full bg-white hover:bg-gray-200 active:scale-90 transition"
                >
                  <img src={arrowLeft || "/placeholder.svg"} alt="" />
                </button>
                <button
                  onClick={() => handleSlide("next")}
                  className="p-2 rounded-full bg-white hover:bg-gray-200 active:scale-90 transition"
                >
                  <img src={arrowRight || "/placeholder.svg"} alt="" />
                </button>
              </div>
              <h3 className="text-2xl font-bold mb-4">{slides[centerIndex].title}</h3>
              <p className="text-gray-600 mb-6">{slides[centerIndex].desc}</p>
              <Button text="Learn More" secondary />
            </div>
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="md:hidden flex flex-col items-center gap-6 mt-10">
          <div className="relative w-[280px] h-[280px] rounded-full overflow-hidden">
            <img
              src={slides[centerIndex].image || "/placeholder.svg"}
              alt={slides[centerIndex].title}
              className="w-full h-full object-cover"
            />
            {/* Decorative elements */}
            {/* <img
              src={imagebg4 || "/placeholder.svg"}
              alt=""
              className="absolute top-0 left-0 w-16 h-16 -translate-x-1/4 -translate-y-1/4 z-50"
            />
            <img
              src={imagebg3 || "/placeholder.svg"}
              alt=""
              className="absolute bottom-0 right-0 w-12 h-12 translate-x-1/4 translate-y-1/4 z-50"
            /> */}
          </div>

          <div className="flex gap-4 mt-2">
            <button
              onClick={() => handleSlide("prev")}
              className="p-2 rounded-full bg-white hover:bg-gray-200 active:scale-90 transition"
            >
              <img src={arrowLeft || "/placeholder.svg"} alt="" />
            </button>
            <button
              onClick={() => handleSlide("next")}
              className="p-2 rounded-full bg-white hover:bg-gray-200 active:scale-90 transition"
            >
              <img src={arrowRight || "/placeholder.svg"} alt="" />
            </button>
          </div>

          <h3 className="text-xl font-bold px-2">{slides[centerIndex].title}</h3>
          <p className="text-gray-600 px-4">{slides[centerIndex].desc}</p>
          <Button text="Learn More" secondary />
        </div>
      </div>
    </section>
  )
}

export default WhyWeSection
