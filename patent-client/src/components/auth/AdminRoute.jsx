import React from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import Loader from "../layout/Loader";
const AdminRoute = ({ children }) => {
  const { user, loading } = useAuth();
  if (loading) {
    return <Loader section="auth" text="Checking permissions..." />;
  }
  if (!user) {
    return <Navigate to="/login" replace />;
  }
  if (!user.isAdmin) {
    return <Navigate to="/dashboard" replace />;
  }
  return children;
};
export default AdminRoute;
