import React from "react";
import { Link } from "react-router-dom";
import styled from "styled-components";
import { Card, Button } from "../../styles/StyledComponents";
import * as authService from "../../services/authService";
import { useToast } from "../../contexts/ToastContext";
const PromptContainer = styled.div`
  max-width: 600px;
  margin: 100px auto;
  text-align: center;
`;
const PromptCard = styled(Card)`
  padding: ${({ theme }) => theme.spacing.xl};
`;
const Title = styled.h1`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  color: ${({ theme }) => theme.colors.primary};
`;
const Message = styled.p`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  line-height: 1.6;
`;
const EmailHighlight = styled.span`
  font-weight: bold;
  color: ${({ theme }) => theme.colors.primary};
`;
const ButtonContainer = styled.div`
  margin-top: ${({ theme }) => theme.spacing.lg};
  display: flex;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing.md};
`;
const VerificationPrompt = ({ email, redirectPath }) => {
  const { addToast } = useToast();
  const handleResendVerification = async () => {
    try {
      await authService.resendVerificationEmail({ email });
      addToast(
        "Verification email has been resent. Please check your inbox.",
        "success",
        5000,
      );
    } catch (error) {
      console.error("Failed to resend verification email:", error);
      addToast(
        "Failed to resend verification email. Please try again.",
        "error",
        5000,
      );
    }
  };
  return (
    <PromptContainer>
      <PromptCard>
        <Title>Verify Your Email</Title>
        <Message>
          Thank you for registering with Patent Pioneer Institute! We've sent a
          verification email to <EmailHighlight>{email}</EmailHighlight>.
        </Message>
        <Message>
          Please check your inbox and click the verification link to activate
          your account. If you don't see the email, please check your spam
          folder.
        </Message>
        <ButtonContainer>
          <button
            className="bg-[#009F9F] hover:bg-[#018A8A] cursor-pointer text-white px-6 py-2 rounded-md w-full md:w-auto"
            onClick={handleResendVerification}
            variant="outline"
          >
            Resend Verification Email
          </button>
          <Button
            as={Link}
            to={redirectPath ? `/login?redirect=${redirectPath}` : "/login"}
          >
            Back to Login
          </Button>
        </ButtonContainer>
      </PromptCard>
    </PromptContainer>
  );
};
export default VerificationPrompt;
