import React, { useState, useEffect, useRef } from "react";
import { useNavigate, Link, useLocation } from "react-router-dom";
import styled, { keyframes } from "styled-components";
import * as authService from "../../services/authService";
import { useToast } from "../../contexts/ToastContext";
import { useAuth } from "../../contexts/AuthContext";
import { Card, Button } from "../../styles/StyledComponents";
const VerifyContainer = styled.div`
  max-width: 600px;
  margin: 100px auto;
  text-align: center;
`;
const VerifyCard = styled(Card)`
  padding: ${({ theme }) => theme.spacing.xl};
`;
const Title = styled.h1`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  color: ${({ theme }) => theme.colors.primary};
`;
const Message = styled.p`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  line-height: 1.6;
`;
const LoadingSpinner = styled.div`
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: ${({ theme }) => theme.colors.primary};
  animation: spin 1s ease-in-out infinite;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
`;
const SuccessIcon = styled.div`
  color: ${({ theme }) => theme.colors.success};
  font-size: 60px;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const ErrorIcon = styled.div`
  color: ${({ theme }) => theme.colors.error};
  font-size: 60px;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const ButtonContainer = styled.div`
  margin-top: ${({ theme }) => theme.spacing.lg};
`;
const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;
const StatusBanner = styled.div`
  background-color: ${({ theme, type }) =>
    type === "success"
      ? theme.colors.success
      : type === "error"
        ? theme.colors.error
        : theme.colors.primary};
  color: white;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-weight: 600;
  font-size: 18px;
  animation: ${fadeIn} 0.5s ease-in-out;
`;
const CustomToast = styled.div`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: ${({ theme, type }) =>
    type === "success"
      ? theme.colors.success
      : type === "error"
        ? theme.colors.error
        : theme.colors.primary};
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  min-width: 300px;
  animation: ${fadeIn} 0.5s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;
const CloseButton = styled.button`
  background: transparent;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  margin-left: 16px;
  opacity: 0.8;
  &:hover {
    opacity: 1;
  }
`;
const VerifyEmail = () => {
  const location = useLocation();
  const verificationAttempted = useRef(false);
  const searchParams = new URLSearchParams(location.search);
  const token = searchParams.get("token") || "";
  const [verifying, setVerifying] = useState(true);
  const [verified, setVerified] = useState(false);
  const [error, setError] = useState(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [showSuccessBanner, setShowSuccessBanner] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastType, setToastType] = useState("success");
  const { addToast } = useToast();
  const { updateCurrentUser, user } = useAuth();
  const navigate = useNavigate();
  useEffect(() => {
    if (user) {
      const userIsAdmin =
        user.isAdmin ||
        (user.user && user.user.isAdmin) ||
        (user.user && user.user.role === "Admin") ||
        user.role === "Admin";
      if (
        user.status === "active" ||
        (user.user && user.user.status === "active")
      ) {
        setVerified(true);
        setVerifying(false);
        setIsAdmin(userIsAdmin);
        setShowSuccessBanner(true);
      }
    }
  }, [user]);
  useEffect(() => {
    if (showToast) {
      const timer = setTimeout(() => {
        setShowToast(false);
      }, 10000);
      return () => clearTimeout(timer);
    }
  }, [showToast]);
  useEffect(() => {
    let isMounted = true;
    const verifyAccount = async () => {
      if (verificationAttempted.current) return;
      verificationAttempted.current = true;
      try {
        if (!token) {
          setVerifying(false);
          setError(
            "Invalid verification link. Please check your email and try again.",
          );
          return;
        }
        setVerifying(true);
        const verificationResponse = await authService.verifyAccount(token);
        if (!isMounted) return;
        setVerified(true);
        setShowSuccessBanner(true);
        setToastMessage(
          "Your email has been verified successfully! You are now logged in.",
        );
        setToastType("success");
        setShowToast(true);
        addToast(
          "Your email has been verified successfully! You are now logged in.",
          "success",
          10000,
        );
        const userData = verificationResponse;
        if (
          userData &&
          (userData.email || (userData.user && userData.user.email))
        ) {
          const userIsAdmin =
            userData.isAdmin ||
            (userData.user && userData.user.isAdmin) ||
            (userData.user && userData.user.role === "Admin") ||
            userData.role === "Admin";
          setIsAdmin(userIsAdmin);
          updateCurrentUser(userData);
        }
      } catch (err) {
        if (!isMounted) return;
        console.error("Verification failed:", err);
        setError(
          err.message ||
            "Verification failed. Please try again or contact support.",
        );
        setToastMessage("Verification failed. Please try again.");
        setToastType("error");
        setShowToast(true);
        addToast("Verification failed. Please try again.", "error");
      } finally {
        if (isMounted) {
          setVerifying(false);
        }
      }
    };
    if (token && !verificationAttempted.current) {
      verifyAccount();
    } else if (!token) {
      setVerifying(false);
      setError(
        "Invalid verification link. Please check your email and try again.",
      );
    }
    return () => {
      isMounted = false;
    };
  }, [token, addToast, navigate, updateCurrentUser]);
  const renderToast = () => {
    if (!showToast) return null;
    return (
      <CustomToast type={toastType}>
        <span>{toastMessage}</span>
        <CloseButton onClick={() => setShowToast(false)}>×</CloseButton>
      </CustomToast>
    );
  };
  if (verifying) {
    return (
      <VerifyContainer>
        {renderToast()}
        <VerifyCard>
          <Title>Email Verification</Title>
          <LoadingSpinner />
          <Message>Verifying your email address...</Message>
        </VerifyCard>
      </VerifyContainer>
    );
  }
  if (verified) {
    return (
      <VerifyContainer>
        {renderToast()}
        <VerifyCard>
          <Title>Email Verification</Title>
          {showSuccessBanner && (
            <StatusBanner type="success">
              Your email has been verified successfully!
            </StatusBanner>
          )}
          <SuccessIcon>✓</SuccessIcon>
          <Message>Your account is now active and you are logged in.</Message>
          <Message>
            {isAdmin
              ? "Click the button below to go to your dashboard."
              : "Click the button below to go to the home page."}
          </Message>
          <ButtonContainer>
            <Button
              as={Link}
              to={isAdmin ? "/dashboard" : "/"}
              style={{ padding: "12px 24px", fontSize: "16px" }}
            >
              {isAdmin ? "Go to Dashboard" : "Go to Home Page"}
            </Button>
          </ButtonContainer>
        </VerifyCard>
      </VerifyContainer>
    );
  }
  return (
    <VerifyContainer>
      {renderToast()}
      <VerifyCard>
        <Title>Email Verification</Title>
        <StatusBanner type="error">Verification Failed</StatusBanner>
        <ErrorIcon>✗</ErrorIcon>
        <Message>We couldn't verify your email: {error}</Message>
        <ButtonContainer>
          <Button
            as={Link}
            to="/"
            variant="outline"
            style={{
              marginRight: "10px",
              padding: "12px 24px",
              fontSize: "16px",
            }}
          >
            Go to Home
          </Button>
          <Button
            as={Link}
            to="/signup"
            style={{ padding: "12px 24px", fontSize: "16px" }}
          >
            Register Again
          </Button>
        </ButtonContainer>
      </VerifyCard>
    </VerifyContainer>
  );
};
export default VerifyEmail;
