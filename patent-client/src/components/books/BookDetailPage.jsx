import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useN<PERSON>gate, Link, useLocation } from "react-router-dom";
import styled from "styled-components";
import { <PERSON><PERSON>, Card, PageTitle } from "../../styles/StyledComponents";
import * as bookService from "../../services/bookService";
import CheckoutModal from "../stripe/CheckoutModal";
import Loader from "../layout/Loader";
import { useToast } from "../../contexts/ToastContext";
import { useAuth } from "../../contexts/AuthContext";
import { API_BASE_URL } from "../../config/config.js";
const BookDetailContainer = styled.div`
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  padding-top: 20px;
`;
const BookCard = styled(Card)`
  display: flex;
  flex-direction: column;
  overflow: hidden;
  @media (min-width: 768px) {
    flex-direction: row;
  }
`;
const BookImageContainer = styled.div`
  flex: 0 0 auto;
  @media (min-width: 768px) {
    width: 300px;
    max-width: 300px;
  }
`;
const BookImage = styled.img`
  width: 100%;
  height: 300px;
  object-fit: cover;
  @media (min-width: 768px) {
    height: 100%;
  }
`;
const BookContent = styled.div`
  padding: ${({ theme }) => theme.spacing.lg};
  flex: 1;
`;
const BookTitle = styled.h1`
  margin: 0 0 ${({ theme }) => theme.spacing.sm};
  font-size: 1.8rem;
  font-weight: bold;
`;
const BookDescription = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  line-height: 1.6;
`;
const BookMeta = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const MetaItem = styled.div`
  display: flex;
  margin-bottom: ${({ theme }) => theme.spacing.xs};
  strong {
    margin-right: ${({ theme }) => theme.spacing.sm};
    min-width: 120px;
  }
`;
const BookType = styled.span`
  background-color: ${({ theme, type }) =>
    type === "hardcopy"
      ? theme.colors.warning + "30"
      : theme.colors.success + "30"};
  color: ${({ theme, type }) =>
    type === "hardcopy" ? theme.colors.warning : theme.colors.success};
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
`;
const BookPrice = styled.div`
  font-size: 1.2rem;
  font-weight: bold;
  color: ${({ theme }) => theme.colors.primary};
  margin-top: ${({ theme }) => theme.spacing.md};
`;
const ActionButtons = styled.div`
  display: flex;
  margin-top: ${({ theme }) => theme.spacing.lg};
  gap: ${({ theme }) => theme.spacing.md};
`;
export const BackButton = styled(Button)`
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  border: 2px solid #009F9F;
  color: #009F9F;
  border-radius: 25px;
  padding: 8px 20px;
  transition: all 0.3s ease;
  margin-bottom: 20px;
  &:hover {
    background-color: #009F9F;
    color: white;
  }
  svg {
    width: 20px;
    height: 20px;
  }
  span {
    @media (max-width: 768px) {
      display: none;
    }
  }
  @media (max-width: 768px) {
    padding: 8px;
    border-radius: 50%;
  }
`;
export const BackArrow = styled.svg`
  width: 20px;
  height: 20px;
`;
const PurchasedLabel = styled.div`
  background-color: ${({ theme }) => theme.colors.success};
  color: white;
  padding: 10px 16px;
  border-radius: 4px;
  font-weight: bold;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  svg {
    width: 18px;
    height: 18px;
  }
`;
const BookDetailPage = () => {
  const { id } = useParams();
  const [book, setBook] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [userOwnsBook, setUserOwnsBook] = useState(false);
  const [checkoutModalOpen, setCheckoutModalOpen] = useState(false);
  const [paymentIntent, setPaymentIntent] = useState(null);
  const { addToast } = useToast();
  const navigate = useNavigate();
  const { user } = useAuth();
  const isAdmin = user && user.isAdmin;
  const location = useLocation();
  const fromDashboard = location.state?.fromDashboard;
  const returnPath = location.state?.returnPath;
  useEffect(() => {
    const fetchBook = async () => {
      try {
        setLoading(true);
        const bookResponse = await fetch(`${API_BASE_URL}/books/${id}`, {
          headers: user
            ? {
                Authorization: `Bearer ${user.accessToken || user.token}`,
              }
            : {},
        });
        if (!bookResponse.ok) {
          throw new Error(`Failed to fetch book: ${bookResponse.status}`);
        }
        const data = await bookResponse.json();
        setBook(data);
        if (!isAdmin && user) {
          try {
            const myBooksResponse = await fetch(`${API_BASE_URL}/books/my`, {
              headers: {
                Authorization: `Bearer ${user.accessToken || user.token}`,
              },
            });
            if (!myBooksResponse.ok) {
              throw new Error(
                `Failed to fetch user books: ${myBooksResponse.status}`,
              );
            }
            const myBooksData = await myBooksResponse.json();
            const myBooks = Array.isArray(myBooksData)
              ? myBooksData
              : myBooksData && myBooksData.books
                ? myBooksData.books
                : [];
            const bookIsOwned = myBooks.some((myBook) => myBook._id === id);
            setUserOwnsBook(bookIsOwned);
            try {
              const purchasesResponse = await fetch(
                `${API_BASE_URL}/purchases/my`,
                {
                  headers: {
                    Authorization: `Bearer ${user.accessToken || user.token}`,
                  },
                },
              );
              if (purchasesResponse.ok) {
                const purchasesData = await purchasesResponse.json();
                const purchases = Array.isArray(purchasesData)
                  ? purchasesData
                  : purchasesData && purchasesData.purchases
                    ? purchasesData.purchases
                    : [];
                const hasPurchased = purchases.some((purchase) => {
                  const purchaseBookId = purchase.bookId
                    ? typeof purchase.bookId === "object"
                      ? purchase.bookId._id
                      : purchase.bookId
                    : null;
                  return (
                    purchaseBookId === id && purchase.status === "Completed"
                  );
                });
                if (hasPurchased) {
                  setUserOwnsBook(true);
                }
              }
            } catch (err) {
              console.error("Error fetching user purchases:", err);
            }
          } catch (err) {
            console.error("Failed to check if user owns the book:", err);
          }
        } else if (isAdmin) {
          setUserOwnsBook(true);
        }
      } catch (err) {
        console.error("Failed to fetch book:", err);
        setError("Failed to load book details. The book may not exist.");
        addToast("Failed to load book details", "error");
      } finally {
        setLoading(false);
      }
    };
    fetchBook();
  }, [id, addToast, isAdmin, user]);
  const handleDelete = async () => {
    if (!window.confirm("Are you sure you want to delete this book?")) {
      return;
    }
    try {
      await bookService.deleteBook(id);
      addToast("Book deleted successfully", "success");
      navigate("/books");
    } catch (err) {
      console.error("Failed to delete book:", err);
      addToast("Failed to delete book", "error");
    }
  };
  const handlePurchase = async () => {
    try {
      setLoading(true);
      const paymentData = await bookService.createStripeCheckoutForBook(id);
      setPaymentIntent(paymentData);
      setCheckoutModalOpen(true);
    } catch (err) {
      console.error("Failed to initiate book purchase:", err);
      addToast("Failed to initiate purchase. Please try again.", "error");
    } finally {
      setLoading(false);
    }
  };
  const handleCheckoutSuccess = async (paymentResult) => {
    "Payment successful:", paymentResult;
    setCheckoutModalOpen(false);
    addToast(`Successfully purchased "${book.title}"`, "success");
    try {
      const myBooks = await bookService.getMyBooks();
      const bookIsOwned = myBooks.some((myBook) => myBook._id === id);
      if (bookIsOwned) {
        ("Book ownership confirmed from server");
      } else {
        ("Book not yet showing as owned, may need to refresh");
        try {
          await bookService.purchaseBook(id);
          ("Direct purchase completed as fallback");
        } catch (purchaseError) {
          console.error("Fallback purchase failed:", purchaseError);
        }
      }
      setUserOwnsBook(true);
    } catch (error) {
      console.error("Error verifying book ownership:", error);
      setUserOwnsBook(true);
    }
    setPaymentIntent(null);
  };
  const handleCheckoutError = (errorMessage) => {
    addToast(`Payment failed: ${errorMessage}`, "error");
  };
  const closeCheckoutModal = () => {
    setCheckoutModalOpen(false);
    setPaymentIntent(null);
  };
  const handleGoBack = () => {
    if (fromDashboard) {
      navigate("/dashboard");
    } else if (returnPath) {
      navigate(returnPath);
    } else {
      navigate(isAdmin ? "/books" : "/books/my");
    }
  };
  if (loading) {
    return <Loader section="books" />;
  }
  if (error) {
    return (
      <BookDetailContainer>
        <PageTitle>Book Details</PageTitle>
        <Card>
          <p>{error}</p>
          <BackButton variant="outline" onClick={handleGoBack}>
            <span>
              {fromDashboard
                ? "Back to Dashboard"
                : returnPath === "/books/my"
                  ? "Back to My Books"
                  : "Back to Books"}
            </span>
          </BackButton>
        </Card>
      </BookDetailContainer>
    );
  }
  return (
    <BookDetailContainer>
      <BackButton variant="outline" onClick={handleGoBack}>
        <BackArrow
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 19l-7-7 7-7"
          />
        </BackArrow>
        <span>
          {fromDashboard
            ? "Back to Dashboard"
            : returnPath === "/books/my"
              ? "Back to My Books"
              : "Back to Books"}
        </span>
      </BackButton>
      <PageTitle>Book Details</PageTitle>
      <BookCard>
        <BookImageContainer>
          <BookImage src={book.image} alt={book.title} />
        </BookImageContainer>
        <BookContent>
          <BookTitle>{book.title}</BookTitle>
          <BookType type={book.type}>
            {book.type === "hardcopy" ? "Hard Copy" : "Soft Copy"}
          </BookType>
          <BookDescription>{book.description}</BookDescription>
          <BookMeta>
            {book.author && (
              <MetaItem>
                <strong>Author:</strong> {book.author}
              </MetaItem>
            )}
            {book.publishedYear && (
              <MetaItem>
                <strong>Published Year:</strong> {book.publishedYear}
              </MetaItem>
            )}
            {book.publisher && (
              <MetaItem>
                <strong>Publisher:</strong> {book.publisher}
              </MetaItem>
            )}
          </BookMeta>
          <BookPrice>${parseFloat(book.price).toFixed(2)}</BookPrice>
          <ActionButtons>
            {isAdmin ? (
              <>
                <Button
                  as={Link}
                  to={`/books/${book._id}/edit`}
                  variant="primary"
                >
                  Edit Book
                </Button>
                <Button onClick={handleDelete} variant="danger">
                  Delete Book
                </Button>
              </>
            ) : userOwnsBook ? (
              <PurchasedLabel>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                Purchased
              </PurchasedLabel>
            ) : (
              <Button onClick={handlePurchase} variant="primary">
                Purchase with Stripe
              </Button>
            )}
          </ActionButtons>
        </BookContent>
      </BookCard>
      {}
      {book && paymentIntent && (
        <CheckoutModal
          isOpen={checkoutModalOpen}
          onClose={closeCheckoutModal}
          clientSecret={paymentIntent.clientSecret}
          onSuccess={handleCheckoutSuccess}
          onError={handleCheckoutError}
          bookTitle={book.title}
          amount={parseFloat(book.price).toFixed(2)}
        />
      )}
    </BookDetailContainer>
  );
};
export default BookDetailPage;
