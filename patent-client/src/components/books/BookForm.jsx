import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";
import {
  Form,
  FormGroup,
  Label,
  Input,
  Select,
  Button,
  Card,
} from "../../styles/StyledComponents";
import * as bookService from "../../services/bookService";
import { useToast } from "../../contexts/ToastContext";
const BookFormContainer = styled(Card)`
  padding: ${({ theme }) => theme.spacing.lg};
  max-width: 800px;
  margin: 0 auto;
`;
const ImagePreview = styled.div`
  margin-top: ${({ theme }) => theme.spacing.sm};
  position: relative;
  display: inline-block;
  img {
    max-width: 100%;
    max-height: 300px;
    border-radius: ${({ theme }) => theme.borderRadius.sm};
    border: 2px solid ${({ theme }) => theme.colors.primary};
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    &:hover {
      transform: scale(1.02);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }
  }
  &::after {
    content: "Book Cover Preview";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 8px;
    font-size: 0.9rem;
    border-bottom-left-radius: ${({ theme }) => theme.borderRadius.sm};
    border-bottom-right-radius: ${({ theme }) => theme.borderRadius.sm};
    text-align: center;
  }
`;
const PdfPreview = styled.div`
  margin-top: ${({ theme }) => theme.spacing.sm};
  padding: ${({ theme }) => theme.spacing.md};
  border: 1px solid ${({ theme }) => theme.colors.primary};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  background-color: rgba(0, 123, 255, 0.05);
  display: flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  svg {
    width: 28px;
    height: 28px;
    margin-right: ${({ theme }) => theme.spacing.sm};
    color: ${({ theme }) => theme.colors.primary};
  }
  span {
    font-weight: 500;
    color: #333;
    word-break: break-all;
  }
`;
const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: ${({ theme }) => theme.spacing.lg};
`;
const ErrorText = styled.div`
  color: ${({ theme }) => theme.colors.error};
  font-size: 0.9rem;
  margin-top: ${({ theme }) => theme.spacing.xs};
`;
const FileInputWrapper = styled.div`
  position: relative;
  margin-bottom: 8px;
`;
const FileInputLabel = styled.label`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 16px;
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: fit-content;
  svg {
    margin-right: 8px;
    width: 20px;
    height: 20px;
    vertical-align: middle;
  }
  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark || "#0056b3"};
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
  &:active {
    transform: translateY(1px);
  }
`;
const HiddenFileInput = styled.input`
  position: absolute;
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  z-index: -1;
`;
const SelectedFileName = styled.div`
  margin-top: 8px;
  font-size: 0.9rem;
  color: #666;
`;
const BookForm = ({ book, isEditing }) => {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    price: "",
    type: "hardcopy",
    image: null,
    pdfFile: null,
    author: "",
    publishedYear: "",
    publisher: "",
  });
  const [imagePreview, setImagePreview] = useState("");
  const [pdfFileName, setPdfFileName] = useState("");
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const { addToast } = useToast();
  const navigate = useNavigate();
  useEffect(() => {
    if (isEditing && book) {
      setFormData({
        title: book.title || "",
        description: book.description || "",
        price: book.price || "",
        type: book.type || "hardcopy",
        image: null,
        pdfFile: null,
        author: book.author || "",
        publishedYear: book.publishedYear || "",
        publisher: book.publisher || "",
      });
      setImagePreview(book.image || "");
      if (book.pdfFile) {
        const fileName = book.pdfFile.split("/").pop();
        setPdfFileName(fileName);
      }
    }
  }, [isEditing, book]);
  const validateForm = () => {
    const newErrors = {};
    if (!formData.title.trim()) newErrors.title = "Title is required";
    if (!formData.description.trim())
      newErrors.description = "Description is required";
    if (!formData.price) newErrors.price = "Price is required";
    else if (isNaN(formData.price) || Number(formData.price) < 0) {
      newErrors.price = "Price must be a non-negative number";
    }
    if (!isEditing && !formData.image && !imagePreview) {
      newErrors.image = "Image is required";
    }
    if (
      formData.type === "softcopy" &&
      !isEditing &&
      !formData.pdfFile &&
      !pdfFileName
    ) {
      newErrors.pdfFile = "PDF file is required for softcopy books";
    }
    if (
      formData.publishedYear &&
      (isNaN(formData.publishedYear) ||
        Number(formData.publishedYear) < 1800 ||
        Number(formData.publishedYear) > new Date().getFullYear())
    ) {
      newErrors.publishedYear = "Enter a valid publication year";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name === "type") {
      setErrors((prev) => ({ ...prev, pdfFile: undefined }));
    }
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };
  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData((prev) => ({
        ...prev,
        image: file,
      }));
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };
  const handlePdfChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.type !== "application/pdf") {
        setErrors((prev) => ({
          ...prev,
          pdfFile: "Only PDF files are allowed",
        }));
        return;
      }
      setFormData((prev) => ({
        ...prev,
        pdfFile: file,
      }));
      setPdfFileName(file.name);
      setErrors((prev) => ({ ...prev, pdfFile: undefined }));
    }
  };
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) {
      return;
    }
    try {
      setLoading(true);
      if (isEditing) {
        await bookService.updateBook(book._id, formData);
        addToast("Book updated successfully", "success");
      } else {
        await bookService.createBook(formData);
        addToast("Book created successfully", "success");
      }
      navigate("/books");
    } catch (error) {
      console.error("Error saving book:", error);
      let errorMessage = isEditing
        ? "Failed to update book. "
        : "Failed to create book. ";
      if (error.message) {
        errorMessage += error.message;
      } else {
        errorMessage += "Please try again.";
      }
      addToast(errorMessage, "error");
    } finally {
      setLoading(false);
    }
  };
  return (
    <BookFormContainer>
      <Form onSubmit={handleSubmit}>
        <FormGroup>
          <Label htmlFor="title">Title *</Label>
          <Input
            id="title"
            name="title"
            value={formData.title}
            onChange={handleChange}
            placeholder="Enter book title"
          />
          {errors.title && <ErrorText>{errors.title}</ErrorText>}
        </FormGroup>
        <FormGroup>
          <Label htmlFor="description">Description *</Label>
          <Input
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            as="textarea"
            rows={5}
            placeholder="Enter book description"
          />
          {errors.description && <ErrorText>{errors.description}</ErrorText>}
        </FormGroup>
        <FormGroup>
          <Label htmlFor="price">Price ($) *</Label>
          <Input
            id="price"
            name="price"
            type="number"
            step="0.01"
            min="0"
            value={formData.price}
            onChange={handleChange}
            placeholder="Enter price"
          />
          {errors.price && <ErrorText>{errors.price}</ErrorText>}
        </FormGroup>
        <FormGroup>
          <Label htmlFor="type">Book Type *</Label>
          <Select
            id="type"
            name="type"
            value={formData.type}
            onChange={handleChange}
          >
            <option value="hardcopy">Hard Copy</option>
            <option value="softcopy">Soft Copy</option>
          </Select>
          {errors.type && <ErrorText>{errors.type}</ErrorText>}
        </FormGroup>
        <FormGroup>
          <Label htmlFor="author">Author</Label>
          <Input
            id="author"
            name="author"
            value={formData.author}
            onChange={handleChange}
            placeholder="Enter author name"
          />
        </FormGroup>
        <FormGroup>
          <Label htmlFor="publishedYear">Publication Year</Label>
          <Input
            id="publishedYear"
            name="publishedYear"
            type="number"
            value={formData.publishedYear}
            onChange={handleChange}
            placeholder="Enter publication year"
          />
          {errors.publishedYear && (
            <ErrorText>{errors.publishedYear}</ErrorText>
          )}
        </FormGroup>
        <FormGroup>
          <Label htmlFor="publisher">Publisher</Label>
          <Input
            id="publisher"
            name="publisher"
            value={formData.publisher}
            onChange={handleChange}
            placeholder="Enter publisher name"
          />
        </FormGroup>
        <FormGroup>
          <Label htmlFor="image">Book Cover {!isEditing && "*"}</Label>
          <FileInputWrapper>
            <FileInputLabel htmlFor="image">
              <svg viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M4,4H7L9,2H15L17,4H20A2,2 0 0,1 22,6V18A2,2 0 0,1 20,20H4A2,2 0 0,1 2,18V6A2,2 0 0,1 4,4M12,7A5,5 0 0,0 7,12A5,5 0 0,0 12,17A5,5 0 0,0 17,12A5,5 0 0,0 12,7M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9Z"
                />
              </svg>
              Choose Image
            </FileInputLabel>
            <HiddenFileInput
              id="image"
              name="image"
              type="file"
              accept="image/*"
              onChange={handleImageChange}
            />
            {imagePreview ? (
              <SelectedFileName>
                Selected: {formData.image?.name || "Current image"}
              </SelectedFileName>
            ) : (
              <SelectedFileName>No image selected</SelectedFileName>
            )}
          </FileInputWrapper>
          {errors.image && <ErrorText>{errors.image}</ErrorText>}
          {imagePreview && (
            <ImagePreview>
              <img src={imagePreview} alt="Book cover preview" />
            </ImagePreview>
          )}
        </FormGroup>
        {formData.type === "softcopy" && (
          <FormGroup>
            <Label htmlFor="pdfFile">
              PDF File {!isEditing || (isEditing && !pdfFileName) ? "*" : ""}
            </Label>
            <FileInputWrapper>
              <FileInputLabel htmlFor="pdfFile">
                <svg viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20M10,19L10.9,19C11.5,19 12,18.5 12,17.9V16.5C12,15.95 11.55,15.5 11,15.5H10V19M10,13.5H11A1.5,1.5 0 0,0 12.5,12A1.5,1.5 0 0,0 11,10.5H10V13.5M8,10.5V19H9V10.5H8M16,19H17V13.5H16V19M14.5,17.5H15.5V16.25H14.5V17.5M14.5,15H15.5V13.75H14.5V15Z"
                  />
                </svg>
                Choose PDF
              </FileInputLabel>
              <HiddenFileInput
                id="pdfFile"
                name="pdfFile"
                type="file"
                accept="application/pdf"
                onChange={handlePdfChange}
              />
              {pdfFileName ? (
                <SelectedFileName>
                  Selected: {formData.pdfFile?.name || pdfFileName}
                </SelectedFileName>
              ) : (
                <SelectedFileName>No PDF selected</SelectedFileName>
              )}
            </FileInputWrapper>
            {errors.pdfFile && <ErrorText>{errors.pdfFile}</ErrorText>}
            {pdfFileName && (
              <PdfPreview>
                <svg viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M6,2A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2H6M6,4H13V9H18V20H6V4M8,12V14H16V12H8M8,16V18H13V16H8Z"
                  />
                </svg>
                <span>{pdfFileName}</span>
              </PdfPreview>
            )}
          </FormGroup>
        )}
        <ButtonGroup>
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate("/books")}
            disabled={loading}
            style={{
              padding: "10px 16px",
              height: "auto",
              minWidth: "100px",
              borderWidth: "2px",
            }}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={loading}
            style={{
              padding: "10px 24px",
              height: "auto",
              minWidth: "150px",
              fontWeight: "600",
              boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
              transition: "all 0.2s ease",
            }}
          >
            {loading ? (
              <span
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  style={{
                    marginRight: "8px",
                    animation: "spin 1s linear infinite",
                  }}
                >
                  <style>{`
                    @keyframes spin {
                      0% { transform: rotate(0deg); }
                      100% { transform: rotate(360deg); }
                    }
                  `}</style>
                  <path
                    fill="currentColor"
                    d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z"
                  />
                </svg>
                Saving...
              </span>
            ) : (
              <span
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  style={{ marginRight: "8px" }}
                >
                  <path
                    fill="currentColor"
                    d={
                      isEditing
                        ? "M21,10.12H14.22L16.96,7.3C14.23,4.6 9.81,4.5 7.08,7.2C4.35,9.91 4.35,14.28 7.08,17C9.81,19.7 14.23,19.7 16.96,17C18.32,15.65 19,14.08 19,12.1H21C21,14.08 20.12,16.65 18.36,18.39C14.85,21.87 9.15,21.87 5.64,18.39C2.14,14.92 2.11,9.28 5.62,5.81C9.13,2.34 14.76,2.34 18.27,5.81L21,3V10.12M12.5,8V12.25L16,14.33L15.28,15.54L11,13V8H12.5Z"
                        : "M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"
                    }
                  />
                </svg>
                {isEditing ? "Update Book" : "Create Book"}
              </span>
            )}
          </Button>
        </ButtonGroup>
      </Form>
    </BookFormContainer>
  );
};
export default BookForm;
