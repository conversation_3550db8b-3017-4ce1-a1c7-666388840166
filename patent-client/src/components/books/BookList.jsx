import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import styled from "styled-components";
import {
  PageTitle,
  Button,
  Card,
  Grid,
  Column,
  SearchInput,
} from "../../styles/StyledComponents";
import * as bookService from "../../services/bookService";
import CheckoutModal from "../stripe/CheckoutModal";
import ConfirmationModal from "../common/ConfirmationModal";
import Loader from "../layout/Loader";
import { useToast } from "../../contexts/ToastContext";
import { useAuth } from "../../contexts/AuthContext";
import Pagination from "../common/Pagination";
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  MenuBook as ReadIcon,
} from "@styled-icons/material";
const BookHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const SearchContainer = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const BookCard = styled(Card)`
  display: flex;
  flex-direction: column;
  height: 100%;
  transition:
    transform 0.2s,
    box-shadow 0.2s;
  padding: 0;
  margin-bottom: 1rem;
  &:hover {
    transform: translateY(-5px);
    box-shadow: ${({ theme }) => theme.shadows.lg};
    .details-button {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;
const BookImage = styled.img`
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: ${({ theme }) => theme.borderRadius.md}
    ${({ theme }) => theme.borderRadius.md} 0 0;
`;
const BookContent = styled.div`
  padding: ${({ theme }) => theme.spacing.sm};
  flex: 1;
  display: flex;
  flex-direction: column;
`;
const BookTitle = styled.h3`
  margin: 0 0 ${({ theme }) => theme.spacing.xs};
  font-size: 1.2rem;
  font-weight: bold;
`;
const BookDescription = styled.p`
  margin: 0 0 ${({ theme }) => theme.spacing.sm};
  color: ${({ theme }) => theme.colors.text};
  font-size: 0.875rem;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;
const BookMeta = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
`;
const BookType = styled.span`
  background-color: ${({ theme, type }) =>
    type === "hardcopy"
      ? theme.colors.warning + "30"
      : theme.colors.success + "30"};
  color: ${({ theme, type }) =>
    type === "hardcopy" ? theme.colors.warning : theme.colors.success};
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
`;
const BookPrice = styled.span`
  font-weight: bold;
  color: ${({ theme }) => theme.colors.primary};
`;
const BookFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.sm};
  border-top: 1px solid ${({ theme }) => theme.colors.border};
`;
const ActionButton = styled.button`
  background-color: transparent;
  border: none;
  color: ${({ theme, variant }) =>
    variant === "delete"
      ? theme.colors.danger
      : variant === "edit"
        ? theme.colors.primary
        : theme.colors.text};
  cursor: pointer;
  padding: ${({ theme }) => theme.spacing.xs};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
  svg {
    width: 20px;
    height: 20px;
  }
`;
const NoDataMessage = styled.div`
  text-align: center;
  margin: ${({ theme }) => theme.spacing.xl} 0;
  color: ${({ theme }) => theme.colors.text.secondary};
`;
const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colors.error};
  padding: ${({ theme }) => theme.spacing.md};
  border: 1px solid ${({ theme }) => theme.colors.error};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin: ${({ theme }) => theme.spacing.lg} 0;
`;
const StyledDetailButton = styled(Button)`
  opacity: 0;
  transform: translateY(10px);
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
  color: white;
  background-color: #009F9F;
  &:hover {
    color: white;
    background-color: #007A7A;
  }
`;
const StyledPurchaseButton = styled(Button)`
  &:hover {
    color: white;
  }
`;
const PurchasedLabel = styled.div`
  background-color: ${({ theme }) => theme.colors.success};
  color: white;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  svg {
    width: 14px;
    height: 14px;
  }
`;
const ReadButton = styled(Button)`
  background-color: ${({ theme }) => theme.colors.info};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 0.8rem;
  padding: 6px 12px;
  &:hover {
    background-color: ${({ theme }) => theme.colors.info}dd;
    color: white;
  }
  svg {
    width: 16px;
    height: 16px;
  }
`;
const BookList = () => {
  const [books, setBooks] = useState([]);
  const [filteredBooks, setFilteredBooks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [checkoutModalOpen, setCheckoutModalOpen] = useState(false);
  const [selectedBook, setSelectedBook] = useState(null);
  const [paymentIntent, setPaymentIntent] = useState(null);
  const [userBooks, setUserBooks] = useState([]);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [bookToDelete, setBookToDelete] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [loadingPdf, setLoadingPdf] = useState({});
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState(null);
  const { addToast } = useToast();
  const { user } = useAuth();
  const navigate = useNavigate();
  const isAdmin = user && (user.role === "Admin" || user.isAdmin === true);
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await bookService.getAllBooks(currentPage, 8);
        setBooks(response.books || []);
        setFilteredBooks(response.books || []);
        setPagination(response.pagination || null);
        if (!isAdmin && user) {
          const myBooksResponse = await bookService.getMyBooks();
          setUserBooks(
            Array.isArray(myBooksResponse)
              ? myBooksResponse
              : myBooksResponse.books || [],
          );
        }
        setError(null);
      } catch (err) {
        console.error("Failed to fetch data:", err);
        setError("Failed to load books. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [isAdmin, user, currentPage]);
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredBooks(books);
      return;
    }
    const searchTermLower = searchTerm.toLowerCase();
    const filtered = books.filter(
      (book) =>
        book.title.toLowerCase().includes(searchTermLower) ||
        book.description.toLowerCase().includes(searchTermLower) ||
        (book.author && book.author.toLowerCase().includes(searchTermLower)),
    );
    setFilteredBooks(filtered);
  }, [searchTerm, books]);
  const userOwnsBook = (bookId) => {
    return userBooks.some((book) => book._id === bookId);
  };
  const handleDeleteClick = (id) => {
    setBookToDelete(id);
    setDeleteModalOpen(true);
  };
  const handleDeleteConfirm = async () => {
    try {
      setIsDeleting(true);
      await bookService.deleteBook(bookToDelete);
      setBooks((prevBooks) =>
        prevBooks.filter((book) => book._id !== bookToDelete),
      );
      addToast("Book deleted successfully", "success");
    } catch (err) {
      console.error("Failed to delete book:", err);
      addToast("Failed to delete book", "error");
    } finally {
      setTimeout(() => {
        setIsDeleting(false);
        setDeleteModalOpen(false);
        setBookToDelete(null);
      }, 500);
    }
  };
  const handlePurchase = async (book) => {
    if (!user) {
      addToast("Please log in to complete your purchase", "info");
      navigate("/login?redirect=shop");
      return;
    }
    try {
      setLoading(true);
      const paymentData = await bookService.createStripeCheckoutForBook(
        book._id,
      );
      setSelectedBook(book);
      setPaymentIntent(paymentData);
      setCheckoutModalOpen(true);
    } catch (err) {
      console.error("Failed to initiate book purchase:", err);
      if (
        err.message &&
        (err.message.includes("No token provided") ||
          err.message.includes("Unauthorized") ||
          err.message.includes("401"))
      ) {
        addToast("Please log in to complete your purchase", "info");
        navigate("/login?redirect=shop");
      } else {
        addToast("Failed to initiate purchase. Please try again.", "error");
      }
    } finally {
      setLoading(false);
    }
  };
  const handleCheckoutSuccess = async () => {
    setCheckoutModalOpen(false);
    addToast(`Successfully purchased "${selectedBook.title}"`, "success");
    try {
      const myBooksData = await bookService.getMyBooks();
      setUserBooks(myBooksData);
      const bookIsOwned = myBooksData.some(
        (book) => book._id === selectedBook._id,
      );
      if (!bookIsOwned) {
        try {
          await bookService.purchaseBook(selectedBook._id);
          const updatedBooks = await bookService.getMyBooks();
          setUserBooks(updatedBooks);
        } catch (purchaseError) {
          console.error("Fallback purchase failed:", purchaseError);
          setUserBooks((prev) => {
            if (!prev.some((book) => book._id === selectedBook._id)) {
              return [...prev, selectedBook];
            }
            return prev;
          });
        }
      }
    } catch (error) {
      console.error("Error verifying book ownership:", error);
      setUserBooks((prev) => {
        if (!prev.some((book) => book._id === selectedBook._id)) {
          return [...prev, selectedBook];
        }
        return prev;
      });
    }
    setSelectedBook(null);
    setPaymentIntent(null);
  };
  const handleCheckoutError = (errorMessage) => {
    addToast(`Payment failed: ${errorMessage}`, "error");
  };
  const closeCheckoutModal = () => {
    setCheckoutModalOpen(false);
    setSelectedBook(null);
    setPaymentIntent(null);
  };
  const handleReadBook = async (bookId) => {
    try {
      setLoadingPdf((prev) => ({ ...prev, [bookId]: true }));
      const response = await bookService.getBookPdfUrl(bookId);
      if (response && response.pdfUrl) {
        const pdfUrl = response.pdfUrl;
        window.open(pdfUrl, "_blank");
        addToast("Opening PDF in a new tab", "success");
      } else {
        addToast("PDF file not available", "error");
      }
    } catch (error) {
      console.error("Error opening PDF:", error);
      addToast("Failed to open the book. Please try again.", "error");
    } finally {
      setLoadingPdf((prev) => ({ ...prev, [bookId]: false }));
    }
  };
  if (loading) {
    return <Loader section="books" />;
  }
  return (
    <>
      <BookHeader>
        <PageTitle>Books Shop</PageTitle>
        {isAdmin && (
          <Button as={Link} to="/books/new">
            <AddIcon size={20} style={{ marginRight: "8px" }} />
             New Book
          </Button>
        )}
      </BookHeader>
      <SearchContainer>
        <SearchInput
          type="text"
          placeholder="Search books by title, description, or author..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </SearchContainer>
      {error && <ErrorMessage>{error}</ErrorMessage>}
      {!error && filteredBooks.length === 0 && (
        <NoDataMessage>
          {searchTerm
            ? "No books found matching your search."
            : "No books available."}
        </NoDataMessage>
      )}
      <Grid columns={4} gap="lg">
        {filteredBooks.map((book) => (
          <Column key={book._id} span={1}>
            <BookCard>
              <BookImage src={book.image} alt={book.title} />
              <BookContent>
                <BookTitle>{book.title}</BookTitle>
                <BookDescription>{book.description}</BookDescription>
                <BookMeta>
                  <BookType type={book.type}>
                    {book.type === "hardcopy" ? "Hard Copy" : "Soft Copy"}
                  </BookType>
                  <BookPrice>${parseFloat(book.price).toFixed(2)}</BookPrice>
                </BookMeta>
              </BookContent>
              <BookFooter>
                <StyledDetailButton
                  as={Link}
                  to={`/books/${book._id}`}
                  variant="primary"
                  size="small"
                  className="details-button"
                >
                  View Details
                </StyledDetailButton>
                {isAdmin ? (
                  <div style={{ display: "flex" }}>
                    <ActionButton
                      as={Link}
                      to={`/books/${book._id}/edit`}
                      variant="edit"
                    >
                      <EditIcon />
                    </ActionButton>
                    <ActionButton
                      variant="delete"
                      onClick={() => handleDeleteClick(book._id)}
                    >
                      <DeleteIcon />
                    </ActionButton>
                  </div>
                ) : userOwnsBook(book._id) ? (
                  <div style={{ display: "flex", gap: "8px" }}>
                    <PurchasedLabel>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      Owned
                    </PurchasedLabel>
                    {}
                    {book.type === "softcopy" && (
                      <ReadButton
                        onClick={() => handleReadBook(book._id)}
                        size="small"
                        disabled={loadingPdf[book._id]}
                      >
                        {loadingPdf[book._id] ? (
                          "Opening..."
                        ) : (
                          <>
                            <ReadIcon size={16} />
                            Open PDF
                          </>
                        )}
                      </ReadButton>
                    )}
                  </div>
                ) : (
                  <StyledPurchaseButton
                    onClick={() => handlePurchase(book)}
                    variant="primary"
                    size="small"
                  >
                    Buy Now
                  </StyledPurchaseButton>
                )}
              </BookFooter>
            </BookCard>
          </Column>
        ))}
      </Grid>
      {}
      {pagination && !searchTerm && pagination.totalPages > 1 && (
        <Pagination
          pagination={pagination}
          onPageChange={(page) => {
            setCurrentPage(page);
            window.scrollTo(0, 0);
          }}
          variant="default"
        />
      )}
      {}
      {selectedBook && paymentIntent && (
        <CheckoutModal
          isOpen={checkoutModalOpen}
          onClose={closeCheckoutModal}
          clientSecret={paymentIntent.clientSecret}
          onSuccess={handleCheckoutSuccess}
          onError={handleCheckoutError}
          bookTitle={selectedBook.title}
          amount={parseFloat(selectedBook.price).toFixed(2)}
        />
      )}
      {}
      <ConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => !isDeleting && setDeleteModalOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="Delete Book"
        message="Are you sure you want to delete this book? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        variant="danger"
        isLoading={isDeleting}
      />
    </>
  );
};
export default BookList;
