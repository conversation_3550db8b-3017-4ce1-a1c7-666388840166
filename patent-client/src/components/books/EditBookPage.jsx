import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { PageTitle } from "../../styles/StyledComponents";
import BookForm from "./BookForm";
import * as bookService from "../../services/bookService";
import Loader from "../layout/Loader";
import { useToast } from "../../contexts/ToastContext";
const EditBookPage = () => {
  const { id } = useParams();
  const [book, setBook] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { addToast } = useToast();
  const navigate = useNavigate();
  useEffect(() => {
    const fetchBook = async () => {
      try {
        setLoading(true);
        const data = await bookService.getBookById(id);
        setBook(data);
      } catch (err) {
        console.error("Failed to fetch book:", err);
        setError(
          "Failed to load book details. The book may not exist or you might not have permission to edit it.",
        );
        addToast("Failed to load book details", "error");
      } finally {
        setLoading(false);
      }
    };
    fetchBook();
  }, [id, addToast]);
  if (loading) {
    return <Loader section="books" />;
  }
  if (error) {
    return (
      <div>
        <PageTitle>Edit Book</PageTitle>
        <p>{error}</p>
        <button onClick={() => navigate("/books")}>Back to Books</button>
      </div>
    );
  }
  return (
    <div>
      <PageTitle>Edit Book</PageTitle>
      {book && <BookForm book={book} isEditing={true} />}
    </div>
  );
};
export default EditBookPage;
