import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import styled from "styled-components";
import {
  PageTitle,
  Card,
  SearchInput,
  Button,
} from "../../styles/StyledComponents";
import * as bookService from "../../services/bookService";
import Loader from "../layout/Loader";
import { useToast } from "../../contexts/ToastContext";
import Pagination from "../common/Pagination";
import { MenuBook as ReadIcon } from "@styled-icons/material";
const MyBooksHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
   margin-bottom: ${({ theme }) => theme.spacing.lg};
  gap: 1rem;
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
      h1 {
      margin-bottom: ${({ theme }) => theme.spacing.md};
    }
  }
`;
const SearchContainer = styled.div`
  margin-bottom: 1.5rem;
  width: 100%;
`;
const BookCard = styled(Card)`
  display: flex;
  flex-direction: column;
  height: 100%;
  transition:
    transform 0.2s,
    box-shadow 0.2s;
  overflow: hidden;
  width: 100%;
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
  @media (max-width: 576px) {
    &:hover {
      transform: translateY(-3px);
    }
  }
`;
const BookImage = styled.img`
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px 8px 0 0;
  @media (max-width: 768px) {
    height: 180px;
  }
  @media (max-width: 576px) {
    height: 160px;
  }
`;
const BookContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  @media (max-width: 768px) {
    padding: 0.75rem;
  }
`;
const BookTitle = styled.h3`
  margin: 0 0 0.5rem;
  font-size: 1.2rem;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  @media (max-width: 992px) {
    font-size: 1.1rem;
  }
`;
const BookDescription = styled.p`
  margin: 0 0 1rem;
  color: #666;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  @media (max-width: 992px) {
    -webkit-line-clamp: 2;
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
  }
  @media (max-width: 576px) {
    font-size: 0.85rem;
    -webkit-line-clamp: 2;
  }
`;
const BookMeta = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  flex-wrap: wrap;
  gap: 0.5rem;
  @media (max-width: 992px) {
    flex-direction: column;
    align-items: flex-start;
  }
`;
const BookType = styled.span`
  background-color: ${({ type }) =>
    type === "hardcopy" ? "rgba(255, 193, 7, 0.2)" : "rgba(40, 167, 69, 0.2)"};
  color: ${({ type }) => (type === "hardcopy" ? "#ffc107" : "#28a745")};
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  white-space: nowrap;
  @media (max-width: 576px) {
    font-size: 0.75rem;
    padding: 3px 6px;
  }
`;
const BookAuthor = styled.span`
  font-style: italic;
  color: #666;
  font-size: 0.85rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  @media (max-width: 576px) {
    font-size: 0.8rem;
  }
`;
const BookFooter = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border-top: 1px solid #eee;
  @media (max-width: 992px) {
    flex-direction: row;
    padding: 0.75rem;
  }
  @media (max-width: 576px) {
    padding: 0.75rem;
    gap: 0.5rem;
  }
`;
const ButtonLink = styled(Link)`
  background-color: #009F9F;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  text-decoration: none;
  font-size: 0.9rem;
  transition: background-color 0.2s;
  white-space: nowrap;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
  &:hover {
    background-color: #007A7A;
    color: white;
  }
`;
const ReadButton = styled(Button)`
  background-color: #009F9F;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  text-decoration: none;
  font-size: 0.9rem;
  transition: background-color 0.2s;
  white-space: nowrap;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
  border: none;
  cursor: pointer;
  &:hover {
    background-color: #007A7A;
    color: white;
  }
  svg {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }
`;
const NoDataMessage = styled.div`
  text-align: center;
  margin: 3rem 0;
  color: #666;
  a {
    color: #009F9F;
    text-decoration: underline;
    margin-left: 0.25rem;
  }
`;
const ErrorMessage = styled.div`
  color: #dc3545;
  padding: 1rem;
  border: 1px solid #dc3545;
  border-radius: 4px;
  margin: 1.5rem 0;
`;
const ResponsiveGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  @media (max-width: 1200px) {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  }
  @media (max-width: 992px) {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1rem;
  }
  @media (max-width: 576px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
`;
const MyBooks = () => {
  const [books, setBooks] = useState([]);
  const [filteredBooks, setFilteredBooks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [loadingPdf, setLoadingPdf] = useState({});
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState(null);
  const { addToast } = useToast();
  useEffect(() => {
    const fetchMyBooks = async () => {
      try {
        setLoading(true);
        const response = await bookService.getMyBooks(currentPage, 6);
        
        if (Array.isArray(response)) {
          setBooks(response);
          setFilteredBooks(response);
          setPagination(null);
        } else if (response && typeof response === 'object') {
          setBooks(response.books || []);
          setFilteredBooks(response.books || []);
          
          // Ensure pagination data is properly structured
          if (response.pagination) {
            setPagination({
              currentPage: response.pagination.currentPage || 1,
              totalPages: response.pagination.totalPages || 1,
              totalCount: response.pagination.totalCount || 0,
              pageSize: response.pagination.pageSize || 6
            });
          } else {
            setPagination(null);
          }
        } else {
          setBooks([]);
          setFilteredBooks([]);
          setPagination(null);
        }
        
        setError(null);
      } catch (err) {
        console.error("Failed to fetch my books:", err);
        setError("Failed to load your books. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    
    fetchMyBooks();
  }, [currentPage]);
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredBooks(books);
      return;
    }
    const searchTermLower = searchTerm.toLowerCase();
    const filtered = books.filter(
      (book) =>
        book.title.toLowerCase().includes(searchTermLower) ||
        book.description.toLowerCase().includes(searchTermLower) ||
        (book.author && book.author.toLowerCase().includes(searchTermLower)),
    );
    setFilteredBooks(filtered);
  }, [searchTerm, books]);
  const handleReadBook = async (bookId) => {
    try {
      setLoadingPdf((prev) => ({ ...prev, [bookId]: true }));
      const response = await bookService.getBookPdfUrl(bookId);
      if (response && response.pdfUrl) {
        window.open(response.pdfUrl, "_blank");
        addToast(" PDF Downloade success");
      } else {
        addToast("PDF file not available", "error");
      }
    } catch (error) {
      console.error("Error opening PDF:", error);
      addToast("Failed to open the book. Please try again.", "error");
    } finally {
      setLoadingPdf((prev) => ({ ...prev, [bookId]: false }));
    }
  };
  if (loading) {
    return <Loader section="books" />;
  }
  return (
    <>
      <MyBooksHeader>
        <PageTitle>My Books</PageTitle>
      </MyBooksHeader>
      <SearchContainer>
        <SearchInput
          type="text"
          placeholder="Search your books by title, description, or author..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </SearchContainer>
      {error && <ErrorMessage>{error}</ErrorMessage>}
      {!error && filteredBooks.length === 0 && (
        <NoDataMessage>
          {searchTerm ? (
            "No books found matching your search."
          ) : (
            <>
              You don't have any books yet.{" "}
              <Link to="/book">Browse books</Link> to purchase
            </>
          )}
        </NoDataMessage>
      )}
      <ResponsiveGrid>
        {filteredBooks.map((book) => (
          <BookCard key={book._id}>
            <BookImage src={book.image} alt={book.title} />
            <BookContent>
              <BookTitle>{book.title}</BookTitle>
              <BookDescription>{book.description}</BookDescription>
              <BookMeta>
                <BookType type={book.type}>
                  {book.type === "hardcopy" ? "Hard Copy" : "Soft Copy"}
                </BookType>
                {book.author && <BookAuthor> {book.author}</BookAuthor>}
              </BookMeta>
            </BookContent>
            <BookFooter>
              <ButtonLink to={`/books/${book._id}`}>View Details</ButtonLink>
              {book.type === "softcopy" && (
                <ReadButton
                  onClick={() => handleReadBook(book._id)}
                  disabled={loadingPdf[book._id]}
                >
                  {loadingPdf[book._id] ? "Opening..." : <>Download</>}
                </ReadButton>
              )}
            </BookFooter>
          </BookCard>
        ))}
      </ResponsiveGrid>
     {pagination && !searchTerm && pagination.totalPages > 1 && (
        <Pagination
          pagination={pagination}
          onPageChange={(page) => {
            setCurrentPage(page);
            window.scrollTo(0, 0);
          }}
          variant="default"
        />
      )}
    </>
  );
};
export default MyBooks;
