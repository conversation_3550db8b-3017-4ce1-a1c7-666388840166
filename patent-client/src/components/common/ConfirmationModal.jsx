import React from "react";
import styled, { keyframes } from "styled-components";
const fadeIn = keyframes`
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
`;
const slideIn = keyframes`
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
`;
const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: ${fadeIn} 0.3s ease-out;
  backdrop-filter: blur(2px);
`;
const ModalContainer = styled.div`
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 420px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  animation: ${slideIn} 0.3s ease-out;
  transform-origin: center;
`;
const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 24px;
  border-bottom: 1px solid #eaeaea;
  background-color: ${({ theme, variant }) =>
    variant === "danger"
      ? theme.colors.danger + "10"
      : theme.colors.primary + "10"};
  h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: ${({ theme, variant }) =>
      variant === "danger" ? theme.colors.danger : theme.colors.primary};
  }
`;
const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: all 0.2s;
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: #333;
  }
`;
const ModalBody = styled.div`
  padding: 24px;
  text-align: center;
`;
const Message = styled.p`
  margin-bottom: 24px;
  font-size: 16px;
  line-height: 1.6;
  color: #444;
`;
const ButtonContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 24px;
`;
const Button = styled.button`
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 100px;
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary + "40"};
  }
  &:disabled {
    cursor: not-allowed;
    opacity: 0.7;
  }
`;
const CancelButton = styled(Button)`
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  color: #444;
  &:hover:not(:disabled) {
    background-color: #e8e8e8;
  }
`;
const ConfirmButton = styled(Button)`
  background-color: ${({ theme, variant }) =>
    variant === "danger" ? theme.colors.danger : theme.colors.primary};
  border: none;
  color: white;
  position: relative;
  &:hover:not(:disabled) {
    background-color: ${({ theme, variant }) =>
      variant === "danger"
        ? theme.colors.danger + "dd"
        : theme.colors.primary + "dd"};
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }
`;
const Spinner = styled.div`
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: ${spin} 0.8s linear infinite;
  margin-right: 8px;
`;
const ConfirmationModal = ({
  isOpen,
  onClose,
  onConfirm,
  title = "Confirm Action",
  message = "Are you sure you want to proceed?",
  confirmText = "Confirm",
  cancelText = "Cancel",
  variant = "primary",
  isLoading = false,
}) => {
  if (!isOpen) return null;
  return (
    <ModalOverlay onClick={isLoading ? null : onClose}>
      <ModalContainer onClick={(e) => e.stopPropagation()}>
        <ModalHeader variant={variant}>
          <h3>{title}</h3>
          {!isLoading && <CloseButton onClick={onClose}>×</CloseButton>}
        </ModalHeader>
        <ModalBody>
          <Message>{message}</Message>
          <ButtonContainer>
            <CancelButton onClick={onClose} disabled={isLoading}>
              {cancelText}
            </CancelButton>
            <ConfirmButton
              variant={variant}
              onClick={onConfirm}
              disabled={isLoading}
            >
              {isLoading && <Spinner />}
              {confirmText}
            </ConfirmButton>
          </ButtonContainer>
        </ModalBody>
      </ModalContainer>
    </ModalOverlay>
  );
};
export default ConfirmationModal;
