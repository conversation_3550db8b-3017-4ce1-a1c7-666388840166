import React from "react";
import styled from "styled-components";
import PropTypes from "prop-types";
const PaginationContainer = styled.div`
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin: 2rem 0;
  gap: 0.5rem;
`;
const PageButton = styled.button`
  padding: 0.5rem 1rem;
  border: 1px solid
    ${({ theme, active }) =>
      active ? theme.colors.primary : theme.colors.border};
  background-color: ${({ theme, active }) =>
    active ? theme.colors.primary : "white"};
  color: ${({ theme, active }) => (active ? "white" : theme.colors.text)};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  min-width: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  &:hover {
    background-color: ${({ theme, active }) =>
      active ? theme.colors.primary : theme.colors.light};
    border-color: ${({ theme }) => theme.colors.primary};
  }
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: ${({ theme }) => theme.colors.light};
    border-color: ${({ theme }) => theme.colors.border};
  }
  @media (max-width: 768px) {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    min-width: 36px;
  }
`;
const PageInfo = styled.div`
  font-size: 0.9rem;
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: 0 1rem;
  @media (max-width: 768px) {
    font-size: 0.8rem;
    margin: 0 0.5rem;
  }
`;
const Pagination = ({ pagination, onPageChange, variant = "default" }) => {
  if (!pagination) return null;
  const { currentPage, totalPages, hasNext, hasPrevious } = pagination;
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxPagesToShow = variant === "compact" ? 3 : 5;
    if (totalPages <= maxPagesToShow) {
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      pageNumbers.push(1);
      let start = Math.max(2, currentPage - 1);
      let end = Math.min(totalPages - 1, currentPage + 1);
      if (currentPage <= 2) {
        end = Math.min(totalPages - 1, 4);
      } else if (currentPage >= totalPages - 1) {
        start = Math.max(2, totalPages - 3);
      }
      if (start > 2) {
        pageNumbers.push("...");
      }
      for (let i = start; i <= end; i++) {
        pageNumbers.push(i);
      }
      if (end < totalPages - 1) {
        pageNumbers.push("...");
      }
      if (totalPages > 1) {
        pageNumbers.push(totalPages);
      }
    }
    return pageNumbers;
  };
  const handlePageChange = (page) => {
    onPageChange(page);
    window.scrollTo(0, 0);
  };
  if (variant === "simple") {
    return (
      <PaginationContainer>
        <PageButton
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={!hasPrevious}
        >
          Previous
        </PageButton>
        <PageInfo>
          Page {currentPage} of {totalPages}
        </PageInfo>
        <PageButton
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={!hasNext}
        >
          Next
        </PageButton>
      </PaginationContainer>
    );
  }
  return (
    <PaginationContainer>
      {variant === "default" && (
        <PageButton
          onClick={() => handlePageChange(1)}
          disabled={currentPage === 1}
        >
          &laquo;
        </PageButton>
      )}
      <PageButton
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={!hasPrevious}
      >
        &lsaquo;
      </PageButton>
      {getPageNumbers().map((page, index) =>
        page === "..." ? (
          <PageInfo key={`ellipsis-${index}`}>...</PageInfo>
        ) : (
          <PageButton
            key={page}
            active={page === currentPage ? "true" : undefined}
            onClick={() => handlePageChange(page)}
          >
            {page}
          </PageButton>
        ),
      )}
      <PageButton
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={!hasNext}
      >
        &rsaquo;
      </PageButton>
      {variant === "default" && (
        <PageButton
          onClick={() => handlePageChange(totalPages)}
          disabled={currentPage === totalPages}
        >
          &raquo;
        </PageButton>
      )}
      <PageInfo>
        Page {currentPage} of {totalPages}
      </PageInfo>
    </PaginationContainer>
  );
};
Pagination.propTypes = {
  pagination: PropTypes.shape({
    currentPage: PropTypes.number.isRequired,
    totalPages: PropTypes.number.isRequired,
    totalCount: PropTypes.number.isRequired,
    hasNext: PropTypes.bool.isRequired,
    hasPrevious: PropTypes.bool.isRequired,
    pageSize: PropTypes.number,
  }),
  onPageChange: PropTypes.func.isRequired,
  variant: PropTypes.oneOf(["default", "compact", "simple"]),
};
export default Pagination;
