import React, { useState, useEffect } from "react";
import styled from "styled-components";
import {
  <PERSON>ton,
  Card,
  Input,
  Textarea,
  Select,
  FormGroup,
  Label,
} from "../../styles/StyledComponents";
import { Plus, TrashAlt, ChevronUp, ChevronDown } from "@styled-icons/fa-solid";
import FileUploader from "./FileUploader";
import { useNavigate, useParams } from "react-router-dom";
import {
  createCourse,
  updateCourse,
  getCourseById,
} from "../../services/courseService";
import { useToast } from "../../contexts/ToastContext";
import Loader from "../layout/Loader";
const ModuleTypeOptions = [
  { value: "text", label: "Text" },
  { value: "image", label: "Image" },
  { value: "pdf", label: "PDF Document" },
  { value: "video", label: "Video" },
];
const FormCard = styled(Card)`
  margin-bottom: 1.5rem;
`;
const FormHeading = styled.h2`
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #f0f0f0;
`;
const ModulesSection = styled.div`
  margin-top: 1.5rem;
`;
const ModuleItem = styled.div`
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 1rem;
`;
const ModuleHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background-color: ${({ isExpanded }) => (isExpanded ? "#007bff" : "#f8f9fa")};
  color: ${({ isExpanded }) => (isExpanded ? "#fff" : "#333")};
  cursor: pointer;
`;
const ModuleTitle = styled.div`
  font-weight: 500;
  span {
    margin-left: 0.5rem;
    font-size: 0.8rem;
    background: ${({ type }) =>
      type === "image"
        ? "#17a2b8"
        : type === "pdf"
          ? "#ffc107"
          : type === "video"
            ? "#28a745"
            : "#6c757d"};
    color: white;
    padding: 2px 6px;
    border-radius: 12px;
  }
`;
const ModuleControls = styled.div`
  display: flex;
  gap: 0.5rem;
`;
const IconButton = styled.button`
  background: transparent;
  border: none;
  color: ${({ isExpanded, color }) => color || (isExpanded ? "#fff" : "#333")};
  cursor: pointer;
  padding: 0.25rem;
  svg {
    width: 16px;
    height: 16px;
  }
`;
const ModuleContent = styled.div`
  padding: 1rem;
  display: ${({ isExpanded }) => (isExpanded ? "block" : "none")};
`;
const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
  margin-bottom: 2rem;
`;
const AddModuleButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  svg {
    width: 16px;
    height: 16px;
  }
`;
const ErrorText = styled.p`
  color: #dc3545;
  font-size: 0.8rem;
  margin-top: 0.25rem;
`;
const ContentField = ({ module, onChange, index }) => {
  switch (module.type) {
    case "text":
      return (
        <Textarea
          value={module.content || ""}
          onChange={(e) => onChange(index, "content", e.target.value)}
          rows={5}
          placeholder="Enter module content..."
        />
      );
    case "image":
    case "pdf":
    case "video":
      return (
        <FileUploader
          type={module.type}
          value={module.content}
          onChange={(file) => onChange(index, "content", file)}
          fieldName={`module-${index}-content`}
        />
      );
    default:
      return <p>Unsupported module type</p>;
  }
};
const CourseForm = () => {
  const { id } = useParams();
  const isEditMode = !!id;
  const [loading, setLoading] = useState(isEditMode);
  const [form, setForm] = useState({
    title: "",
    description: "",
    price: "",
    image: null,
    modules: [],
    certificate: false,
    level: "Beginner",
    hours: 0,
  });
  const [expandedModule, setExpandedModule] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});
  const navigate = useNavigate();
  const { addToast } = useToast();
  useEffect(() => {
    if (isEditMode) {
      const fetchCourseDetails = async () => {
        try {
          setLoading(true);
          const courseData = await getCourseById(id);
          setForm({
            title: courseData.title || "",
            description: courseData.description || "",
            price: courseData.price?.toString() || "0",
            image: courseData.image || null,
            modules:
              courseData.modules?.map((module, index) => ({
                title: module.title,
                type: module.type,
                content: module.content || "",
                order: index,
              })) || [],
            certificate: courseData.certificate || false,
            level: courseData.level || "Beginner",
            hours: courseData.hours || 0,
          });
        } catch (error) {
          addToast("Failed to load course details", "error");
          navigate("/courses");
        } finally {
          setLoading(false);
        }
      };
      fetchCourseDetails();
    }
  }, [id, isEditMode, addToast, navigate]);
  const validateForm = () => {
    const newErrors = {};
    if (!form.title.trim()) newErrors.title = "Course title is required";
    if (!form.description.trim())
      newErrors.description = "Course description is required";
    if (!form.price || isNaN(form.price) || Number(form.price) < 0) {
      newErrors.price = "Price must be a non-negative number";
    }
    if (!form.image) newErrors.image = "Course image is required";
    if (form.modules.length === 0)
      newErrors.modules = "At least one module is required";
    form.modules.forEach((module, index) => {
      if (!module.title.trim()) {
        newErrors[`module-${index}-title`] = "Module title is required";
      }
      if (!module.content && module.type !== "text" && !module.contentUrl) {
        newErrors[`module-${index}-content`] = `${
          module.type.charAt(0).toUpperCase() + module.type.slice(1)
        } content is required`;
      } else if (module.type === "text" && !module.content?.trim()) {
        newErrors[`module-${index}-content`] = "Text content is required";
      }
    });
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
    setErrors((prev) => ({ ...prev, [name]: undefined }));
  };
  const handleImageChange = (file) => {
    setForm((prev) => ({ ...prev, image: file }));
    setErrors((prev) => ({ ...prev, image: undefined }));
  };
  const handleAddModule = () => {
    setForm((prev) => ({
      ...prev,
      modules: [
        ...prev.modules,
        {
          title: "",
          type: "text",
          content: "",
          order: prev.modules.length,
        },
      ],
    }));
    setExpandedModule(form.modules.length);
    setErrors((prev) => ({ ...prev, modules: undefined }));
  };
  const handleRemoveModule = (index) => {
    setForm((prev) => ({
      ...prev,
      modules: prev.modules
        .filter((_, i) => i !== index)
        .map((module, i) => ({ ...module, order: i })),
    }));
    if (expandedModule === index) {
      setExpandedModule(null);
    } else if (expandedModule > index) {
      setExpandedModule(expandedModule - 1);
    }
  };
  const toggleModuleExpand = (index) => {
    setExpandedModule(expandedModule === index ? null : index);
  };
  const handleModuleChange = (index, field, value) => {
    setForm((prev) => ({
      ...prev,
      modules: prev.modules.map((module, i) =>
        i === index
          ? {
              ...module,
              [field]: value,
              ...(field === "type" && ["image", "pdf", "video"].includes(value)
                ? { content: null }
                : {}),
            }
          : module,
      ),
    }));
    setErrors((prev) => ({ ...prev, [`module-${index}-${field}`]: undefined }));
  };
  const moveModule = (index, direction) => {
    const newIndex = direction === "up" ? index - 1 : index + 1;
    if (newIndex < 0 || newIndex >= form.modules.length) return;
    setForm((prev) => {
      const updatedModules = [...prev.modules];
      [updatedModules[index], updatedModules[newIndex]] = [
        updatedModules[newIndex],
        updatedModules[index],
      ];
      return {
        ...prev,
        modules: updatedModules.map((module, i) => ({ ...module, order: i })),
      };
    });
    if (expandedModule === index) {
      setExpandedModule(newIndex);
    } else if (expandedModule === newIndex) {
      setExpandedModule(index);
    }
  };
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) {
      addToast("Please fix the errors before submitting", "error");
      return;
    }
    setIsSubmitting(true);
    try {
      const price = parseFloat(form.price);
      if (isNaN(price) || price < 0) {
        throw new Error("Price must be a non-negative number");
      }
      const submissionData = {
        title: form.title,
        description: form.description,
        price: price,
        image: form.image,
        modules: form.modules.map(({ title, type, content, order }) => ({
          title,
          type,
          content: content ?? (type === "text" ? "" : null),
          order,
          publicId: "",
          resourceType: "",
        })),
        certificate: form.certificate,
        level: form.level,
        hours: parseInt(form.hours) || 0,
      };
      if (isEditMode) {
        await updateCourse(id, submissionData);
      } else {
        await createCourse(submissionData);
      }
      addToast(
        `Course ${isEditMode ? "updated" : "created"} successfully!`,
        "success",
      );
      navigate("/courses");
    } catch (err) {
      addToast(
        `Failed to ${isEditMode ? "update" : "create"} course: ${err.message}`,
        "error",
      );
    } finally {
      setIsSubmitting(false);
    }
  };
  if (loading)
    return <Loader section="courses" text="Loading course data..." />;
  return (
    <form onSubmit={handleSubmit}>
      <FormCard>
        <FormHeading>
          {isEditMode ? "Edit Course" : "Create New Course"}
        </FormHeading>
        <FormGroup>
          <Label htmlFor="title">Course Title *</Label>
          <Input
            id="title"
            name="title"
            value={form.title}
            onChange={handleChange}
            placeholder="Enter course title"
          />
          {errors.title && <ErrorText>{errors.title}</ErrorText>}
        </FormGroup>
        <FormGroup>
          <Label htmlFor="description">Course Description *</Label>
          <Textarea
            id="description"
            name="description"
            value={form.description}
            onChange={handleChange}
            rows={4}
            placeholder="Enter course description"
          />
          {errors.description && <ErrorText>{errors.description}</ErrorText>}
        </FormGroup>
        <FormGroup>
          <Label htmlFor="price">Price (USD) *</Label>
          <Input
            id="price"
            name="price"
            value={form.price}
            onChange={handleChange}
            type="number"
            min="0"
            step="0.01"
            placeholder="Enter price"
          />
          {errors.price && <ErrorText>{errors.price}</ErrorText>}
        </FormGroup>
        <FormGroup>
          <Label htmlFor="image">Course Image *</Label>
          <FileUploader
            type="image"
            value={form.image}
            onChange={handleImageChange}
            fieldName="image"
          />
          {errors.image && <ErrorText>{errors.image}</ErrorText>}
        </FormGroup>
        <FormGroup>
          <Label htmlFor="hours">Course Duration (Hours)</Label>
          <Input
            id="hours"
            name="hours"
            value={form.hours}
            onChange={handleChange}
            type="number"
            min="0"
            placeholder="Enter course duration in hours"
          />
        </FormGroup>
        <FormGroup>
          <Label htmlFor="level">Course Level</Label>
          <Select
            id="level"
            name="level"
            value={form.level}
            onChange={handleChange}
          >
            <option value="Beginner">Beginner</option>
            <option value="Intermediate">Intermediate</option>
            <option value="Expert">Expert</option>
          </Select>
        </FormGroup>
        <FormGroup>
          <div className="flex items-center gap-2">
            <input
              id="certificate"
              name="certificate"
              type="checkbox"
              checked={form.certificate}
              onChange={(e) =>
                setForm((prev) => ({ ...prev, certificate: e.target.checked }))
              }
              className="w-4 h-4"
            />
            <Label htmlFor="certificate" className="mb-0">
              Certificate Included
            </Label>
          </div>
        </FormGroup>
      </FormCard>
      <FormCard>
        <ModulesSection>
          <FormHeading>Course Modules</FormHeading>
          {errors.modules && <ErrorText>{errors.modules}</ErrorText>}
          {form.modules.map((module, index) => (
            <ModuleItem key={`module-${index}`}>
              <ModuleHeader
                isExpanded={expandedModule === index}
                onClick={() => toggleModuleExpand(index)}
              >
                <ModuleTitle type={module.type}>
                  {module.title || `Module ${index + 1}`}
                  <span>{module.type}</span>
                </ModuleTitle>
                <ModuleControls>
                  <IconButton
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      moveModule(index, "up");
                    }}
                    disabled={index === 0}
                    isExpanded={expandedModule === index}
                  >
                    <ChevronUp />
                  </IconButton>
                  <IconButton
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      moveModule(index, "down");
                    }}
                    disabled={index === form.modules.length - 1}
                    isExpanded={expandedModule === index}
                  >
                    <ChevronDown />
                  </IconButton>
                  <IconButton
                    type="button"
                    color="#dc3545"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveModule(index);
                    }}
                    isExpanded={expandedModule === index}
                  >
                    <TrashAlt />
                  </IconButton>
                </ModuleControls>
              </ModuleHeader>
              <ModuleContent isExpanded={expandedModule === index}>
                <FormGroup>
                  <Label htmlFor={`module-${index}-title`}>
                    Module Title *
                  </Label>
                  <Input
                    id={`module-${index}-title`}
                    value={module.title}
                    onChange={(e) =>
                      handleModuleChange(index, "title", e.target.value)
                    }
                    placeholder="Enter module title"
                  />
                  {errors[`module-${index}-title`] && (
                    <ErrorText>{errors[`module-${index}-title`]}</ErrorText>
                  )}
                </FormGroup>
                <FormGroup>
                  <Label htmlFor={`module-${index}-type`}>Content Type *</Label>
                  <Select
                    id={`module-${index}-type`}
                    value={module.type}
                    onChange={(e) =>
                      handleModuleChange(index, "type", e.target.value)
                    }
                  >
                    {ModuleTypeOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </Select>
                </FormGroup>
                <FormGroup>
                  <Label htmlFor={`module-${index}-content`}>
                    Module Content *
                  </Label>
                  <ContentField
                    module={module}
                    onChange={handleModuleChange}
                    index={index}
                  />
                  {errors[`module-${index}-content`] && (
                    <ErrorText>{errors[`module-${index}-content`]}</ErrorText>
                  )}
                </FormGroup>
              </ModuleContent>
            </ModuleItem>
          ))}
          <AddModuleButton
            type="button"
            variant="secondary"
            onClick={handleAddModule}
          >
            <Plus />
            Add Module
          </AddModuleButton>
        </ModulesSection>
      </FormCard>
      <ButtonGroup>
        <Button
          type="button"
          variant="outline"
          onClick={() => navigate("/courses")}
          style={{
            padding: "8px 16px",
            height: "auto",
            minWidth: "100px",
            borderWidth: "2px",
            backgroundColor: "#f5f5f5",
            border: "1px solid #e0e0e0",
            color: "#444",
          }}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="primary"
          disabled={isSubmitting}
          style={{
            padding: "8px 20px",
            height: "auto",
            minWidth: "150px",
            fontWeight: "600",
            boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
            transition: "all 0.2s ease",
          }}
        >
          {isSubmitting ? (
            <span
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <svg
                width="18"
                height="18"
                viewBox="0 0 24 24"
                style={{
                  marginRight: "8px",
                  animation: "spin 1s linear infinite",
                }}
              >
                <style>{`
                  @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                  }
                `}</style>
                <path
                  fill="currentColor"
                  d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z"
                />
              </svg>
              Saving...
            </span>
          ) : (
            <span
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              {isEditMode ? "Update Course" : "Create Course"}
            </span>
          )}
        </Button>
      </ButtonGroup>
    </form>
  );
};
export default CourseForm;
