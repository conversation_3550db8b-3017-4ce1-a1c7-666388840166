import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import styled from "styled-components";
import {
  PageTitle,
  Button,
  Card,
  Grid,
  Column,
  SearchInput,
} from "../../styles/StyledComponents";
import * as courseService from "../../services/courseService";
import Loader from "../layout/Loader";
import { useToast } from "../../contexts/ToastContext";
import { useAuth } from "../../contexts/AuthContext";
import Pagination from "../common/Pagination";
import ConfirmationModal from "../common/ConfirmationModal";
import { API_BASE_URL } from "../../config/config.js";
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
} from "@styled-icons/material";
const CourseHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const SearchContainer = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const StyledSearchInput = styled(SearchInput)`
  color: #333;
  &::placeholder {
    color: #999;
  }
`;
const CourseCard = styled(Card)`
  display: flex;
  flex-direction: column;
  padding: ${({ theme }) => theme.spacing.md};
  text-decoration: none;
  color: inherit;
  height: 100%;
`;
const CourseImage = styled.img`
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const CourseContent = styled.div`
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  justify-content: space-between;
`;
const CourseTitle = styled.h3`
  margin: 0;
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: bold;
`;
const CourseDescription = styled.p`
  margin: ${({ theme }) => theme.spacing.sm} 0;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  min-height: 3em;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;
const CoursePrice = styled.span`
  font-weight: bold;
  color: #009F9F; /* Teal color from landing page */
  margin-top: auto;
`;
const CourseActions = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: ${({ theme }) => theme.spacing.md};
`;
const ActionButton = styled.button`
  background-color: transparent;
  border: none;
  color: #009F9F; /* Teal color from landing page */
  cursor: pointer;
  margin-left: ${({ theme }) => theme.spacing.sm};
  padding: ${({ theme }) => theme.spacing.xs};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: background-color ${({ theme }) => theme.transition};
  &:hover {
    background-color: rgba(0, 159, 159, 0.1); /* Teal with opacity */
  }
  svg {
    width: 20px;
    height: 20px;
  }
`;
const NoCoursesMessage = styled.div`
  text-align: center;
  margin-top: ${({ theme }) => theme.spacing.lg};
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const LoadingMessage = styled.div`
  text-align: center;
  margin-top: ${({ theme }) => theme.spacing.lg};
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const PurchaseTag = styled.div`
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #009F9F; /* Teal color from landing page */
  color: white;
  padding: 4px 8px;
  font-size: 0.7rem;
  border-radius: 12px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
`;
const CourseCardWrapper = styled.div`
  position: relative;
  height: 100%;
`;
const CourseList = () => {
  const [courses, setCourses] = useState([]);
  const [filteredCourses, setFilteredCourses] = useState([]);
  const [userPurchases, setUserPurchases] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [courseToDelete, setCourseToDelete] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const location = useLocation();
  const { addToast } = useToast();
  const { user } = useAuth();
  const isAdmin = user && user.isAdmin;
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        if (isAdmin) {
          try {
            const response = await fetch(
              `${API_BASE_URL}/courses?page=${currentPage}&limit=6`,
              {
                headers: {
                  Authorization: `Bearer ${user.accessToken || user.token}`,
                },
              },
            );
            if (response.ok) {
              const data = await response.json();
              if (Array.isArray(data)) {
                setCourses(data);
                setPagination(null);
              } else if (data && data.courses) {
                setCourses(data.courses);
                setPagination(data.pagination || null);
              } else {
                setCourses([]);
                setPagination(null);
              }
            } else {
              console.error("Failed to fetch courses:", response.status);
              setCourses([]);
              setPagination(null);
            }
          } catch (error) {
            console.error("Error fetching courses:", error);
            setCourses([]);
            setPagination(null);
          }
        } else {
          try {
            const purchasesResponse = await fetch(`${API_BASE_URL}/purchases/my`, {
              headers: {
                Authorization: `Bearer ${user.accessToken || user.token}`,
              },
            });
            if (purchasesResponse.ok) {
              const purchasesData = await purchasesResponse.json();
              if (Array.isArray(purchasesData)) {
                setUserPurchases(purchasesData);
              } else if (purchasesData && purchasesData.purchases) {
                setUserPurchases(purchasesData.purchases);
              } else {
                setUserPurchases([]);
              }
            } else {
              console.error(
                "Failed to fetch user purchases:",
                purchasesResponse.status,
              );
              setUserPurchases([]);
            }
            const coursesResponse = await fetch(
              `${API_BASE_URL}/courses?page=${currentPage}&limit=6`,
              {
                headers: {
                  Authorization: `Bearer ${user.accessToken || user.token}`,
                },
              },
            );
            if (coursesResponse.ok) {
              const data = await coursesResponse.json();
              if (Array.isArray(data)) {
                setCourses(data);
                setPagination(null);
              } else if (data && data.courses) {
                setCourses(data.courses);
                setPagination(data.pagination || null);
              } else {
                setCourses([]);
                setPagination(null);
              }
            } else {
              console.error("Failed to fetch courses:", coursesResponse.status);
              setCourses([]);
              setPagination(null);
            }
          } catch (error) {
            console.error("Error fetching user data:", error);
            setUserPurchases([]);
            setCourses([]);
            setPagination(null);
          }
        }
        setError(null);
      } catch (err) {
        console.error("Failed to fetch course data:", err);
        setError("Failed to load courses. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [location, user, isAdmin, currentPage]);
  useEffect(() => {
    if (courses.length > 0) {
      const filtered = courses.filter(
        (course) =>
          course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          course.description.toLowerCase().includes(searchTerm.toLowerCase()),
      );
      setFilteredCourses(filtered);
    }
  }, [courses, searchTerm]);
  const isCoursePurchased = (courseId) => {
    if (!userPurchases || userPurchases.length === 0) return false;
    return userPurchases.some((purchase) => {
      if (!purchase.courseId) return false;
      if (typeof purchase.courseId === "object") {
        return purchase.courseId._id === courseId;
      } else {
        return purchase.courseId === courseId;
      }
    });
  };
  const handleDeleteClick = (e, id) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isAdmin) {
      addToast("You do not have permission to delete courses.", "error");
      return;
    }
    setCourseToDelete(id);
    setDeleteModalOpen(true);
  };
  const handleDeleteConfirm = async () => {
    try {
      setIsDeleting(true);
      await courseService.deleteCourse(courseToDelete);
      setCourses((prevCourses) => {
        const updatedCourses = prevCourses.filter(
          (course) => course._id !== courseToDelete,
        );
        return updatedCourses;
      });
      setFilteredCourses((prevFilteredCourses) =>
        prevFilteredCourses.filter((course) => course._id !== courseToDelete),
      );
      addToast("Course deleted successfully!", "success");
    } catch (err) {
      console.error("Failed to delete course:", err);
      addToast("Failed to delete course. Please try again.", "error");
    } finally {
      setTimeout(() => {
        setIsDeleting(false);
        setDeleteModalOpen(false);
        setCourseToDelete(null);
      }, 500);
    }
  };
  if (loading) {
    return <Loader section="courses" />;
  }
  if (error) {
    return <NoCoursesMessage>{error}</NoCoursesMessage>;
  }
  return (
    <div>
      <CourseHeader>
        <PageTitle>{isAdmin ? "All Courses" : "My Courses"}</PageTitle>
        {isAdmin && (
          <Button
            as={Link}
            to="/courses/new"
            style={{
              backgroundColor: "#009F9F",
              color: "white",
              padding: "8px 16px",
              borderRadius: "4px",
              fontWeight: "bold",
              display: "flex",
              alignItems: "center",
              gap: "8px"
            }}
          >
            <AddIcon size={20} /> New Course
          </Button>
        )}
      </CourseHeader>
      <SearchContainer>
        <StyledSearchInput
          placeholder="Search courses..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </SearchContainer>
      <Grid columns={3} gap="lg">
        {filteredCourses.map((course) => {
          if (!course || !course._id) {
            return null;
          }
          const purchased = !isAdmin && isCoursePurchased(course._id);
          if (!isAdmin && !purchased) {
            return null;
          }
          return (
            <Column key={course._id} span={1}>
              <CourseCardWrapper>
                {purchased && <PurchaseTag>Purchased</PurchaseTag>}
                <CourseCard as={Link} to={`/courses/${course._id}`}>
                  <CourseImage src={course.image} alt={course.title} />
                  <CourseContent>
                    <CourseTitle>{course.title.toUpperCase()}</CourseTitle>
                    <CourseDescription>
                      {course.description.length > 100
                        ? `${course.description.substring(0, 97)}...`
                        : course.description}
                    </CourseDescription>
                    <CoursePrice>
                      ${parseFloat(course.price).toFixed(2)}
                    </CoursePrice>
                    {isAdmin && (
                      <CourseActions>
                        <ActionButton
                          as={Link}
                          to={`/courses/${course._id}/edit`}
                          onClick={(e) => e.stopPropagation()}
                        >
                          <EditIcon />
                        </ActionButton>
                        <ActionButton
                          onClick={(e) => handleDeleteClick(e, course._id)}
                          className="delete"
                        >
                          <DeleteIcon />
                        </ActionButton>
                      </CourseActions>
                    )}
                  </CourseContent>
                </CourseCard>
              </CourseCardWrapper>
            </Column>
          );
        })}
      </Grid>
      {filteredCourses.length === 0 && !loading && (
        <NoCoursesMessage>
          {searchTerm
            ? `No courses found for "${searchTerm}". Try a different search.`
            : isAdmin
              ? 'No courses available. Click "New Course" to create one.'
              : "You haven't purchased any courses yet."}
        </NoCoursesMessage>
      )}
      {}
      {pagination && !searchTerm && pagination.totalPages > 1 && (
        <Pagination
          pagination={pagination}
          onPageChange={(page) => {
            setCurrentPage(page);
            window.scrollTo(0, 0);
          }}
          variant="default"
        />
      )}
      {}
      <ConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => !isDeleting && setDeleteModalOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="Delete Course"
        message="Are you sure you want to delete this course? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        variant="danger"
        isLoading={isDeleting}
      />
    </div>
  );
};
export default CourseList;
