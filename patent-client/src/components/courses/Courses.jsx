import React, { useEffect, useState, useRef } from "react";
import { usePara<PERSON>, useN<PERSON>gate, Link, useLocation } from "react-router-dom";
import styled from "styled-components";
import {
  PageTitle,
  Button,
  Card,
  Grid,
  Column,
} from "../../styles/StyledComponents";
import * as courseService from "../../services/courseService";
import * as purchaseService from "../../services/purchaseService";
import Loader from "../layout/Loader";
import { useAuth } from "../../contexts/AuthContext";
import { useToast } from "../../contexts/ToastContext";
import { API_BASE_URL } from "../../config/config.js";
import { Edit as EditIcon } from "@styled-icons/material";
import {
  InsertDriveFile,
  VideoLibrary,
  Image,
  Download,
  GetApp,
  ArrowBack,
} from "@styled-icons/material";
const CourseDetailContainer = styled.div`
  width: 100%;
  padding: 0 ${({ theme }) => theme.spacing.sm};
  @media (max-width: 768px) {
    padding: 0 ${({ theme }) => theme.spacing.xs};
  }
`;
const HeaderActions = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  width: 100%;
  flex-wrap: nowrap;
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: ${({ theme }) => theme.spacing.md};
    margin-top: 0px;
    & > h1 {
      margin-bottom: ${({ theme }) => theme.spacing.md};
    }
    & > div {
      width: 100%;
      justify-content: flex-start;
    }
  }
`;
const CourseImage = styled.img`
  width: 100%;
  max-height: 300px;
  object-fit: cover;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const CourseInfo = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.xl};
`;
const Price = styled.div`
  font-size: 1.5rem;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.primary};
  margin: ${({ theme }) => `${theme.spacing.md} 0`};
`;
const ModuleCard = styled(Card)`
  width: 100%;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const ModuleTitle = styled.h3`
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`;
const ModuleType = styled.span`
  display: inline-block;
  padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.sm}`};
  background-color: ${({ theme }) => theme.colors.light};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: 0.75rem;
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`;
const EditButton = styled(Button)`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing.sm};
  font-size: 0.9rem;
  padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.md}`};
  height: 36px;
  line-height: 1;
  svg {
    width: 14px;
    height: 14px;
  }
`;
export const BackButton = styled(Button)`
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  border: 2px solid #009F9F;
  color: #009F9F;
  border-radius: 25px;
  padding: 8px 20px;
  transition: all 0.3s ease;
  margin-bottom: 20px;
  &:hover {
    background-color: #009F9F;
    color: white;
  }
  svg {
    width: 20px;
    height: 20px;
  }
  span {
    @media (max-width: 768px) {
      display: none;
    }
  }
  @media (max-width: 768px) {
    padding: 8px;
    border-radius: 50%;
  }
`;
export const BackArrow = styled.svg`
  width: 20px;
  height: 20px;
`;
const FileViewer = styled.div`
  margin-top: ${({ theme }) => theme.spacing.md};
`;
const PDFViewer = styled.iframe`
  width: 100%;
  height: 500px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  @media (max-width: 768px) {
    height: 350px;
  }
  @media (max-width: 480px) {
    height: 250px;
  }
`;
const VideoPlayer = styled.video`
  width: 100%;
  max-height: 400px;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  @media (max-width: 768px) {
    max-height: 300px;
  }
  @media (max-width: 480px) {
    max-height: 200px;
  }
`;
const ImageViewer = styled.img`
  max-width: 100%;
  max-height: 400px;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  @media (max-width: 768px) {
    max-height: 300px;
  }
  @media (max-width: 480px) {
    max-height: 200px;
  }
`;
const FileLink = styled.a`
  display: flex;
  align-items: center;
  text-decoration: none;
  color: ${({ theme }) => theme.colors.primary};
  margin-bottom: ${({ theme }) => theme.spacing.sm};
  padding: ${({ theme }) => theme.spacing.sm};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.light};
  word-break: break-word;
  &:hover {
    background-color: ${({ theme }) => theme.colors.primary + "10"};
  }
  svg {
    width: 20px;
    height: 20px;
    margin-right: ${({ theme }) => theme.spacing.sm};
    flex-shrink: 0;
  }
  @media (max-width: 480px) {
    font-size: 14px;
    padding: ${({ theme }) => theme.spacing.xs};
    svg {
      width: 16px;
      height: 16px;
      margin-right: ${({ theme }) => theme.spacing.xs};
    }
  }
`;
const FileIcon = styled.span`
  margin-right: ${({ theme }) => theme.spacing.sm};
  color: ${({ theme }) => theme.colors.primary};
  svg {
    width: 24px;
    height: 24px;
  }
`;
const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  width: 100%;
`;
const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colors.error};
  padding: ${({ theme }) => theme.spacing.md};
  border: 1px solid ${({ theme }) => theme.colors.error};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin: ${({ theme }) => theme.spacing.lg} 0;
`;
const PurchaseStatus = styled.div`
  padding: ${({ theme }) => theme.spacing.md};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: ${({ theme, isPurchased }) =>
    isPurchased ? theme.colors.success + "20" : theme.colors.light};
  border: 1px solid
    ${({ theme, isPurchased }) =>
      isPurchased ? theme.colors.success : theme.colors.border};
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: ${({ theme }) => theme.spacing.md};
  }
`;
const PurchaseButton = styled(Button)`
  margin-left: ${({ theme }) => theme.spacing.md};
  @media (max-width: 768px) {
    margin-left: 0;
    width: 100%;
  }
`;
const DownloadButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  min-width: 180px;
  padding: 8px 16px;
  font-size: 0.9rem;
  height: auto;
  background-color: #009F9F;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  ${({ downloading }) =>
    downloading &&
    `
      min-height: 100px;
      height: auto;
  `}
  svg {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
  }
  &:hover {
    background-color: #007A7A;
  }
  @media (max-width: 768px) {
    width: 100%;
    justify-content: center;
  }
`;
const VideoDownloadButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-top: ${({ theme }) => theme.spacing.sm};
  font-size: 0.85rem;
  padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.sm}`};
  min-width: 140px;
  height: auto;
  ${(props) =>
    props.disabled &&
    `
    min-height: 80px;
    height: auto;
  `}
  svg {
    width: 14px;
    height: 14px;
  }
  @media (max-width: 768px) {
    width: 100%;
    justify-content: center;
  }
`;
const LoadingSpinner = styled.div`
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: ${({ theme }) => theme.spacing.xs};
  ${(props) =>
    props.variant === "outline" &&
    `
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-top-color: ${props.theme.colors.primary};
  `}
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
`;
const ProgressBar = styled.div`
  width: 100%;
  height: 4px;
  background-color: ${({ theme }) => theme.colors.light};
  border-radius: 2px;
  margin-top: ${({ theme }) => theme.spacing.xs};
  overflow: hidden;
`;
const ProgressFill = styled.div`
  height: 100%;
  background-color: ${({ theme }) => theme.colors.primary};
  width: ${({ progress }) => `${Math.max(0, Math.min(100, progress))}%`};
  transition: width 0.3s ease;
`;
const ButtonText = styled.span`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
`;
const ProgressText = styled.span`
  font-size: 0.8rem;
  color: ${({ theme, variant }) =>
    variant === "outline" ? theme.colors.primary : "rgba(255, 255, 255, 0.9)"};
  margin-top: ${({ theme }) => theme.spacing.xs};
  text-align: center;
  width: 100%;
  display: block;
  font-weight: 500;
  line-height: 1.4;
`;
const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};
const Courses = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { addToast } = useToast();
  const { user } = useAuth();
  const isAdmin = user && user.isAdmin;
  const [course, setCourse] = useState(null);
  const [isPurchased, setIsPurchased] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [downloadingCourse, setDownloadingCourse] = useState(false);
  const [downloadingModules, setDownloadingModules] = useState({});
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [moduleDownloadProgress, setModuleDownloadProgress] = useState({});
  const [downloadDetails, setDownloadDetails] = useState(null);
  const [moduleDownloadDetails, setModuleDownloadDetails] = useState({});
  const fromDashboard = location.state?.fromDashboard;
  useEffect(() => {
    if (downloadProgress > 0) {
      const progressElements = document.querySelectorAll(".progress-fill");
      const progressTexts = document.querySelectorAll(".progress-text");
      progressElements.forEach((el) => {
        el.style.width = `${downloadProgress}%`;
      });
      progressTexts.forEach((el) => {
        if (el.textContent.includes("Downloading...")) {
          el.textContent = `Downloading... ${downloadProgress}%`;
        }
      });
      setDownloadDetails((prev) =>
        prev ? { ...prev, lastUpdate: Date.now() } : null,
      );
    }
  }, [downloadProgress]);
  useEffect(() => {
    const moduleIds = Object.keys(moduleDownloadProgress);
    moduleIds.forEach((moduleId) => {
      const progress = moduleDownloadProgress[moduleId];
      if (progress > 0) {
        const progressElement = document.querySelector(
          `#module-progress-${moduleId}`,
        );
        const progressText = document.querySelector(
          `#module-progress-text-${moduleId}`,
        );
        if (progressElement) {
          progressElement.style.width = `${progress}%`;
        }
        if (progressText) {
          progressText.textContent = `Downloading... ${progress}%`;
        }
        setModuleDownloadDetails((prev) => {
          const newDetails = { ...prev };
          if (newDetails[moduleId]) {
            newDetails[moduleId] = {
              ...newDetails[moduleId],
              lastUpdate: Date.now(),
            };
          }
          return newDetails;
        });
      }
    });
  }, [moduleDownloadProgress]);
  useEffect(() => {
    const fetchCourseAndPurchaseStatus = async () => {
      try {
        setLoading(true);
        const coursePromise = fetch(`${API_BASE_URL}/courses/${id}`, {
          headers: user
            ? {
                Authorization: `Bearer ${user.accessToken || user.token}`,
              }
            : {},
        });
        const purchaseStatusPromise =
          user && !isAdmin
            ? fetch(`${API_BASE_URL}/purchases/my/courses`, {
                headers: {
                  Authorization: `Bearer ${user.accessToken || user.token}`,
                },
              })
            : null;
        const [courseResponse, purchaseStatusResponse] = await Promise.all(
          [coursePromise, purchaseStatusPromise].filter(Boolean),
        );
        if (!courseResponse.ok) {
          throw new Error(`Failed to fetch course: ${courseResponse.status}`);
        }
        const courseData = await courseResponse.json();
        setCourse(courseData);
        if (isAdmin) {
          setIsPurchased(true);
        } else if (!user) {
          setIsPurchased(false);
        } else if (purchaseStatusResponse) {
          const userCoursesData = await purchaseStatusResponse.json();
          const courseIds = Array.isArray(userCoursesData)
            ? userCoursesData.map((c) => c._id)
            : [];
          setIsPurchased(courseIds.includes(id));
        }
        setError(null);
      } catch (err) {
        console.error("Failed to fetch course:", err);
        setError(
          "Failed to load course. The course may not exist or there was a server error.",
        );
        setIsPurchased(false);
      } finally {
        setLoading(false);
      }
    };
    fetchCourseAndPurchaseStatus();
  }, [id, user, isAdmin]);
  const handlePurchase = async () => {
    try {
      if (!user || !user._id) {
        addToast("You must be logged in to purchase a course.", "error");
        return;
      }
      const purchaseData = {
        userId: user._id,
        courseId: id,
        amount: course.price,
        status: "Completed",
      };
      await purchaseService.createPurchase(purchaseData);
      setIsPurchased(true);
      addToast("Course purchased successfully!", "success");
    } catch (error) {
      console.error("Purchase failed:", error);
      addToast("Failed to purchase course. Please try again.", "error");
    }
  };
  const handleGoBack = () => {
    if (fromDashboard) {
      navigate("/dashboard");
    }
    else if (isAdmin) {
      navigate("/courses");
    }
    else {
      navigate("/courses/my");
    }
  };

  const handleDownloadCourse = async () => {
    try {
      setDownloadProgress(0);
      setDownloadingCourse(true);
      setTimeout(() => {
        const progressElements = document.querySelectorAll(".progress-fill");
        const progressTexts = document.querySelectorAll(".progress-text");
        progressElements.forEach((el) => {
          el.style.width = "0%";
        });
        progressTexts.forEach((el) => {
          if (el.textContent.includes("Downloading...")) {
            el.textContent = "Downloading... 0%";
          }
        });
      }, 50);
      const startTime = Date.now();
      let lastUpdate = startTime;
      let lastBytes = 0;
      let downloadSpeed = 0;
      let estimatedTimeLeft = 0;
      await courseService.downloadCourse(
        id,
        (progress, bytesReceived, totalBytes) => {
          setDownloadProgress(progress);
          const progressElements = document.querySelectorAll(".progress-fill");
          const progressTexts = document.querySelectorAll(".progress-text");
          progressElements.forEach((el) => {
            el.style.width = `${progress}%`;
          });
          progressTexts.forEach((el) => {
            el.textContent = `Downloading... ${progress}%`;
          });
          const now = Date.now();
          const timeDiff = now - lastUpdate;
          if (timeDiff > 500) {
            const bytesDiff = bytesReceived - lastBytes;
            downloadSpeed = ((bytesDiff / timeDiff) * 1000) / 1024;
            if (totalBytes && downloadSpeed > 0) {
              const bytesRemaining = totalBytes - bytesReceived;
              estimatedTimeLeft = bytesRemaining / (downloadSpeed * 1024);
            }
            setDownloadDetails({
              speed: downloadSpeed.toFixed(1),
              timeLeft:
                estimatedTimeLeft > 60
                  ? `${Math.ceil(estimatedTimeLeft / 60)} min`
                  : `${Math.ceil(estimatedTimeLeft)} sec`,
              bytesReceived: formatFileSize(bytesReceived),
              totalSize: formatFileSize(totalBytes || 0),
            });
            lastUpdate = now;
            lastBytes = bytesReceived;
          }
        },
      );
      addToast("Course downloaded successfully", "success");
    } catch (error) {
      console.error("Download error:", error);
      addToast(error.message || "Failed to download course", "error");
    } finally {
      setDownloadingCourse(false);
      setTimeout(() => {
        setDownloadProgress(0);
        setDownloadDetails(null);
      }, 1000);
    }
  };
  const handleDownloadModule = async (moduleId) => {
    try {
      setModuleDownloadProgress((prev) => ({ ...prev, [moduleId]: 0 }));
      setDownloadingModules((prev) => ({ ...prev, [moduleId]: true }));
      setTimeout(() => {
        const progressElement = document.querySelector(
          `#module-progress-${moduleId}`,
        );
        const progressText = document.querySelector(
          `#module-progress-text-${moduleId}`,
        );
        if (progressElement) {
          progressElement.style.width = "0%";
        }
        if (progressText) {
          progressText.textContent = "Downloading... 0%";
        }
      }, 50);
      const startTime = Date.now();
      let lastUpdate = startTime;
      let lastBytes = 0;
      let downloadSpeed = 0;
      let estimatedTimeLeft = 0;
      await courseService.downloadModuleFile(
        id,
        moduleId,
        (progress, bytesReceived, totalBytes) => {
          setModuleDownloadProgress((prev) => ({
            ...prev,
            [moduleId]: progress,
          }));
          const moduleProgressElement = document.querySelector(
            `#module-progress-${moduleId}`,
          );
          const moduleProgressText = document.querySelector(
            `#module-progress-text-${moduleId}`,
          );
          if (moduleProgressElement) {
            moduleProgressElement.style.width = `${progress}%`;
          }
          if (moduleProgressText) {
            moduleProgressText.textContent = `Downloading... ${progress}%`;
          }
          const now = Date.now();
          const timeDiff = now - lastUpdate;
          if (timeDiff > 500) {
            const bytesDiff = bytesReceived - lastBytes;
            downloadSpeed = ((bytesDiff / timeDiff) * 1000) / 1024;
            if (totalBytes && downloadSpeed > 0) {
              const bytesRemaining = totalBytes - bytesReceived;
              estimatedTimeLeft = bytesRemaining / (downloadSpeed * 1024);
            }
            setModuleDownloadDetails((prev) => ({
              ...prev,
              [moduleId]: {
                speed: downloadSpeed.toFixed(1),
                timeLeft:
                  estimatedTimeLeft > 60
                    ? `${Math.ceil(estimatedTimeLeft / 60)} min`
                    : `${Math.ceil(estimatedTimeLeft)} sec`,
                bytesReceived: formatFileSize(bytesReceived),
                totalSize: formatFileSize(totalBytes || 0),
              },
            }));
            lastUpdate = now;
            lastBytes = bytesReceived;
          }
        },
      );
      addToast("Module downloaded successfully", "success");
    } catch (error) {
      console.error("Module download error:", error);
      addToast(error.message || "Failed to download module", "error");
    } finally {
      setTimeout(() => {
        setDownloadingModules((prev) => ({ ...prev, [moduleId]: false }));
        setModuleDownloadProgress((prev) => ({ ...prev, [moduleId]: 0 }));
        setModuleDownloadDetails((prev) => ({ ...prev, [moduleId]: null }));
      }, 1000);
    }
  };
  if (loading) {
    return <Loader section="courses" text="Loading course content..." />;
  }
  if (error) {
    return (
      <>
        <BackButton variant="outline" onClick={handleGoBack}>
          <ArrowBack />
          {fromDashboard ? (
            <span>Back to Dashboard</span>
          ) : isAdmin ? (
            <span>Back to All Courses</span>
          ) : (
            <span>Back to My Courses</span>
          )}
        </BackButton>
        <ErrorMessage>{error}</ErrorMessage>
      </>
    );
  }
  if (!course) {
    return (
      <>
        <BackButton variant="outline" onClick={handleGoBack}>
          <ArrowBack />
          {fromDashboard ? (
            <span>Back to Dashboard</span>
          ) : isAdmin ? (
            <span>Back to All Courses</span>
          ) : (
            <span>Back to My Courses</span>
          )}
        </BackButton>
        <ErrorMessage>Course not found</ErrorMessage>
      </>
    );
  }
  const renderContent = (module) => {
    const isDownloading = downloadingModules[module._id];
    const progress = moduleDownloadProgress[module._id] || 0;
    const details = moduleDownloadDetails[module._id];
    const renderDownloadButton = () => {
      return (
        <VideoDownloadButton
          onClick={() => handleDownloadModule(module._id)}
          disabled={isDownloading}
          variant="outline"
          style={{
            marginTop: "12px",
            height: isDownloading ? "auto" : "32px",
            minHeight: isDownloading ? "80px" : "32px",
          }}
        >
          {isDownloading ? (
            <>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  marginBottom: "8px",
                }}
              >
                <LoadingSpinner variant="outline" />
                <span
                  id={`module-progress-text-${module._id}`}
                  style={{ marginLeft: "8px" }}
                >
                  Downloading... {progress}%
                </span>
              </div>
              <ProgressBar>
                <ProgressFill
                  id={`module-progress-${module._id}`}
                  progress={progress}
                  style={{
                    width: `${progress}%`,
                    backgroundColor: "#4CAF50",
                    minWidth: progress > 0 ? "5px" : "0",
                  }}
                />
              </ProgressBar>
              {details && (
                <ProgressText variant="outline">
                  {details.bytesReceived} of {details.totalSize} •{" "}
                  {details.speed} KB/s • {details.timeLeft} remaining
                </ProgressText>
              )}
            </>
          ) : (
            <>
              <GetApp style={{ width: "16px", height: "16px" }} />
              Download{" "}
              {module.type.charAt(0).toUpperCase() + module.type.slice(1)}
            </>
          )}
        </VideoDownloadButton>
      );
    };
    switch (module.type) {
      case "text":
        return (
          <>
            <p>{module.content}</p>
            {/* {renderDownloadButton()} */}
          </>
        );
      case "pdf":
        if (
          module.content &&
          (module.content.startsWith("/") || module.content.startsWith("http"))
        ) {
          return (
            <>
              <FileLink href={module.content} target="_blank">
                <FileIcon>
                  <InsertDriveFile />
                </FileIcon>
                View PDF: {module.content.split("/").pop()}
              </FileLink>
              {/* {renderDownloadButton()} */}
            </>
          );
        }
        return (
          <>
            <p>PDF Document: {module.content}</p>
            {/* {renderDownloadButton()} */}
          </>
        );
      case "image":
        if (
          module.content &&
          (module.content.startsWith("/") ||
            module.content.startsWith("http") ||
            module.content.startsWith("blob:"))
        ) {
          return (
            <>
              <ImageViewer src={module.content} alt={module.title} />
              {/* {renderDownloadButton()} */}
            </>
          );
        }
        return (
          <>
            <p>Image Resource: {module.content}</p>
            {/* {renderDownloadButton()} */}
          </>
        );
      case "video":
        if (
          module.content &&
          (module.content.startsWith("/") ||
            module.content.startsWith("http") ||
            module.content.startsWith("blob:"))
        ) {
          return (
            <>
              <VideoPlayer controls>
                <source src={module.content} />
                Your browser does not support the video tag.
              </VideoPlayer>
              {/* {renderDownloadButton()} */}
            </>
          );
        }
        return (
          <>
            <p>Video Resource: {module.content}</p>
            {renderDownloadButton()}
          </>
        );
      default:
        return (
          <>
            <p>{module.content}</p>
            {renderDownloadButton()}
          </>
        );
    }
  };
  return (
    <CourseDetailContainer>
      <BackButton variant="outline" onClick={handleGoBack}>
        <ArrowBack />
        {fromDashboard ? (
          <span>Back to Dashboard</span>
        ) : isAdmin ? (
          <span>Back to All Courses</span>
        ) : (
          <span>Back to My Courses</span>
        )}
      </BackButton>
      <HeaderActions>
        <PageTitle style={{ fontWeight: "bold" }}>
          {course.title.toUpperCase()}
        </PageTitle>
        {isAdmin && (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-end",
            }}
          >
            <EditButton
              as={Link}
              to={`/courses/${course._id}/edit`}
              style={{ height: "36px", marginTop: "0", minWidth: "120px" }}
            >
              <EditIcon /> Edit Course
            </EditButton>
          </div>
        )}
      </HeaderActions>
      <CourseImage src={course.image} alt={course.title.toUpperCase()} />
      {!isAdmin && !isPurchased && (
        <PurchaseStatus isPurchased={false}>
          <div>
            <strong>You don't own this course yet</strong>
            <p>Purchase this course to access all learning materials.</p>
          </div>
          <PurchaseButton onClick={handlePurchase}>
            Purchase for ${parseFloat(course.price).toFixed(2)}
          </PurchaseButton>
        </PurchaseStatus>
      )}
      <CourseInfo>
        <h2 style={{ fontWeight: "bold", fontSize: "1.8rem" }}>
          Course Description
        </h2>
        <p>{course.description}</p>
        <Price>${parseFloat(course.price).toFixed(2)}</Price>
        {(isAdmin || isPurchased) && (
          <div style={{ marginTop: "20px" }}>
            <h3 style={{ marginBottom: "10px", fontWeight: "bold" }}>
              Download Complete Course
            </h3>
            <p
              style={{
                marginBottom: "10px",
                fontSize: "0.9rem",
                color: "#666",
              }}
            >
              Download the entire course as a ZIP file including all modules
              (text, PDFs, videos, and images).
            </p>
            <DownloadButton
              variant="primary"
              onClick={handleDownloadCourse}
              disabled={downloadingCourse}
              downloading={downloadingCourse}
              style={{
                marginTop: "10px",
                height: downloadingCourse ? "auto" : "42px",
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                padding: "12px 20px",
                fontSize: "1rem",
                fontWeight: "bold",
              }}
            >
              <Download
                style={{ marginRight: "10px", width: "20px", height: "20px" }}
              />
              <ButtonText>
                {downloadingCourse ? (
                  <>
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        marginBottom: "10px",
                      }}
                    >
                      <LoadingSpinner />
                      <span
                        className="progress-text"
                        style={{ marginLeft: "10px", fontWeight: "bold" }}
                      >
                        Downloading... {downloadProgress}%
                      </span>
                    </div>
                    <ProgressBar style={{ height: "6px", borderRadius: "3px" }}>
                      <ProgressFill
                        className="progress-fill"
                        progress={downloadProgress}
                        style={{
                          width: `${downloadProgress}%`,
                          backgroundColor: "#4CAF50",
                          minWidth: downloadProgress > 0 ? "5px" : "0",
                          height: "6px",
                          borderRadius: "3px",
                        }}
                      />
                    </ProgressBar>
                    {downloadDetails && (
                      <ProgressText
                        style={{ marginTop: "8px", fontSize: "0.85rem" }}
                      >
                        {downloadDetails.bytesReceived} of{" "}
                        {downloadDetails.totalSize} • {downloadDetails.speed}{" "}
                        KB/s • {downloadDetails.timeLeft} remaining
                      </ProgressText>
                    )}
                  </>
                ) : (
                  <>Download (All Modules)</>
                )}
              </ButtonText>
            </DownloadButton>
          </div>
        )}
      </CourseInfo>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "15px",
        }}
      >
        <h2 style={{ fontWeight: "bold", fontSize: "1.8rem", margin: 0 }}>
          Course Modules
        </h2>
        <div
          style={{
            backgroundColor: "rgba(0, 159, 159, 0.1)",
            padding: "5px 12px",
            borderRadius: "20px",
            fontSize: "0.9rem",
            border: "1px solid rgba(0, 159, 159, 0.3)",
            color: "#009F9F",
            fontWeight: "bold",
          }}
        >
          {course.modules?.length || 0}{" "}
          {course.modules?.length === 1 ? "Module" : "Modules"}
        </div>
      </div>
      {course.modules && course.modules.length > 0 ? (
        course.modules.map((module, index) => (
          <ModuleCard key={index}>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                marginBottom: "5px",
              }}
            >
              <div
                style={{
                  backgroundColor: "#1976d2",
                  color: "white",
                  borderRadius: "50%",
                  width: "28px",
                  height: "28px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  marginRight: "10px",
                  fontWeight: "bold",
                  fontSize: "0.9rem",
                }}
              >
                {index + 1}
              </div>
              <ModuleTitle style={{ margin: 0 }}>
                {module.title.toUpperCase()}
              </ModuleTitle>
            </div>
            <ModuleType>
              {module.type === "text" && (
                <InsertDriveFile
                  style={{
                    width: "16px",
                    height: "16px",
                    marginRight: "5px",
                    verticalAlign: "middle",
                  }}
                />
              )}
              {module.type === "pdf" && (
                <InsertDriveFile
                  style={{
                    width: "16px",
                    height: "16px",
                    marginRight: "5px",
                    verticalAlign: "middle",
                  }}
                />
              )}
              {module.type === "video" && (
                <VideoLibrary
                  style={{
                    width: "16px",
                    height: "16px",
                    marginRight: "5px",
                    verticalAlign: "middle",
                  }}
                />
              )}
              {module.type === "image" && (
                <Image
                  style={{
                    width: "16px",
                    height: "16px",
                    marginRight: "5px",
                    verticalAlign: "middle",
                  }}
                />
              )}
              Content Type:{" "}
              {module.type.charAt(0).toUpperCase() + module.type.slice(1)}
            </ModuleType>
            {isAdmin || isPurchased ? (
              renderContent(module)
            ) : (
              <p>Purchase this course to access this content.</p>
            )}
          </ModuleCard>
        ))
      ) : (
        <div
          style={{
            padding: "20px",
            textAlign: "center",
            backgroundColor: "#f9f9f9",
            borderRadius: "8px",
            border: "1px dashed #ddd",
            marginTop: "10px",
          }}
        >
          <p style={{ margin: 0, color: "#666" }}>
            This course doesn't have any modules yet.
          </p>
        </div>
      )}
    </CourseDetailContainer>
  );
};
export default Courses;
