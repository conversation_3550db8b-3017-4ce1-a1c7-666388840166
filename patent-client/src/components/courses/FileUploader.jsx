import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { Button } from '../../styles/StyledComponents';
import { CloudUpload, InsertDriveFile, VideoLibrary, Image, Error } from '@styled-icons/material';
const UploaderContainer = styled.div`
  width: 100%;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  .file-preview-wrapper {
    display: inline-block;
    width: auto;
  }
`;
const UploadButton = styled(Button)`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing.sm};
  width: auto;
  min-width: 180px;
  max-width: 300px;
  padding: 8px 16px;
  height: auto;
  background-color: ${({ theme }) => theme.colors.light};
  color: ${({ theme }) => theme.colors.primary};
  border: 1px solid ${({ theme }) => theme.colors.primary};
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  &:hover {
    background-color: ${({ theme }) => theme.colors.primary};
    color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }
  svg {
    width: 18px;
    height: 18px;
  }
`;
const FileInput = styled.input`
  display: none;
`;
const FilePreview = styled.div`
  margin-top: ${({ theme }) => theme.spacing.md};
  border: 1px solid ${({ theme }) => theme.colors.primary};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  padding: ${({ theme }) => theme.spacing.md};
  background-color: rgba(0, 123, 255, 0.05);
  display: inline-flex;
  flex-direction: column;
  width: auto;
  max-width: fit-content;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
`;
const FileItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.spacing.sm};
  background-color: white;
  padding: 6px 10px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  width: fit-content;
  min-width: 280px;
  &:last-child {
    margin-bottom: 0;
  }
`;
const FileIcon = styled.span`
  margin-right: ${({ theme }) => theme.spacing.sm};
  color: ${({ theme }) => theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  svg {
    width: 22px;
    height: 22px;
  }
`;
const FileName = styled.span`
  flex: 1;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  color: #333;
  margin-right: 8px;
  max-width: 180px;
`;
const RemoveButton = styled.button`
  background-color: #f8f9fa;
  color: ${({ theme }) => theme.colors.danger};
  border: 1px solid ${({ theme }) => theme.colors.danger};
  border-radius: 4px;
  cursor: pointer;
  padding: 4px 8px;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
  margin-left: 12px;
  &:hover {
    background-color: ${({ theme }) => theme.colors.danger};
    color: white;
  }
  &:active {
    transform: translateY(1px);
  }
`;
const ImagePreviewContainer = styled.div`
  display: inline-block;
  margin-top: ${({ theme }) => theme.spacing.md};
  align-self: flex-start;
  background-color: white;
  padding: 4px;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-width: fit-content;
`;
const PreviewImage = styled.img`
  max-width: 250px;
  max-height: 120px;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  border: 2px solid ${({ theme }) => theme.colors.primary};
  transition: transform 0.3s ease;
  object-fit: cover;
  display: block;
  &:hover {
    transform: scale(1.02);
  }
`;
const ErrorMessage = styled.div`
  color: white;
  font-size: 0.8rem;
  margin-top: ${({ theme }) => theme.spacing.xs};
  display: flex;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.danger};
  padding: 6px 10px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
  svg {
    width: 16px;
    height: 16px;
    margin-right: ${({ theme }) => theme.spacing.xs};
  }
`;
const FileInfo = styled.div`
  font-size: 0.8rem;
  color: ${({ theme }) => theme.colors.text?.secondary || '#6c757d'};
  margin-top: ${({ theme }) => theme.spacing.xs};
  background-color: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  border-left: 3px solid ${({ theme }) => theme.colors.primary};
  margin-left: 2px;
  display: inline-block;
  width: fit-content;
`;
const FileUploader = ({ type = 'image', value, onChange, disabled = false, fieldName = 'file' }) => {
  const [filePreview, setFilePreview] = useState(null);
  const [error, setError] = useState(null);
  const fileInputRef = useRef(null);
  const fileSizeLimits = {
    image: 5 * 1024 * 1024,
    pdf: 20 * 1024 * 1024,
    video: 100 * 1024 * 1024
  };
  const acceptedTypes = {
    image: 'image/*',
    pdf: 'application/pdf',
    video: 'video/*'
  };

  const fileType = type || 'image';

  useEffect(() => {
    if (value) {
      if (typeof value === 'string') {
        setFilePreview({
          name: value.split('/').pop(),
          url: value,
          isExisting: true
        });
      } else if (value instanceof File) {
        handleFileSelected(value);
      }
    } else {
      setFilePreview(null);
    }
  }, [value]);

  const handleFileSelected = (file) => {
    if (!file) return;

    // Check file size
    if (fileSizeLimits[fileType] && file.size > fileSizeLimits[fileType]) {
      setError(`File size exceeds the limit (${formatFileSize(fileSizeLimits[fileType])})`);
      return;
    }

    const previewUrl = URL.createObjectURL(file);

    setFilePreview({
      name: file.name,
      size: file.size,
      url: previewUrl,
      isExisting: false
    });

    setError(null);

    if (onChange) {
      onChange(file);
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      handleFileSelected(file);
    }
  };

  const handleRemoveFile = () => {
    setFilePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    if (onChange) {
      onChange(null);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes < 1024) return bytes + ' bytes';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };

  const getFileIcon = (type) => {
    switch (type) {
      case 'image':
        return <Image />;
      case 'video':
        return <VideoLibrary />;
      case 'pdf':
        return <InsertDriveFile />;
      default:
        return <InsertDriveFile />;
    }
  };

  return (
    <UploaderContainer>
      {!filePreview ? (
        <>
          <UploadButton
            as="label"
            htmlFor={`file-upload-${fieldName}`}
            disabled={disabled}
          >
            <CloudUpload />
            Upload {fileType.charAt(0).toUpperCase() + fileType.slice(1)}
          </UploadButton>
          <FileInput
            id={`file-upload-${fieldName}`}
            type="file"
            accept={acceptedTypes[fileType]}
            ref={fileInputRef}
            onChange={handleFileChange}
            disabled={disabled}
            name={fieldName}
          />
        </>
      ) : (
        <FilePreview>
          <FileItem>
            <FileIcon>
              {getFileIcon(fileType)}
            </FileIcon>
            <FileName>{filePreview.name}</FileName>
            <RemoveButton type="button" onClick={handleRemoveFile} disabled={disabled} title="Remove file">
              <svg width="16" height="16" viewBox="0 0 24 24">
                <path fill="currentColor" d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z" />
              </svg>
            </RemoveButton>
          </FileItem>
          {!filePreview.isExisting && filePreview.size && (
            <FileInfo>Size: {formatFileSize(filePreview.size)}</FileInfo>
          )}
          {filePreview.isExisting && (
            <FileInfo>Current file: {filePreview.name}</FileInfo>
          )}
          {filePreview.url && fileType === 'image' && (
            <ImagePreviewContainer>
              <PreviewImage src={filePreview.url} alt="Preview" />
            </ImagePreviewContainer>
          )}
        </FilePreview>
      )}
      {error && (
        <ErrorMessage>
          <Error />
          {error}
        </ErrorMessage>
      )}
    </UploaderContainer>
  );
};
export default FileUploader;