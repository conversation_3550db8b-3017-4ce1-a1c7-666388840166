import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import styled from "styled-components";
import {
  PageTitle,
  Button,
  Grid,
  Column,
  SearchInput,
} from "../../styles/StyledComponents";
import Loader from "../layout/Loader";
import { useToast } from "../../contexts/ToastContext";
import { useAuth } from "../../contexts/AuthContext";
import Pagination from "../common/Pagination";
import { API_BASE_URL } from "../../config/config.js";
const CourseHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    h1 {
      margin-bottom: ${({ theme }) => theme.spacing.md};
    }
  }
`;
const SearchContainer = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const StyledSearchInput = styled(SearchInput)`
  width: 100%;
  max-width: 500px;
`;
const CourseCardWrapper = styled.div`
  position: relative;
  height: 100%;
`;
const CourseCard = styled(Link)`
  display: block;
  background-color: ${({ theme }) => theme.colors.white};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  overflow: hidden;
  transition:
    transform 0.2s,
    box-shadow 0.2s;
  height: 100%;
  text-decoration: none;
  color: inherit;
  &:hover {
    transform: translateY(-5px);
    box-shadow: ${({ theme }) => theme.shadows.md};
  }
`;
const CourseImage = styled.img`
  width: 100%;
  height: 180px;
  object-fit: cover;
`;
const CourseContent = styled.div`
  padding: ${({ theme }) => theme.spacing.md};
`;
const CourseTitle = styled.h3`
  margin: 0 0 ${({ theme }) => theme.spacing.sm};
  font-size: 1.1rem;
  font-weight: bold;
`;
const CourseDescription = styled.p`
  margin: 0 0 ${({ theme }) => theme.spacing.md};
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: 0.9rem;
`;
const CoursePrice = styled.div`
  font-weight: 600;
  color: ${({ theme }) => theme.colors.primary};
`;
const PurchaseTag = styled.div`
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: ${({ theme }) => theme.colors.success};
  color: white;
  padding: 4px 8px;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: 0.8rem;
  font-weight: 500;
  z-index: 1;
`;
const NoCoursesMessage = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing.xl};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const MyCourses = () => {
  const [courses, setCourses] = useState([]);
  const [filteredCourses, setFilteredCourses] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState(6);
  const location = useLocation();
  const { addToast } = useToast();
  const { user } = useAuth();
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        try {
          const purchasesResponse = await fetch(`${API_BASE_URL}/purchases/my`, {
            headers: {
              Authorization: `Bearer ${user.accessToken || user.token}`,
            },
          });
          if (!purchasesResponse.ok) {
            throw new Error(
              `Failed to fetch purchases: ${purchasesResponse.status}`,
            );
          }
          const purchasesData = await purchasesResponse.json();
          const purchases = Array.isArray(purchasesData)
            ? purchasesData
            : purchasesData && purchasesData.purchases
              ? purchasesData.purchases
              : [];
          const courseIds = purchases
            .filter((purchase) => purchase.courseId)
            .map((purchase) => {
              return typeof purchase.courseId === "object"
                ? purchase.courseId._id
                : purchase.courseId;
            })
            .filter(Boolean);
          if (courseIds.length === 0) {
            setCourses([]);
            setFilteredCourses([]);
            setError(null);
            setLoading(false);
            return;
          }
          const userCourses = [];
          for (const courseId of courseIds) {
            try {
              const courseResponse = await fetch(
                `${API_BASE_URL}/courses/${courseId}`,
                {
                  headers: {
                    Authorization: `Bearer ${user.accessToken || user.token}`,
                  },
                },
              );
              if (courseResponse.ok) {
                const courseData = await courseResponse.json();
                if (courseData) {
                  userCourses.push(courseData);
                }
              }
            } catch (error) {
              console.error(`Error fetching course ${courseId}:`, error);
            }
          }
          setCourses(userCourses);
          setFilteredCourses(userCourses);
          setPagination(null);
        } catch (error) {
          console.error("Error fetching user courses:", error);
          setCourses([]);
          setFilteredCourses([]);
          setPagination(null);
        }
        setError(null);
      } catch (err) {
        console.error("Failed to fetch course data:", err);
        setError("Failed to load courses. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    if (user) {
      fetchData();
    }
  }, [location, user]);
  useEffect(() => {
    if (courses.length > 0) {
      const filtered = courses.filter(
        (course) =>
          course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          course.description.toLowerCase().includes(searchTerm.toLowerCase()),
      );
      setFilteredCourses(filtered);
    }
  }, [courses, searchTerm]);
  if (loading) {
    return <Loader section="courses" />;
  }
  if (error) {
    return <NoCoursesMessage>{error}</NoCoursesMessage>;
  }
  return (
    <div>
      <CourseHeader>
        <PageTitle>My Purchased Courses</PageTitle>
      </CourseHeader>
      <SearchContainer>
        <StyledSearchInput
          placeholder="Search your courses..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </SearchContainer>
      <Grid columns={3} gap="lg">
        {filteredCourses.map((course) => {
          if (!course || !course._id) {
            return null;
          }
          return (
            <Column key={course._id} span={1}>
              <CourseCardWrapper>
                <PurchaseTag>Purchased</PurchaseTag>
                <CourseCard as={Link} to={`/courses/${course._id}`}>
                  <CourseImage src={course.image} alt={course.title} />
                  <CourseContent>
                    <CourseTitle>{course.title.toUpperCase()}</CourseTitle>
                    <CourseDescription>
                      {course.description.length > 100
                        ? `${course.description.substring(0, 97)}...`
                        : course.description}
                    </CourseDescription>
                    <CoursePrice>
                      ${parseFloat(course.price).toFixed(2)}
                    </CoursePrice>
                  </CourseContent>
                </CourseCard>
              </CourseCardWrapper>
            </Column>
          );
        })}
      </Grid>
      {filteredCourses.length === 0 && !loading && (
        <NoCoursesMessage>
          {searchTerm ? (
            `No courses found for "${searchTerm}". Try a different search.`
          ) : (
            <>
              You haven't purchased any courses yet.{" "}
              <a
                href="/course"
                style={{ color: "#009F9F", textDecoration: "underline" }}
              >
                Browse courses to purchase
              </a>
              .
            </>
          )}
        </NoCoursesMessage>
      )}
      {}
      {pagination && !searchTerm && pagination.totalPages > 1 && (
        <Pagination
          pagination={pagination}
          onPageChange={(page) => {
            setCurrentPage(page);
            window.scrollTo(0, 0);
          }}
          variant="default"
        />
    )} 
    </div>
  );
};
export default MyCourses;