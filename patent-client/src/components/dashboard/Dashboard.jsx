import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";
import { PageTitle, Card, Grid } from "../../styles/StyledComponents";
import * as userService from "../../services/userService";
import * as bookService from "../../services/bookService";
import * as courseService from "../../services/courseService";
import * as purchaseService from "../../services/purchaseService";
import Loader from "../layout/Loader";
import { useAuth } from "../../contexts/AuthContext";
import {
  School as CourseIcon,
  People as UsersIcon,
  ShoppingCart as PurchasesIcon,
  Book as BookIcon,
} from "@styled-icons/material";
const DashboardContainer = styled.div`
  width: 100%;
  padding: 0 ${({ theme }) => theme.spacing.sm};
  padding-right: 20px;
     margin-bottom: ${({ theme }) => theme.spacing.lg};
  max-width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
  height: calc(100vh - 80px);
  scrollbar-width: thin;
  scrollbar-color: #009f9f #f1f1f1;
  background-color: #ffffff; /* White background like landing page */
  &::-webkit-scrollbar {
    width: 12px;
    height: 12px;
    position: absolute;
    right: 0;
  }
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 0;
  }
  &::-webkit-scrollbar-thumb {
    background: #009f9f;
    border-radius: 6px;
    border: 2px solid #f1f1f1;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: #018a8a;
  }
  & + div {
    scrollbar-width: thin;
    scrollbar-color: #009f9f #f1f1f1;
  }
  @media (max-width: 768px) {
    padding: 0 ${({ theme }) => theme.spacing.xs};
    padding-right: 16px;
       h1 {
      margin-bottom: ${({ theme }) => theme.spacing.md};
    }
  }
`;
const StatsGrid = styled(Grid)`
  margin-bottom: ${({ theme }) => theme.spacing.xl};
  width: 100%;
  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: ${({ theme }) => theme.spacing.md} !important;
  }
  @media (max-width: 480px) {
    grid-template-columns: 1fr !important;
  }
`;
const StatCard = styled(Card)`
  display: flex;
  align-items: center;
  cursor: pointer;
  transition:
    transform 0.2s,
    box-shadow 0.2s;
  margin-bottom: 10px;
  &:hover {
    transform: translateY(-5px);
    box-shadow: ${({ theme }) => theme.shadows.lg};
  }
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing.md};
  }
  @media (max-width: 480px) {
    padding: ${({ theme }) => theme.spacing.sm};
  }
`;
const StatIcon = styled.div`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: ${({ color }) =>
    color === "primary" ? "rgba(0, 159, 159, 0.2)" :
    color === "warning" ? "rgba(0, 159, 159, 0.15)" :
    color === "success" ? "rgba(0, 159, 159, 0.1)" :
    "rgba(0, 159, 159, 0.25)"
  };
  color: #009F9F; /* Teal color for all icons */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: ${({ theme }) => theme.spacing.lg};
  svg {
    width: 24px;
    height: 24px;
  }
  @media (max-width: 480px) {
    width: 40px;
    height: 40px;
    margin-right: ${({ theme }) => theme.spacing.md};
    svg {
      width: 20px;
      height: 20px;
    }
  }
`;
const StatContent = styled.div`
  h3 {
    font-size: 1.75rem;
    font-weight: 600;
    color: #009F9F; /* Teal color for numbers */
    margin-bottom: ${({ theme }) => theme.spacing.xs};
  }
  p {
    font-size: 0.875rem;
    color: ${({ theme }) => theme.colors.text};
    opacity: 0.7;
    margin: 0;
  }
  @media (max-width: 480px) {
    h3 {
      font-size: 1.5rem;
    }
    p {
      font-size: 0.8rem;
    }
  }
`;
const RecentSection = styled.div`
  margin-top: ${({ theme }) => theme.spacing.xl};
  width: 100%;
  position: relative;
  margin-top: ${({ theme }) => theme.spacing.xxl};
  padding-top: ${({ theme }) => theme.spacing.xl};
  border-top: 1px solid #e0e0e0;
  @media (max-width: 768px) {
    margin-top: ${({ theme }) => theme.spacing.xl};
    padding-top: ${({ theme }) => theme.spacing.lg};
  }
`;
const SectionHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  width: 100%;
  h2 {
    font-size: 1.5rem;
    color: #009F9F; /* Teal color for section headers */
    font-weight: 700;
    position: relative;
  }
  @media (max-width: 480px) {
    h2 {
      font-size: 1.25rem;
    }
  }
`;
const CourseList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  grid-gap: ${({ theme }) => theme.spacing.lg};
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
  padding-bottom: ${({ theme }) => theme.spacing.md};
  position: relative;
  &::before {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 8px;
    background: #f1f1f1;
    border-radius: 10px;
    z-index: 4;
  }
  &::after {
    content: "";
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 40px;
    background: linear-gradient(
      to left,
      rgba(255, 255, 255, 0.95),
      rgba(255, 255, 255, 0)
    );
    pointer-events: none;
    z-index: 5;
  }
  &:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 40px;
    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 0.95),
      rgba(255, 255, 255, 0)
    );
    pointer-events: none;
    z-index: 5;
  }
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }
  ::-webkit-scrollbar-thumb {
    background: #009f9f;
    border-radius: 10px;
    border: 2px solid #f1f1f1;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: #018a8a;
  }
  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    grid-gap: ${({ theme }) => theme.spacing.md};
  }
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    grid-gap: ${({ theme }) => theme.spacing.md};
  }
`;
const CourseCard = styled(Card)`
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 360px;
  cursor: pointer;
  transition:
    transform 0.2s,
    box-shadow 0.2s;
  &:hover {
    transform: translateY(-5px);
    box-shadow: ${({ theme }) => theme.shadows.lg};
  }
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing.md};
    min-height: 340px;
  }
  @media (max-width: 480px) {
    padding: ${({ theme }) => theme.spacing.sm};
    min-height: 320px;
  }
`;
const CourseImage = styled.img`
  width: 100%;
  height: 140px;
  object-fit: cover;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  @media (max-width: 768px) {
    height: 120px;
    margin-bottom: ${({ theme }) => theme.spacing.sm};
  }
`;
const CourseTitle = styled.h3`
  font-size: 1.125rem;
  margin-bottom: ${({ theme }) => theme.spacing.sm};
  font-weight: bold;
  @media (max-width: 480px) {
    font-size: 1rem;
  }
`;
const CourseDescription = styled.p`
  margin-bottom: ${({ theme }) => theme.spacing.md};
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 2.8em;
  @media (max-width: 480px) {
    font-size: 0.9rem;
    margin-bottom: ${({ theme }) => theme.spacing.sm};
  }
`;
const CoursePrice = styled.div`
  font-weight: 600;
  font-size: 1.5rem;
  color: #009F9F; /* Teal color for price */
  // margin-top: auto;
  font-size: 1.25rem;
  @media (max-width: 480px) {
    padding-top: ${({ theme }) => theme.spacing.sm};
  }
`;
const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colors.error};
  padding: ${({ theme }) => theme.spacing.md};
  border: 1px solid ${({ theme }) => theme.colors.error};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin: ${({ theme }) => theme.spacing.lg} 0;
`;
const BookList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  grid-gap: ${({ theme }) => theme.spacing.lg};
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
  padding-bottom: ${({ theme }) => theme.spacing.md};
  position: relative;
  &::before {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 8px;
    background: #f1f1f1;
    border-radius: 10px;
    z-index: 4;
  }
  &::after {
    content: "";
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 40px;
    background: linear-gradient(
      to left,
      rgba(255, 255, 255, 0.95),
      rgba(255, 255, 255, 0)
    );
    pointer-events: none;
    z-index: 5;
  }
  &:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 40px;
    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 0.95),
      rgba(255, 255, 255, 0)
    );
    pointer-events: none;
    z-index: 5;
  }
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }
  ::-webkit-scrollbar-thumb {
    background: #009f9f;
    border-radius: 10px;
    border: 2px solid #f1f1f1;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: #018a8a;
  }
  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    grid-gap: ${({ theme }) => theme.spacing.md};
  }
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    grid-gap: ${({ theme }) => theme.spacing.md};
  }
`;
const BookCard = styled(Card)`
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 360px;
  cursor: pointer;
  transition:
    transform 0.2s,
    box-shadow 0.2s;
  &:hover {
    transform: translateY(-5px);
    box-shadow: ${({ theme }) => theme.shadows.lg};
  }
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing.md};
    min-height: 340px;
  }
  @media (max-width: 480px) {
    padding: ${({ theme }) => theme.spacing.sm};
    min-height: 320px;
  }
`;
const BookImage = styled.img`
  width: 100%;
  height: 140px;
  object-fit: cover;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  @media (max-width: 768px) {
    height: 120px;
    margin-bottom: ${({ theme }) => theme.spacing.sm};
  }
`;
const BookTitle = styled.h3`
  font-size: 1.125rem;
  margin-bottom: ${({ theme }) => theme.spacing.sm};
  font-weight: bold;
  @media (max-width: 480px) {
    font-size: 1rem;
  }
`;
const BookDescription = styled.p`
  margin-bottom: ${({ theme }) => theme.spacing.md};
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 2.8em;
  @media (max-width: 480px) {
    font-size: 0.9rem;
    margin-bottom: ${({ theme }) => theme.spacing.sm};
  }
`;
const BookPrice = styled.div`
  font-weight: 600;
  color: #009F9F; /* Teal color for price */
  // margin-top: auto;
  font-size: 1.25rem;
  @media (max-width: 480px) {
    padding-top: ${({ theme }) => theme.spacing.sm};
  }
`;
const Dashboard = () => {
  const navigate = useNavigate();
  const [courses, setCourses] = useState([]);
  const [purchases, setPurchases] = useState([]);
  const [users, setUsers] = useState([]);
  const [books, setBooks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useAuth();
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        if (user && user.isAdmin) {
          try {
            const booksData = await bookService.getAllBooksForDashboard();
            setBooks(booksData || []);
          } catch (error) {
            console.error("Error fetching books for dashboard:", error);
            setBooks([]);
          }
          try {
            const coursesData = await courseService.getCoursesForDashboard();
            setCourses(coursesData || []);
          } catch (error) {
            console.error("Error fetching courses for dashboard:", error);
            setCourses([]);
          }
          try {
            const purchasesData =
              await purchaseService.getAllPurchasesForDashboard();
            if (purchasesData && typeof purchasesData.totalCount === "number") {
              setPurchases([...Array(purchasesData.totalCount)]);
            } else {
              setPurchases([]);
            }
          } catch (error) {
            console.error("Error fetching purchases count:", error);
            setPurchases([]);
          }
          try {
            const usersData = await userService.getUsersForDashboard();
            if (Array.isArray(usersData)) {
              setUsers(usersData);
            } else {
              setUsers([]);
            }
          } catch (error) {
            console.error("Error fetching users for dashboard:", error);
            setUsers([]);
          }
        } else {
          try {
            const purchasesData =
              await purchaseService.getUserPurchasesForDashboard();
            if (purchasesData && typeof purchasesData.totalCount === "number") {
              setPurchases([...Array(purchasesData.totalCount)]);
            } else {
              setPurchases([]);
            }
          } catch (error) {
            console.error("Error fetching user purchases count:", error);
            setPurchases([]);
          }
          try {
            const booksData = await bookService.getMyBooksForDashboard();
            "User books data for dashboard:", booksData;
            setBooks(booksData || []);
          } catch (error) {
            console.error("Error fetching user books for dashboard:", error);
            setBooks([]);
          }
          try {
            const coursesData = await courseService.getMyCoursesForDashboard();
            setCourses(coursesData || []);
          } catch (error) {
            console.error("Error fetching user courses for dashboard:", error);
            setCourses([]);
          }
          setUsers([user]);
        }
        setError(null);
      } catch (err) {
        console.error("Failed to fetch dashboard data:", err);
        setError("Failed to load dashboard data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    if (user) {
      fetchDashboardData();
    }
  }, [user]);
  if (loading) {
    return <Loader section="dashboard" />;
  }
  if (error) {
    return (
      <DashboardContainer>
        <PageTitle>Dashboard</PageTitle>
        <ErrorMessage>{error}</ErrorMessage>
      </DashboardContainer>
    );
  }
  return (
    <DashboardContainer>
      <PageTitle style={{marginBottom:"20px"}}>
        {user && user.isAdmin ? "Admin Dashboard" : "My Dashboard"}
      </PageTitle>
      <StatsGrid columns={user && user.isAdmin ? 4 : 3} gap="lg">
        <StatCard
          onClick={() =>
            navigate(user && user.isAdmin ? "/courses" : "/courses/my")
          }
          variant="beige" /* Using beige background for first card */
        >
          <StatIcon color="primary">
            <CourseIcon />
          </StatIcon>
          <StatContent>
            <h3>{courses.length}</h3>
            <p>{user && user.isAdmin ? "Total Courses" : "My Courses"}</p>
          </StatContent>
        </StatCard>
        <StatCard
          onClick={() =>
            navigate(user && user.isAdmin ? "/books" : "/books/my")
          }
        >
          <StatIcon color="warning">
            <BookIcon />
          </StatIcon>
          <StatContent>
            <h3>{books.length}</h3>
            <p>{user && user.isAdmin ? "Total Books" : "My Books"}</p>
          </StatContent>
        </StatCard>
        {user && user.isAdmin && (
          <StatCard onClick={() => navigate("/users")} variant="beige" /* Using beige background for third card */>
            <StatIcon color="success">
              <UsersIcon />
            </StatIcon>
            <StatContent>
              <h3>{users.length}</h3>
              <p>Registered Users</p>
            </StatContent>
          </StatCard>
        )}
        <StatCard onClick={() => navigate("/purchases")}>
          <StatIcon color="info">
            <PurchasesIcon />
          </StatIcon>
          <StatContent>
            <h3>{purchases.length}</h3>
            <p>{user && user.isAdmin ? "Total Purchases" : "My Purchases"}</p>
          </StatContent>
        </StatCard>
      </StatsGrid>
      <RecentSection>
        <SectionHeader>
          <h2>{user && user.isAdmin ? "Recent Courses" : "My Courses"}</h2>
          <div
            style={{
              cursor: "pointer",
              color: "#009F9F",
              fontWeight: "bold",
              backgroundColor: "#009F9F10",
              padding: "8px 16px",
              borderRadius: "4px"
            }}
            onClick={() =>
              navigate(user && user.isAdmin ? "/courses" : "/courses/my")
            }
          >
            View All
          </div>
        </SectionHeader>
        {courses.length === 0 ? (
          <Card>
            <p style={{ textAlign: "center", padding: "20px" }}>
              {user && user.isAdmin
                ? "No courses available. Add a new course to get started."
                : "You haven't purchased any courses yet."}
            </p>
          </Card>
        ) : (
          <CourseList>
            {courses.slice(0, 4).map((course, index) => (
              <CourseCard
                key={course._id}
                onClick={() =>
                  navigate(`/courses/${course._id}`, {
                    state: { fromDashboard: true },
                  })
                }
                variant={index % 2 === 0 ? "beige" : undefined} /* Alternating beige background */
              >
                <CourseImage src={course.image} alt={course.title} />
                <div style={{ display: 'flex', flexDirection: 'column', flexGrow: 1 }}>
                  <CourseTitle>{course.title.toUpperCase()}</CourseTitle>
                  <CourseDescription>{course.description}</CourseDescription>
                  <CoursePrice>
                    ${parseFloat(course.price).toFixed(2)}
                  </CoursePrice>
                </div>
              </CourseCard>
            ))}
          </CourseList>
        )}
      </RecentSection>
      <RecentSection>
        <SectionHeader>
          <h2>{user && user.isAdmin ? "Recent Books" : "My Books"}</h2>
          {}
          <div
            style={{
              cursor: "pointer",
              color: "#009F9F",
              fontWeight: "bold",
              backgroundColor: "#009F9F10",
              padding: "8px 16px",
              borderRadius: "4px"
            }}
            onClick={() =>
              navigate(user && user.isAdmin ? "/books" : "/books/my")
            }
          >
            View All
          </div>
        </SectionHeader>
        {books.length === 0 ? (
          <Card>
            <p style={{ textAlign: "center", padding: "20px" }}>
              {user && user.isAdmin
                ? "No books available. Add a new book to get started."
                : "You haven't purchased any books yet."}
            </p>
          </Card>
        ) : (
          <BookList>
            {books.slice(0, 4).map((book, index) => (
              <BookCard
                key={book._id}
                onClick={() =>
                  navigate(`/books/${book._id}`, {
                    state: {
                      fromDashboard: true,
                      returnPath: user && user.isAdmin ? "/books" : "/books/my",
                    },
                  })
                }
                variant={index % 2 !== 0 ? "beige" : undefined} /* Alternating beige background, opposite to courses */
              >
                <BookImage src={book.image} alt={book.title} />
                <div style={{ display: 'flex', flexDirection: 'column', flexGrow: 1 }}>
                  <BookTitle>{book.title}</BookTitle>
                  <BookDescription>{book.description}</BookDescription>
                  <BookPrice>${parseFloat(book.price).toFixed(2)}</BookPrice>
                </div>
              </BookCard>
            ))}
          </BookList>
        )}
      </RecentSection>
    </DashboardContainer>
  );
};
export default Dashboard;
