import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { useNavigate, useParams } from "react-router-dom";
import { Upload, X, BookOpen, Video, Save, ArrowLeft } from "lucide-react";
import apiRequest from "../../services/api";
import { useToast } from "../../contexts/ToastContext";
import { getAllBooks, getAllBooksForDashboard } from "../../services/bookService";
import { getCourses, getCoursesForDashboard } from "../../services/courseService";
import { API_BASE_URL } from "../../config/config";
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

// Styled components
const FormContainer = styled.div`
  width: 100%;
  padding: 0 ${({ theme }) => theme.spacing.sm};
  padding-right: 20px;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  max-width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
  height: calc(100vh - 80px);
  scrollbar-width: thin;
  scrollbar-color: #009f9f #f1f1f1;
  background-color: #ffffff;

  &::-webkit-scrollbar {
    width: 12px;
    height: 12px;
    position: absolute;
    right: 0;
  }
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 0;
  }
  &::-webkit-scrollbar-thumb {
    background: #009f9f;
    border-radius: 6px;
    border: 2px solid #f1f1f1;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: #018a8a;
  }
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};

  h1 {
    font-size: 2rem;
    color: #009F9F;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: ${({ theme }) => theme.spacing.md};
  }
`;

const FormSection = styled.div`
  background-color: white;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  box-shadow: ${({ theme }) => theme.shadows.md};
  padding: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.lg};

  h2 {
    font-size: 1.5rem;
    color: #009F9F;
    margin-bottom: ${({ theme }) => theme.spacing.md};
    padding-bottom: ${({ theme }) => theme.spacing.sm};
    border-bottom: 1px solid #e0e0e0;
  }
`;

const FormGroup = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.md};

  label {
    display: block;
    margin-bottom: ${({ theme }) => theme.spacing.xs};
    font-weight: 500;
    color: #444;
  }

  span.required {
    color: #FF3B30;
    margin-left: 4px;
  }

  p.hint {
    font-size: 0.85rem;
    color: #666;
    margin-top: 4px;
  }
`;

const Input = styled.input`
  width: 100%;
  padding: ${({ theme }) => theme.spacing.sm};
  border: 2px solid #e0e0e0;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: 1rem;
  transition: border-color 0.2s;
  
  &:focus {
    outline: none;
    border-color: #009F9F;
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: ${({ theme }) => theme.spacing.sm};
  border: 2px solid #e0e0e0;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: 1rem;
  transition: border-color 0.2s;
  min-height: 150px;
  resize: vertical;
  
  &:focus {
    outline: none;
    border-color: #009F9F;
  }
`;

const Select = styled.select`
  width: 100%;
  padding: ${({ theme }) => theme.spacing.sm};
  border: 2px solid #e0e0e0;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: 1rem;
  transition: border-color 0.2s;
  background-color: white;
  
  &:focus {
    outline: none;
    border-color: #009F9F;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.lg};

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing.xs};
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.lg};
  color: ${({ variant }) => (variant === "primary" ? "white" : "#333")};
  background-color: ${({ variant }) =>
    variant === "primary" ? "#009F9F" : "transparent"};
  border: ${({ variant }) =>
    variant === "primary" ? "none" : "2px solid #e0e0e0"};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 120px;
  
  &:hover {
    background-color: ${({ variant }) =>
      variant === "primary" ? "#018a8a" : "#f1f1f1"};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  @media (max-width: 768px) {
    width: 100%;
  }
`;

const RadioGroup = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.xs};

  @media (max-width: 768px) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing.xs};
  }
`;

const RadioOption = styled.label`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
  border: 2px solid ${({ checked }) => (checked ? "#009F9F" : "#e0e0e0")};
  background-color: ${({ checked }) => (checked ? "rgba(0, 159, 159, 0.1)" : "white")};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${({ checked }) => (checked ? "rgba(0, 159, 159, 0.15)" : "#f9f9f9")};
  }
`;

const ImageUploadContainer = styled.div`
  margin-top: ${({ theme }) => theme.spacing.md};
`;

const UploadButton = styled.label`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing.xs};
  padding: ${({ theme }) => theme.spacing.md};
  border: 2px dashed #e0e0e0;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background-color: #f9f9f9;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
  
  &:hover {
    background-color: #f1f1f1;
    border-color: #ccc;
  }
`;

const HiddenInput = styled.input`
  display: none;
`;

const ImagesPreviewContainer = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  flex-wrap: wrap;
  margin-top: ${({ theme }) => theme.spacing.md};
`;

const ImagePreviewItem = styled.div`
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  overflow: hidden;
  box-shadow: ${({ theme }) => theme.shadows.sm};
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const RemoveImageButton = styled.button`
  position: absolute;
  top: 5px;
  right: 5px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(255, 59, 48, 0.8);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: rgba(255, 59, 48, 1);
  }
`;

const ErrorMessage = styled.p`
  color: #FF3B30;
  font-size: 0.9rem;
  margin-top: 4px;
`;

const RelatedItemsSelect = styled.div`
  margin-top: ${({ theme }) => theme.spacing.md};
  display: ${({ visible }) => (visible ? "block" : "none")};
`;

const InsightForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { addToast } = useToast();
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [books, setBooks] = useState([]);
  const [courses, setCourses] = useState([]);
  const [booksLoading, setBooksLoading] = useState(false);
  const [coursesLoading, setCoursesLoading] = useState(false);
  const [loadingError, setLoadingError] = useState(null);
  
  const [formData, setFormData] = useState({
    title: "",
    details: "",
    relatedTo: {
      type: "Book", // Changed from "book" to "Book"
      id: ""
    },
  });
  
  const [selectedImages, setSelectedImages] = useState([]);
  const [errors, setErrors] = useState({});
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    // Fetch books and courses for the dropdown
    const fetchRelatedItems = async () => {
      setLoadingError(null);
      
      // Fetch books
      setBooksLoading(true);
      try {
        const booksResponse = await getAllBooksForDashboard();
        // Check if response is an array or an object with data property
        if (booksResponse && Array.isArray(booksResponse)) {
          setBooks(booksResponse);
        } else if (booksResponse && booksResponse.data && Array.isArray(booksResponse.data)) {
          setBooks(booksResponse.data);
        } else {
          console.warn("Books response is not in expected format", booksResponse);
          setBooks([]);
        }
      } catch (error) {
        console.error("Error fetching books:", error);
        setLoadingError("Failed to load books. Please try refreshing the page.");
        setBooks([]);
      } finally {
        setBooksLoading(false);
      }
      
      // Fetch courses
      setCoursesLoading(true);
      try {
        const coursesResponse = await getCoursesForDashboard();
        // Check if response is an array or an object with data property
        if (coursesResponse && Array.isArray(coursesResponse)) {
          setCourses(coursesResponse);
        } else if (coursesResponse && coursesResponse.data && Array.isArray(coursesResponse.data)) {
          setCourses(coursesResponse.data);
        } else {
          console.warn("Courses response is not in expected format", coursesResponse);
          setCourses([]);
        }
      } catch (error) {
        console.error("Error fetching courses:", error);
        setLoadingError((prev) => prev || "Failed to load courses. Please try refreshing the page.");
        setCourses([]);
      } finally {
        setCoursesLoading(false);
      }
    };

    fetchRelatedItems();

    // If we have an ID, we're editing an existing insight
    if (id && id !== 'new') {
      setIsEditing(true);
      fetchInsight(id);
    }
  }, [id]);

  const fetchInsight = async (insightId) => {
    try {
      setLoading(true);
      const response = await apiRequest(`/insights/${insightId}`);
      // Normalize type to uppercase for backend compatibility
      let relatedType = response.relatedTo?.type;
      if (relatedType && typeof relatedType === 'string') {
        if (relatedType.toLowerCase() === 'book') relatedType = 'Book';
        else if (relatedType.toLowerCase() === 'course') relatedType = 'Course';
      }
      setFormData({
        title: response.title || "",
        details: response.details || "",
        relatedTo: response.relatedTo ? { ...response.relatedTo, type: relatedType } : { type: "Book", id: "" }
      });
      
      // Set up images for preview
      if (response.images && response.images.length > 0) {
        setSelectedImages(response.images.map(img => ({
          url: img.url,
          publicId: img.publicId,
          isExisting: true
        })));
      }
      
    } catch (error) {
      console.error("Error fetching insight:", error);
      addToast("Failed to load insight data", "error");
      navigate("/insights");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    // Normalize type to uppercase for backend compatibility
    let normalizedValue = value;
    if (name === 'relatedTo.type') {
      if (value.toLowerCase() === 'book') normalizedValue = 'Book';
      else if (value.toLowerCase() === 'course') normalizedValue = 'Course';
    }
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData({
        ...formData,
        [parent]: {
          ...formData[parent],
          [child]: normalizedValue
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: normalizedValue
      });
    }
    // Clear error when field is changed
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };

  const handleImageSelect = (e) => {
    const files = Array.from(e.target.files);
    
    if (files.length > 0) {
      // Preview the images
      const newImages = files.map(file => ({
        file,
        url: URL.createObjectURL(file),
        isNew: true
      }));
      
      setSelectedImages([...selectedImages, ...newImages]);
    }
  };

  const handleRemoveImage = (index) => {
    const newImages = [...selectedImages];
    
    // If it's a preview from file, revoke the object URL to prevent memory leaks
    if (newImages[index].url && newImages[index].isNew) {
      URL.revokeObjectURL(newImages[index].url);
    }
    
    newImages.splice(index, 1);
    setSelectedImages(newImages);
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.title.trim()) {
      newErrors.title = "Title is required";
    }
    
    if (!formData.details.trim()) {
      newErrors.details = "Details are required";
    }
    
    if (!formData.relatedTo.id) {
      newErrors['relatedTo.id'] = "Please select a related item";
    }
    
    if (selectedImages.length === 0) {
      newErrors.images = "At least one image is required";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      addToast("Please fill in all required fields", "error");
      return;
    }
    
    try {
      setSubmitting(true);
      
      const newImages = selectedImages.filter(img => img.isNew);
      
      if (isEditing) {
        let updatedImages = selectedImages.filter(img => !img.isNew);
        
        if (newImages.length > 0) {
          const formDataObj = new FormData();
          newImages.forEach(img => {
            formDataObj.append('images', img.file);
          });
          
          formDataObj.append('title', formData.title);
          formDataObj.append('details', formData.details);
          formDataObj.append('type', formData.relatedTo.type);
          formDataObj.append('entityId', formData.relatedTo.id);
          
          let token = '';
          try {
            let userDataString = sessionStorage.getItem("patentpionner_user");
            if (!userDataString) {
              userDataString = localStorage.getItem("patentpionner_user");
            }
            let userData = null;
            if (userDataString) {
              userData = JSON.parse(userDataString);
            }
            if (userData) {
              token = userData.accessToken || 
                     userData.token || 
                     (userData.user && userData.user.accessToken) ||
                     (userData.user && userData.user.token);
              
              if (!token) {
                console.warn("No token found in user data:", userData);
              }
            } else {
              console.warn("No user data found in storage");
            }
          } catch (error) {
            console.error("Error getting token:", error);
          }
          
          const uploadResponse = await fetch(`${API_BASE_URL}/insights/upload`, {
            method: 'POST',
            body: formDataObj,
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          
          const uploadResult = await uploadResponse.json();
          
          if (uploadResult.success && uploadResult.insight.images) {
            updatedImages = [...updatedImages, ...uploadResult.insight.images];
          }
        }
        
        // Update the insight with all images
        const updateData = {
          ...formData,
          images: updatedImages.map(img => ({
            url: img.url,
            publicId: img.publicId
          }))
        };
        
        await apiRequest(`/admin/insights/${id}`, 'PATCH', updateData);
        addToast("Insight updated successfully", "success");
        
      } else {
        // Create new insight with images
        if (newImages.length > 0) {
          const formDataObj = new FormData();
          newImages.forEach(img => {
            formDataObj.append('images', img.file);
          });
          
          formDataObj.append('title', formData.title);
          formDataObj.append('details', formData.details);
          formDataObj.append('type', formData.relatedTo.type);
          formDataObj.append('entityId', formData.relatedTo.id);
          
          // Use the upload endpoint
          // Get token from the same storage locations as the API service
          let token = '';
          try {
            let userDataString = sessionStorage.getItem("patentpionner_user");
            if (!userDataString) {
              userDataString = localStorage.getItem("patentpionner_user");
            }
            let userData = null;
            if (userDataString) {
              userData = JSON.parse(userDataString);
            }
            if (userData) {
              token = userData.accessToken || 
                     userData.token || 
                     (userData.user && userData.user.accessToken) ||
                     (userData.user && userData.user.token);
              
              if (!token) {
                console.warn("No token found in user data:", userData);
              }
            } else {
              console.warn("No user data found in storage");
            }
          } catch (error) {
            console.error("Error getting token:", error);
          }
          
          const response = await fetch(`${API_BASE_URL}/insights/upload`, {
            method: 'POST',
            body: formDataObj,
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          
          const result = await response.json();
          console.log('Server response:', result);
          
          if (result.success) {
            addToast("Insight created successfully", "success");
          } else {
            const errorMessage = result.error || result.message || "Failed to create insight";
            console.error("API Error:", result);
            
            if (errorMessage.includes('publicId') || errorMessage.includes('public_id')) {
              addToast("Error with image upload. Please try again or contact support.", "error");
            } else {
              throw new Error(errorMessage);
            }
          }
        } else {
          throw new Error("No images selected");
        }
      }
      
      navigate("/insights");
      
    } catch (error) {
      console.error("Error submitting insight:", error);
      addToast(error.message || "Failed to save insight", "error");
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <FormContainer>
        <div style={{ textAlign: 'center', padding: '2rem' }}>Loading...</div>
      </FormContainer>
    );
  }

  return (
    <FormContainer>
      <PageHeader>
        <h1>{isEditing ? "Edit Insight" : "Create New Insight"}</h1>
        <Button variant="secondary" onClick={() => navigate("/admin/insights")}>
          <ArrowLeft size={18} />
          Back to Insights
        </Button>
      </PageHeader>

      <form onSubmit={handleSubmit}>
        <FormSection>
          <h2>Basic Information</h2>
          
          <FormGroup>
            <label htmlFor="title">
              Title <span className="required">*</span>
            </label>
            <Input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              placeholder="Enter insight title"
            />
            {errors.title && <ErrorMessage>{errors.title}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <label htmlFor="details">
              Details <span className="required">*</span>
            </label>
            <ReactQuill
              id="details"
              name="details"
              value={formData.details}
              onChange={value => handleInputChange({ target: { name: 'details', value } })}
              placeholder="Enter insight details (format text, add headings, color, etc.)"
              theme="snow"
              style={{ minHeight: 150, marginBottom: 8 }}
              modules={{
                toolbar: [
                  [{ 'header': [1, 2, 3, false] }],
                  ['bold', 'italic', 'underline', 'strike'],
                  [{ 'color': [] }, { 'background': [] }],
                  [{ 'font': [] }],
                  [{ 'align': [] }],
                  ['blockquote', 'code-block'],
                  [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                  ['link'],
                  ['clean']
                ]
              }}
            />
            {errors.details && <ErrorMessage>{errors.details}</ErrorMessage>}
          </FormGroup>
        </FormSection>

        <FormSection>
          <h2>Related Content</h2>
          
          <FormGroup>
            <label>
              This insight is related to <span className="required">*</span>
            </label>
            <RadioGroup>
              <RadioOption 
                checked={formData.relatedTo.type === "Book"}
              >
                <input
                  type="radio"
                  name="relatedTo.type"
                  value="Book"
                  checked={formData.relatedTo.type === "Book"}
                  onChange={handleInputChange}
                />
                <BookOpen size={18} />
                Book
              </RadioOption>
              
              <RadioOption 
                checked={formData.relatedTo.type === "Course"}
              >
                <input
                  type="radio"
                  name="relatedTo.type"
                  value="Course" 
                  checked={formData.relatedTo.type === "Course"}
                  onChange={handleInputChange}
                />
                <Video size={18} />
                Course
              </RadioOption>
            </RadioGroup>

            <RelatedItemsSelect visible={formData.relatedTo.type === "Book"}>
              <label htmlFor="bookId">
                Select Book <span className="required">*</span>
              </label>
              <Select
                id="bookId"
                name="relatedTo.id"
                value={formData.relatedTo.type === "Book" ? formData.relatedTo.id : ""}
                onChange={handleInputChange}
                disabled={formData.relatedTo.type !== "Book" || booksLoading}
              >
                <option value="">{booksLoading ? "Loading books..." : "Select a book"}</option>
                {!booksLoading && Array.isArray(books) && books.map(book => (
                  <option key={book._id} value={book._id}>
                    {book.title}
                  </option>
                ))}
              </Select>
              {booksLoading && <p className="hint">Loading books...</p>}
            </RelatedItemsSelect>

            <RelatedItemsSelect visible={formData.relatedTo.type === "Course"}>
              <label htmlFor="courseId">
                Select Course <span className="required">*</span>
              </label>
              <Select
                id="courseId"
                name="relatedTo.id"
                value={formData.relatedTo.type === "Course" ? formData.relatedTo.id : ""}
                onChange={handleInputChange}
                disabled={formData.relatedTo.type !== "Course" || coursesLoading}
              >
                <option value="">{coursesLoading ? "Loading courses..." : "Select a course"}</option>
                {!coursesLoading && Array.isArray(courses) && courses.map(course => (
                  <option key={course._id} value={course._id}>
                    {course.title}
                  </option>
                ))}
              </Select>
              {coursesLoading && <p className="hint">Loading courses...</p>}
            </RelatedItemsSelect>
            
            {errors['relatedTo.id'] && (
              <ErrorMessage>{errors['relatedTo.id']}</ErrorMessage>
            )}
            {loadingError && (
              <ErrorMessage>{loadingError}</ErrorMessage>
            )}
          </FormGroup>
        </FormSection>

        <FormSection>
          <h2>Images</h2>
          <p className="hint">Upload images that represent the insight. At least one image is required.</p>
          
          <ImageUploadContainer>
            <UploadButton>
              <Upload size={20} />
              Select Images to Upload
              <HiddenInput
                type="file"
                accept="image/*"
                multiple
                onChange={handleImageSelect}
              />
            </UploadButton>
            
            {errors.images && <ErrorMessage>{errors.images}</ErrorMessage>}

            {selectedImages.length > 0 && (
              <ImagesPreviewContainer>
                {selectedImages.map((image, index) => (
                  <ImagePreviewItem key={index}>
                    <img src={image.url} alt={`Preview ${index + 1}`} />
                    <RemoveImageButton 
                      onClick={() => handleRemoveImage(index)}
                      type="button"
                    >
                      <X size={14} />
                    </RemoveImageButton>
                  </ImagePreviewItem>
                ))}
              </ImagesPreviewContainer>
            )}
          </ImageUploadContainer>
        </FormSection>

        <ButtonGroup>
          <Button 
            type="button" 
            variant="secondary"
            onClick={() => navigate("/insights")}
          >
            Cancel
          </Button>
          <Button 
            type="submit" 
            variant="primary"
            disabled={submitting}
          >
            {submitting ? "Saving..." : (
              <>
                <Save size={18} />
                {isEditing ? "Update Insight" : "Create Insight"}
              </>
            )}
          </Button>
        </ButtonGroup>
      </form>
    </FormContainer>
  );
};

export default InsightForm;
