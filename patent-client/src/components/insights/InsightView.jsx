import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { useNavigate, useParams } from "react-router-dom";
import { ArrowLeft, Edit, Trash, BookOpen, Video, Calendar } from "lucide-react";
import apiRequest from "../../services/api";
import { useToast } from "../../contexts/ToastContext";

// Styled components
const InsightContainer = styled.div`
  width: 100%;
  padding: 0 ${({ theme }) => theme.spacing.sm};
  padding-right: 20px;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  max-width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
  height: calc(100vh - 80px);
  scrollbar-width: thin;
  scrollbar-color: #009f9f #f1f1f1;
  background-color: #ffffff;

  &::-webkit-scrollbar {
    width: 12px;
    height: 12px;
    position: absolute;
    right: 0;
  }
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 0;
  }
  &::-webkit-scrollbar-thumb {
    background: #009f9f;
    border-radius: 6px;
    border: 2px solid #f1f1f1;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: #018a8a;
  }
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};

  h1 {
    font-size: 2rem;
    color: #009F9F;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: ${({ theme }) => theme.spacing.md};
  }
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
  background-color: ${({ variant }) => 
    variant === "primary" ? "#009F9F" : 
    variant === "danger" ? "#FF3B30" :
    "transparent"};
    
  color: ${({ variant }) => 
    variant === "primary" || variant === "danger" ? "white" : "#333"};
  border: ${({ variant }) => 
    variant === "primary" || variant === "danger" ? "none" : "2px solid #e0e0e0"};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: ${({ variant }) => 
      variant === "primary" ? "#018a8a" : 
      variant === "danger" ? "#E0312C" :
      "#f1f1f1"};
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  
  @media (max-width: 768px) {
    width: 100%;
    justify-content: flex-end;
  }
`;

const ContentSection = styled.div`
  background-color: white;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  box-shadow: ${({ theme }) => theme.shadows.md};
  overflow: hidden;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const InsightHeader = styled.div`
  position: relative;
  height: 300px;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  @media (max-width: 768px) {
    height: 200px;
  }
`;

const RelatedBadge = styled.span`
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: ${({ type }) => type === 'book' ? 'rgba(0, 159, 159, 0.85)' : 'rgba(255, 153, 0, 0.85)'};
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 2;
`;

const InsightContent = styled.div`
  padding: ${({ theme }) => theme.spacing.xl};
  
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing.lg};
  }
`;

const InsightTitle = styled.h1`
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  
  @media (max-width: 768px) {
    font-size: 1.8rem;
  }
`;

const InsightMeta = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  color: #666;
  font-size: 0.95rem;
  
  span {
    display: flex;
    align-items: center;
    gap: 6px;
  }
`;

const InsightDetails = styled.div`
  font-size: 1.1rem;
  line-height: 1.7;
  color: #444;
  white-space: normal;
  margin-bottom: ${({ theme }) => theme.spacing.xl};
  
  @media (max-width: 768px) {
    font-size: 1rem;
  }
`;

const RelatedContent = styled.div`
  margin-top: ${({ theme }) => theme.spacing.xl};
  padding-top: ${({ theme }) => theme.spacing.lg};
  border-top: 1px solid #e0e0e0;
`;

const RelatedTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 600;
  color: #009F9F;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const RelatedItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.md};
  border: 1px solid #e0e0e0;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  transition: all 0.2s;
  cursor: pointer;
  
  &:hover {
    background-color: #f9f9f9;
    transform: translateY(-2px);
    box-shadow: ${({ theme }) => theme.shadows.sm};
  }
  
  img {
    width: 80px;
    height: 80px;
    border-radius: ${({ theme }) => theme.borderRadius.sm};
    object-fit: cover;
  }
  
  div {
    flex: 1;
  }
  
  h4 {
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 4px;
  }
  
  p {
    font-size: 0.9rem;
    color: #666;
  }
`;

const ImageGallery = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.lg};
  
  img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: ${({ theme }) => theme.borderRadius.md};
    transition: transform 0.2s;
    cursor: pointer;
    
    &:hover {
      transform: scale(1.05);
    }
  }
  
  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    
    img {
      height: 120px;
    }
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  font-size: 1.2rem;
  color: #666;
`;

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  cursor: pointer;
`;

const ModalContent = styled.div`
  position: relative;
  max-width: 90%;
  max-height: 90%;
  
  img {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
  }
`;

const CloseButton = styled.button`
  position: absolute;
  top: -40px;
  right: 0;
  background-color: transparent;
  color: white;
  border: none;
  font-size: 2rem;
  cursor: pointer;
`;

const InsightView = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { showToast } = useToast();
  const [insight, setInsight] = useState(null);
  const [relatedItem, setRelatedItem] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState(null);

  useEffect(() => {
    fetchInsight();
  }, [id]);

  const fetchInsight = async () => {
    try {
      setLoading(true);
      const response = await apiRequest(`/insights/${id}`);
      setInsight(response);
      
      if (response.relatedTo) {
        fetchRelatedItem(response.relatedTo.type, response.relatedTo.id);
      }
    } catch (error) {
      console.error("Error fetching insight:", error);
      showToast("Failed to load insight data", "error");
      navigate("/insights");
    } finally {
      setLoading(false);
    }
  };

  const fetchRelatedItem = async (type, itemId) => {
    try {
      const response = await apiRequest(`/${type}s/${itemId}`);
      setRelatedItem(response);
    } catch (error) {
      console.error(`Error fetching related ${type}:`, error);
    }
  };

  const handleDelete = async () => {
    if (window.confirm("Are you sure you want to delete this insight? This action cannot be undone.")) {
      try {
        await apiRequest(`/insights/${id}`, 'DELETE');
        showToast("Insight deleted successfully", "success");
        navigate("/insights");
      } catch (error) {
        console.error("Error deleting insight:", error);
        showToast("Failed to delete insight", "error");
      }
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const navigateToRelatedItem = () => {
    if (relatedItem && insight?.relatedTo) {
      const type = insight.relatedTo.type;
      navigate(`/${type}s/${relatedItem._id}`);
    }
  };

  if (loading) {
    return (
      <InsightContainer>
        <LoadingContainer>Loading insight...</LoadingContainer>
      </InsightContainer>
    );
  }

  if (!insight) {
    return (
      <InsightContainer>
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          Insight not found.
        </div>
      </InsightContainer>
    );
  }

  return (
    <InsightContainer>
      <PageHeader>
        <Button variant="secondary" onClick={() => navigate("/admin/insights")}>
          <ArrowLeft size={18} />
          Back to Insights
        </Button>
        
        <ButtonGroup>
          <Button 
            variant="primary"
            onClick={() => navigate(`/insights/${id}/edit`)}
          >
            <Edit size={18} />
            Edit
          </Button>
          {/* <Button 
            variant="danger"
            onClick={handleDelete}
          >
            <Trash size={18} />
            Delete
          </Button> */}
        </ButtonGroup>
      </PageHeader>

      <ContentSection>
        <InsightHeader>
          <img 
            src={insight.images && insight.images.length > 0 ? 
              insight.images[0].url : 
              'https://via.placeholder.com/1200x400?text=No+Image'
            } 
            alt={insight.title} 
          />
          <RelatedBadge type={insight.relatedTo?.type}>
            {insight.relatedTo?.type === 'book' ? (
              <>
                <BookOpen size={18} />
                Related to Book
              </>
            ) : (
              <>
                <Video size={18} />
                Related to Course
              </>
            )}
          </RelatedBadge>
        </InsightHeader>

        <InsightContent>
          <InsightTitle>{insight.title}</InsightTitle>
          
          <InsightMeta>
            <span>
              <Calendar size={18} />
              {formatDate(insight.createdAt)}
            </span>
          </InsightMeta>
          
          <InsightDetails dangerouslySetInnerHTML={{ __html: insight.details }} />
          
          {insight.images && insight.images.length > 1 && (
            <>
              <h3>More Images</h3>
              <ImageGallery>
                {insight.images.slice(1).map((image, index) => (
                  <img 
                    key={index} 
                    src={image.url} 
                    alt={`${insight.title} - image ${index + 2}`}
                    onClick={() => setSelectedImage(image.url)}
                  />
                ))}
              </ImageGallery>
            </>
          )}
          
          {relatedItem && (
            <RelatedContent>
              <RelatedTitle>
                {insight.relatedTo?.type === 'book' ? (
                  <>
                    <BookOpen size={22} />
                    Related Book
                  </>
                ) : (
                  <>
                    <Video size={22} />
                    Related Course
                  </>
                )}
              </RelatedTitle>
              
              <RelatedItem onClick={navigateToRelatedItem}>
                <img 
                  src={relatedItem.image || 'https://via.placeholder.com/80?text=No+Image'} 
                  alt={relatedItem.title} 
                />
                <div>
                  <h4>{relatedItem.title}</h4>
                  <p>
                    {relatedItem.description?.substring(0, 100)}
                    {relatedItem.description?.length > 100 ? '...' : ''}
                  </p>
                </div>
              </RelatedItem>
            </RelatedContent>
          )}
        </InsightContent>
      </ContentSection>

      {selectedImage && (
        <ModalOverlay onClick={() => setSelectedImage(null)}>
          <ModalContent onClick={e => e.stopPropagation()}>
            <CloseButton onClick={() => setSelectedImage(null)}>&times;</CloseButton>
            <img src={selectedImage} alt="Enlarged view" />
          </ModalContent>
        </ModalOverlay>
      )}
    </InsightContainer>
  );
};

export default InsightView;
