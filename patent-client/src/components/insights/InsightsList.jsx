import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { Plus, BookOpen, Video, Eye, Search } from "lucide-react";
import { Delete as DeleteIcon, Edit as EditIcon } from "@styled-icons/material";
import apiRequest from "../../services/api";
import { useNavigate } from "react-router-dom";
import { useToast } from "../../contexts/ToastContext";
import { useAuth } from "../../contexts/AuthContext";
import ConfirmationModal from '../common/ConfirmationModal';

// Styled components
const InsightsContainer = styled.div`
  width: 100%;
  padding: 0 ${({ theme }) => theme.spacing.sm};
  padding-right: 20px;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  max-width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
  height: calc(100vh - 80px);
  scrollbar-width: thin;
  scrollbar-color: #009f9f #f1f1f1;
  background-color: #ffffff;

  &::-webkit-scrollbar {
    width: 12px;
    height: 12px;
    position: absolute;
    right: 0;
  }
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 0;
  }
  &::-webkit-scrollbar-thumb {
    background: #009f9f;
    border-radius: 6px;
    border: 2px solid #f1f1f1;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: #018a8a;
  }
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};

  h1 {
    font-size: 2rem;
    color: #009F9F;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: ${({ theme }) => theme.spacing.md};
  }
`;

const SearchContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  width: 300px;

  @media (max-width: 768px) {
    width: 100%;
  }
`;

const SearchInput = styled.input`
  width: 100%;
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.sm} 40px;
  border: 2px solid #e0e0e0;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: 0.95rem;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #009F9F;
  }
`;

const SearchIconContainer = styled.div`
  position: absolute;
  left: 10px;
  color: #9e9e9e;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
  background-color: #009F9F;
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #018a8a;
  }
`;

const InsightsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: ${({ theme }) => theme.spacing.lg};
  margin-top: ${({ theme }) => theme.spacing.xl};

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: ${({ theme }) => theme.spacing.md};
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
`;

const InsightCard = styled.div`
  position: relative;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  overflow: hidden;
  box-shadow: ${({ theme }) => theme.shadows.md};
  background-color: white;
  transition: transform 0.2s, box-shadow 0.2s;

  &:hover {
    transform: translateY(-5px);
    box-shadow: ${({ theme }) => theme.shadows.lg};
  }
`;

const InsightImageContainer = styled.div`
  position: relative;
  height: 160px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
  }

  ${InsightCard}:hover & img {
    transform: scale(1.05);
  }
`;

const RelatedBadge = styled.span`
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: ${({ type }) => type?.toLowerCase() === 'book' ? 'rgba(0, 159, 159, 0.85)' : 'rgba(255, 153, 0, 0.85)'};
  color: white;
  padding: 3px 10px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 5px;
  z-index: 2;
`;

const InsightContent = styled.div`
  padding: ${({ theme }) => theme.spacing.md};
`;

const InsightTitle = styled.h3`
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: ${({ theme }) => theme.spacing.sm};
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 2.75rem;
`;

const InsightDetails = styled.div`
  font-size: 0.9rem;
  color: #666;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 3.6rem;
  /* Allow HTML rendering */
  white-space: normal;
`;

const InsightMeta = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: ${({ theme }) => theme.spacing.sm};
`;

const InsightRelated = styled.div`
  font-size: 0.85rem;
  color: #666;
`;

const InsightDate = styled.div`
  font-size: 0.8rem;
  color: #999;
`;

const InsightActions = styled.div`
  position: absolute;
  bottom: 10px;
  right: 10px;
  display: flex;
  gap: 8px;
`;

const ActionButton = styled.button`
  background: none;
  color: ${({ color }) =>
    color === 'edit' ? '#009F9F' :
    color === 'delete' ? '#FF3B30' :
    '#007AFF'
  };
  border: none;
  border-radius: 0;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: none;
  padding: 0;
  margin: 0;
  &:hover {
    background: none;
    opacity: 0.7;
    transform: translateY(-2px);
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  width: 100%;
  font-size: 1.2rem;
  color: #666;
`;

const NoResultsContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  text-align: center;
  color: #666;
  width: 100%;

  h3 {
    font-size: 1.5rem;
    color: #009F9F;
    margin-bottom: ${({ theme }) => theme.spacing.md};
  }

  p {
    font-size: 1rem;
    margin-bottom: ${({ theme }) => theme.spacing.lg};
  }
`;

const FilterContainer = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};

  @media (max-width: 768px) {
    flex-wrap: wrap;
  }
`;

const FilterButton = styled.button`
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.md};
  background-color: ${({ active }) => active ? '#009F9F' : '#f1f1f1'};
  color: ${({ active }) => active ? 'white' : '#333'};
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-weight: ${({ active }) => active ? '600' : 'normal'};
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${({ active }) => active ? '#018a8a' : '#e0e0e0'};
  }
`;

const InsightsList = () => {
  const [insights, setInsights] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [insightToDelete, setInsightToDelete] = useState(null);
  const [deleting, setDeleting] = useState(false);
  const navigate = useNavigate();
  const { showToast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    fetchInsights();
  }, []);

  const fetchInsights = async () => {
    try {
      setLoading(true);
      const response = await apiRequest('/insights');
      setInsights(response);
    } catch (error) {
      console.error('Error fetching insights:', error);
      showToast('Failed to load insights', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = (id) => {
    setInsightToDelete(id);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!insightToDelete) return;
    setDeleting(true);
    try {
      await apiRequest(`/insights/${insightToDelete}`, 'DELETE');
      showToast('Insight deleted successfully', 'success');
      // Remove the deleted insight from local state for instant UI update
      setInsights(prev => prev.filter(insight => insight._id !== insightToDelete));
    } catch (error) {
      console.error('Error deleting insight:', error);
      showToast('Failed to delete insight', 'error');
    } finally {
      setDeleting(false);
      setShowDeleteModal(false);
      setInsightToDelete(null);
    }
  };

  const handleEdit = (id) => {
    navigate(`/insights/${id}/edit`);
  };

  const handleAddNew = () => {
    navigate('/insights/new');
  };

  const handleFilterChange = (filter) => {
    setActiveFilter(filter);
  };

  // Filter insights based on search term and active filter
  const filteredInsights = insights
    .filter(insight => {
      const matchesSearch = insight.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           insight.details.toLowerCase().includes(searchTerm.toLowerCase());

      if (activeFilter === 'all') return matchesSearch;
      return matchesSearch && insight.relatedTo.type?.toLowerCase() === activeFilter.toLowerCase();
    });

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <InsightsContainer>
      <PageHeader>
        <h1>Insights Management</h1>
        <Button onClick={handleAddNew}>
          <Plus size={18} />
          Add New Insight
        </Button>
      </PageHeader>

      <FilterContainer>
        <FilterButton
          active={activeFilter === 'all'}
          onClick={() => handleFilterChange('all')}
        >
          All Insights
        </FilterButton>
        {/* <FilterButton
          active={activeFilter === 'book'}
          onClick={() => handleFilterChange('book')}
        >
          <BookOpen size={16} style={{ marginRight: '5px' }} />
          Book Insights
        </FilterButton>
        <FilterButton
          active={activeFilter === 'course'}
          onClick={() => handleFilterChange('course')}
        >
          <Video size={16} style={{ marginRight: '5px' }} />
          Course Insights
        </FilterButton> */}
      </FilterContainer>

      <SearchContainer>
        <SearchIconContainer>
          <Search size={20} />
        </SearchIconContainer>
        <SearchInput
          type="text"
          placeholder="Search insights..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </SearchContainer>

      {loading ? (
        <LoadingContainer>Loading insights...</LoadingContainer>
      ) : filteredInsights.length === 0 ? (
        <NoResultsContainer>
          <h3>No insights found</h3>
          <p>There are no insights matching your search criteria.</p>
          <Button onClick={handleAddNew}>
            <Plus size={18} />
            Add New Insight
          </Button>
        </NoResultsContainer>
      ) : (
        <InsightsGrid>
          {filteredInsights.map((insight) => (
            <InsightCard key={insight._id}>
              <InsightImageContainer>
                <img
                  src={insight.images && insight.images.length > 0 ? insight.images[0].url : 'https://via.placeholder.com/300x160?text=No+Image'}
                  alt={insight.title}
                />
                <RelatedBadge type={insight.relatedTo.type}>
                  {insight.relatedTo.type?.toLowerCase() === 'book' ? (
                    <>
                      <BookOpen size={14} />
                      Book
                    </>
                  ) : (
                    <>
                      <Video size={14} />
                      Course
                    </>
                  )}
                </RelatedBadge>
              </InsightImageContainer>

              <InsightContent>
                <InsightTitle>{insight.title}</InsightTitle>
                <InsightDetails dangerouslySetInnerHTML={{ __html: insight.details }} />

                <InsightMeta>
                  <InsightDate>{formatDate(insight.createdAt)}</InsightDate>
                </InsightMeta>
              </InsightContent>

              <InsightActions>
                <ActionButton
                  color="view"
                  title="View Insight"
                  onClick={() => navigate(`/admin/insights/${insight._id}`)}
                >
                  <Eye size={18} />
                </ActionButton>
                <ActionButton
                  color="edit"
                  title="Edit Insight"
                  onClick={() => handleEdit(insight._id)}
                >
                  <EditIcon size={18} style={{ color: '#018A8A' }} />
                </ActionButton>
                <ActionButton
                  color="delete"
                  title="Delete Insight"
                  onClick={() => handleDelete(insight._id)}
                  style={{ background: 'none', boxShadow: 'none' }} // Remove circle bg
                >
                  <DeleteIcon size={18} style={{ color: '#FF3B30' }} />
                </ActionButton>
              </InsightActions>
            </InsightCard>
          ))}
        </InsightsGrid>
      )}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => { setShowDeleteModal(false); setInsightToDelete(null); }}
        onConfirm={confirmDelete}
        title="Delete Insight"
        message="Are you sure you want to delete this insight? This action cannot be undone."
        confirmText={deleting ? 'Deleting...' : 'Delete'}
        cancelText="Cancel"
        variant="danger"
        isLoading={deleting}
      />
    </InsightsContainer>
  );
};

export default InsightsList;
