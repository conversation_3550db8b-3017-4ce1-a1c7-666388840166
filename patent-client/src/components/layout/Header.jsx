import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";
import { useAuth } from "../../contexts/AuthContext";
import {
  Person as ProfileIcon,
  ExitToApp as <PERSON>goutIcon,
  KeyboardArrowDown as ArrowDownIcon,
  KeyboardArrowUp as ArrowUpIcon,
} from "@styled-icons/material";
import { HomeIcon } from "lucide-react";
const HeaderContainer = styled.header`
  background-color: white;
  height: 70px;
  box-shadow: ${({ theme }) => theme.shadows.sm};
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 ${({ theme }) => theme.spacing.xl};
  margin-left: 0;
  box-sizing: border-box;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 100;
  width: calc(100% - 280px);
  transition:
    width 0.3s ease,
    padding 0.3s ease;
  @media (max-width: 1200px) {
    padding: 0 ${({ theme }) => theme.spacing.lg};
  }
  @media (max-width: 992px) {
    width: 100%;
  }
  @media (max-width: 768px) {
    width: 100%;
    padding: 0 ${({ theme }) => theme.spacing.md};
    justify-content: flex-end;
  }
  @media (max-width: 576px) {
    padding: 0 ${({ theme }) => theme.spacing.sm};
    height: 60px;
  }
  @media (max-width: 480px) {
    height: 56px;
  }
`;
const UserInfo = styled.div`
  display: flex;
  align-items: center;
  position: relative;
  padding-right: ${({ theme }) => theme.spacing.sm};
  max-width: 250px;
  @media (max-width: 992px) {
    max-width: 220px;
  }
  @media (max-width: 768px) {
    max-width: 200px;
  }
  @media (max-width: 576px) {
    padding-right: ${({ theme }) => theme.spacing.xs};
    max-width: 180px;
  }
  @media (max-width: 480px) {
    justify-content: flex-end;
    max-width: 50px;
  }
`;
const UserAvatar = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: ${({ theme }) => theme.spacing.md};
  font-weight: 500;
  flex-shrink: 0;
  font-size: 0.9rem;
  min-width: 36px;
  @media (max-width: 992px) {
    width: 34px;
    height: 34px;
    min-width: 34px;
  }
  @media (max-width: 768px) {
    width: 32px;
    height: 32px;
    min-width: 32px;
    margin-right: ${({ theme }) => theme.spacing.sm};
  }
  @media (max-width: 576px) {
    width: 30px;
    height: 30px;
    min-width: 30px;
    font-size: 0.85rem;
  }
  @media (max-width: 480px) {
    width: 28px;
    height: 28px;
    min-width: 28px;
    margin-right: 0;
    font-size: 0.8rem;
  }
`;
const UserInfoContainer = styled.div`
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-width: 160px;
  margin-right: ${({ theme }) => theme.spacing.xs};
  @media (max-width: 992px) {
    max-width: 140px;
  }
  @media (max-width: 768px) {
    max-width: 120px;
  }
  @media (max-width: 576px) {
    max-width: 100px;
  }
  @media (max-width: 480px) {
    display: none;
  }
`;
const UserName = styled.div`
  font-weight: 600;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  @media (max-width: 992px) {
    font-size: 0.85rem;
  }
  @media (max-width: 768px) {
    font-size: 0.8rem;
  }
  @media (max-width: 576px) {
    font-size: 0.75rem;
  }
`;
const UserEmail = styled.div`
  font-size: 0.7rem;
  color: ${({ theme }) => theme.colors.text};
  opacity: 0.6;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  @media (max-width: 992px) {
    font-size: 0.65rem;
  }
  @media (max-width: 768px) {
    font-size: 0.6rem;
  }
  @media (max-width: 576px) {
    display: none;
  }
`;
const DropdownToggle = styled.div`
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: ${({ theme }) => theme.spacing.xs};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  transition: all 0.2s ease;
  max-width: 100%;
  &:hover {
    background-color: ${({ theme }) => theme.colors.light};
  }
  svg {
    width: 16px;
    height: 16px;
    margin-left: ${({ theme }) => theme.spacing.xs};
    color: ${({ theme }) => theme.colors.textSecondary};
    flex-shrink: 0;
  }
  @media (max-width: 480px) {
    padding: ${({ theme }) => theme.spacing.xs};
    svg {
      width: 14px;
      height: 14px;
    }
  }
`;
const DropdownMenu = styled.div`
  position: absolute;
  top: calc(100% + 5px);
  right: 0;
  background-color: white;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  box-shadow: ${({ theme }) => theme.shadows.md};
  width: 220px;
  z-index: 200;
  overflow: hidden;
  transition: all 0.3s ease;
  opacity: ${({ isOpen }) => (isOpen ? 1 : 0)};
  transform: translateY(${({ isOpen }) => (isOpen ? "0" : "-10px")});
  pointer-events: ${({ isOpen }) => (isOpen ? "all" : "none")};
  @media (max-width: 576px) {
    width: 200px;
  }
`;
const DropdownItem = styled.div`
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  &:hover {
    background-color: ${({ theme }) => theme.colors.light};
  }
  svg {
    width: 18px;
    height: 18px;
    margin-right: ${({ theme }) => theme.spacing.sm};
    color: ${({ theme }) => theme.colors.textSecondary};
  }
  @media (max-width: 576px) {
    padding: ${({ theme }) => theme.spacing.xs}
      ${({ theme }) => theme.spacing.sm};
    svg {
      width: 16px;
      height: 16px;
      margin-right: ${({ theme }) => theme.spacing.xs};
    }
  }
`;
const Header = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [userData, setUserData] = useState(null);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);
  useEffect(() => {
    const getUserData = () => {
      if (user) {
        setUserData(user);
        return;
      }
      try {
        const storedUser = localStorage.getItem("patentpionner_user");
        if (storedUser) {
          const parsedUser = JSON.parse(storedUser);
          setUserData(parsedUser);
        }
      } catch (error) {
        console.error("Error parsing user data from localStorage:", error);
        setUserData({ username: "User" });
      }
    };
    getUserData();
  }, [user]);
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  const getInitials = (name) => {
    if (!name) return "U";
    return name.charAt(0).toUpperCase();
  };
  const handleLogout = () => {
    logout();
    navigate("/login");
    setDropdownOpen(false);
  };
  const goToProfile = () => {
    navigate("/profile");
    setDropdownOpen(false);
  };
  const goHome = () => {
    navigate("/");
    setDropdownOpen(false);
  };
  return (
    <HeaderContainer>
      <UserInfo ref={dropdownRef}>
        <DropdownToggle onClick={() => setDropdownOpen(!dropdownOpen)}>
          <UserAvatar>
            {getInitials(userData?.name || userData?.username)}
          </UserAvatar>
          <UserInfoContainer>
            <UserName>
              {userData?.name || userData?.username || "User"}
            </UserName>
            <UserEmail>{userData?.email}</UserEmail>
          </UserInfoContainer>
          {dropdownOpen ? <ArrowUpIcon /> : <ArrowDownIcon />}
        </DropdownToggle>
        <DropdownMenu isOpen={dropdownOpen}>
          {}
          <DropdownItem onClick={goHome}>
            <HomeIcon />
            Home
          </DropdownItem>
          <DropdownItem onClick={goToProfile}>
            <ProfileIcon />
            My Profile
          </DropdownItem>
          <DropdownItem onClick={handleLogout}>
            <LogoutIcon />
            Logout
          </DropdownItem>
        </DropdownMenu>
      </UserInfo>
    </HeaderContainer>
  );
};
export default Header;
