import React, { useState, useEffect } from "react";
import styled from "styled-components";
import Sidebar from "./Sidebar";
import Header from "./Header";

const LayoutContainer = styled.div`
  display: flex;
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
  position: relative;
`;

// Updated Main component to work with the responsive sidebar
const Main = styled.main`
  flex: 1;
  margin-left: ${({ sidebarVisible }) => (sidebarVisible ? "280px" : "0")};
  width: ${({ sidebarVisible }) => 
    sidebarVisible ? "calc(100% - 280px)" : "100%"};
  transition: margin-left 0.3s ease, width 0.3s ease;
  position: relative;
  max-width: 100vw;
  
  @media (max-width: 1200px) {
    margin-left: ${({ sidebarVisible }) => (sidebarVisible ? "280px" : "0")};
    width: ${({ sidebarVisible }) => 
      sidebarVisible ? "calc(100% - 280px)" : "100%"};
  }
  
  @media (max-width: 992px) {
    margin-left: ${({ sidebarVisible }) => (sidebarVisible ? "250px" : "0")};
    width: ${({ sidebarVisible }) => 
      sidebarVisible ? "calc(100% - 250px)" : "100%"};
  }
  
  @media (max-width: 768px) {
    margin-left: 0;
    width: 100%;
  }
`;

const ContentContainer = styled.div`
  padding: ${({ theme }) => theme.spacing.xl};
  padding-top: calc(70px + ${({ theme }) => theme.spacing.xl});
  width: 100%;
  box-sizing: border-box;
  position: relative;
  z-index: 1;
  
  @media (max-width: 1200px) {
    padding: ${({ theme }) =>
      `calc(70px + ${theme.spacing.lg}) ${theme.spacing.lg}`};
  }
  
  @media (max-width: 992px) {
    padding: ${({ theme }) =>
      `calc(65px + ${theme.spacing.md}) ${theme.spacing.md}`};
  }
  
  @media (max-width: 768px) {
    padding: ${({ theme }) =>
      `calc(60px + ${theme.spacing.sm}) ${theme.spacing.md}`};
    margin-top: 20px;
  }
  
  @media (max-width: 576px) {
    padding: ${({ theme }) =>
      `calc(60px + ${theme.spacing.xs}) ${theme.spacing.sm}`};
  }
`;

// Updated HeaderWrapper to match sidebar widths at different breakpoints
const HeaderWrapper = styled.div`
  position: fixed;
  top: 0;
  right: 0;
  left: ${({ sidebarVisible }) => (sidebarVisible ? "280px" : "0")};
  z-index: 10;
  transition: left 0.3s ease;
  
  @media (max-width: 1200px) {
    left: ${({ sidebarVisible }) => (sidebarVisible ? "260px" : "0")};
  }
  
  @media (max-width: 992px) {
    left: ${({ sidebarVisible }) => (sidebarVisible ? "250px" : "0")};
  }
  
  @media (max-width: 768px) {
    left: 0;
    width: 100%;
  }
`;

const Layout = ({ children }) => {
  // State to track sidebar visibility
  const [sidebarVisible, setSidebarVisible] = useState(true);
  
  // State to track screen size
  const [isMobile, setIsMobile] = useState(false);
  
  // Track sidebar state for communication between components
  const [sidebarState, setSidebarState] = useState({
    isOpen: window.innerWidth > 992,
    width: 280
  });

  // Effect to handle responsiveness
  useEffect(() => {
    const checkResponsive = () => {
      // Check if we're on mobile view
      const mobile = window.innerWidth <= 768;
      setIsMobile(mobile);
      
      // Set sidebar visibility based on screen size
      if (mobile) {
        setSidebarVisible(false);
      } else if (window.innerWidth > 992) {
        setSidebarVisible(true);
      }
      
      // Update sidebar width based on screen size
      let width = 280;
      if (window.innerWidth <= 1200 && window.innerWidth > 992) {
        width = 260;
      } else if (window.innerWidth <= 992) {
        width = 250;
      }
      
      setSidebarState(prev => ({
        ...prev,
        isOpen: window.innerWidth > 992,
        width
      }));
    };
    
    // Run on mount and window resize
    checkResponsive();
    window.addEventListener("resize", checkResponsive);
    
    // Cleanup on unmount
    return () => window.removeEventListener("resize", checkResponsive);
  }, []);

  // Toggle sidebar visibility
  const toggleSidebar = () => {
    setSidebarVisible(prev => !prev);
    setSidebarState(prev => ({
      ...prev,
      isOpen: !prev.isOpen
    }));
  };

  // Function to handle sidebar state changes from Sidebar component
  const handleSidebarChange = (isOpen) => {
    setSidebarVisible(isOpen && !isMobile);
    setSidebarState(prev => ({
      ...prev,
      isOpen
    }));
  };

  return (
    <LayoutContainer>
      {/* Pass necessary props to Sidebar for proper coordination */}
      <Sidebar 
        onToggle={handleSidebarChange} 
        initialState={sidebarState.isOpen} 
      />
      
      <Main sidebarVisible={sidebarVisible && !isMobile}>
        <HeaderWrapper sidebarVisible={sidebarVisible && !isMobile}>
          <Header 
            toggleSidebar={toggleSidebar} 
            sidebarVisible={sidebarState.isOpen}
          />
        </HeaderWrapper>
        <ContentContainer>{children}</ContentContainer>
      </Main>
    </LayoutContainer>
  );
};

export default Layout;