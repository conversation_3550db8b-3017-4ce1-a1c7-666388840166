import React from "react";
import styled, { keyframes } from "styled-components";
import { School, People, ShoppingCart, Settings } from "@styled-icons/material";
const rotateBook = keyframes`
  0% {
    transform: rotateY(0deg);
  }
  100% {
    transform: rotateY(360deg);
  }
`;
const pulse = keyframes`
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
`;
const bounce = keyframes`
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
`;
const LoaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: ${(props) => (props.fullScreen ? "100vh" : "200px")};
  background-color: ${(props) =>
    props.fullScreen ? "rgba(255, 255, 255, 0.95)" : "transparent"};
  position: ${(props) => (props.fullScreen ? "fixed" : "relative")};
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
`;
const BookContainer = styled.div`
  position: relative;
  width: 120px;
  height: 80px;
  perspective: 600px;
  animation: ${bounce} 2s ease-in-out infinite;
`;
const Book = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  animation: ${rotateBook} 2.5s infinite linear;
`;
const BookCover = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  background: ${({ theme }) => theme.colors.primary};
  border-radius: 5px;
  transform: rotateY(0deg) translateZ(10px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 18px;
`;
const BookBack = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  background: ${({ theme }) => theme.colors.secondary || "#4a6fa5"};
  border-radius: 5px;
  transform: rotateY(180deg) translateZ(10px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
`;
const BookSpine = styled.div`
  position: absolute;
  width: 20px;
  height: 100%;
  left: 50%;
  margin-left: -10px;
  background: ${({ theme }) => theme.colors.info || "#2d9cdb"};
  transform: rotateY(90deg) translateZ(60px);
  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);
`;
const LoadingText = styled.div`
  margin-top: 30px;
  font-size: ${(props) => (props.size === "large" ? "1.5rem" : "1.2rem")};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.primary};
  animation: ${pulse} 1.5s infinite ease-in-out;
`;
const PatentIcon = styled.div`
  position: absolute;
  font-size: 24px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
`;
const IconWrapper = styled.div`
  svg {
    width: 32px;
    height: 32px;
    color: rgba(255, 255, 255, 0.8);
  }
`;
const Loader = ({
  section = "default",
  fullScreen = false,
  size = "medium",
}) => {
  const getLoaderProps = () => {
    switch (section) {
      case "courses":
        return {
          text: "Loading courses...",
          icon: <School />,
        };
      case "users":
        return {
          text: "Loading users...",
          icon: <People />,
        };
      case "purchases":
        return {
          text: "Loading purchases...",
          icon: <ShoppingCart />,
        };
      case "settings":
        return {
          text: "Loading settings...",
          icon: <Settings />,
        };
      case "login":
        return {
          text: "Signing in...",
          icon: "PP",
        };
      default:
        return {
          text: "Loading...",
          icon: "PP",
        };
    }
  };
  const { text, icon } = getLoaderProps();
  return (
    <LoaderContainer fullScreen={fullScreen}>
      <BookContainer>
        <Book>
          <BookCover>
            <PatentIcon>
              {typeof icon === "string" ? (
                icon
              ) : (
                <IconWrapper>{icon}</IconWrapper>
              )}
            </PatentIcon>
          </BookCover>
          <BookBack />
          <BookSpine />
        </Book>
      </BookContainer>
      <LoadingText size={size}>{text}</LoadingText>
    </LoaderContainer>
  );
};
export default Loader;
