import React, { useState, useEffect, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import styled from "styled-components";
import { NavLink } from "../../styles/StyledComponents";
import { useAuth } from "../../contexts/AuthContext";
import {
  Dashboard as DashboardIcon,
  LibraryBooks as CoursesIcon,
  People as UsersIcon,
  ShoppingCart as PurchasesIcon,
  Menu as MenuIcon,
  Close as CloseIcon,
  Book as BookIcon,
  MenuBook as MyBookIcon,
  LocalShipping as ShippingIcon,
  EmojiObjects as LightBulbIcon,
} from "@styled-icons/material";

// Refactored SidebarContainer with improved responsive behavior
const SidebarContainer = styled.div`
  width: 280px;
  height: 100vh;
  background-color: #009F9F;
  box-shadow: ${({ theme }) => theme.shadows.md};
  position: fixed;
  left: 0;
  top: 0;
  z-index: 100;
  transform: translateX(${({ isOpen }) => (isOpen ? "0" : "-100%")});
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  max-height: 100vh;
  
  @media (max-width: 1200px) {
    width: 280px;
  }
  
  @media (max-width: 992px) {
    width: 250px;
  }
  
  @media (max-width: 768px) {
    width: 240px;
  }
  
  @media (max-width: 576px) {
    width: 100%;
    max-width: 260px;
  }
`;

const Logo = styled.div`
  padding: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  min-height: 70px;
  position: sticky;
  top: 0;
  background-color: #009F9F;
  z-index: 2;
  gap: ${({ theme }) => theme.spacing.sm};
  
  h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    text-align: center;
  }
  
  @media (max-width: 768px) {
    min-height: 60px;
    padding: ${({ theme }) => theme.spacing.sm};
    
    h1 {
      font-size: 1.3rem;
    }
  }
  
  @media (max-width: 576px) {
    min-height: 56px;
    
    h1 {
      font-size: 1.2rem;
    }
  }
`;

const BackButton = styled.button`
  background: none;
  border: none;
  padding: ${({ theme }) => theme.spacing.xs};
  cursor: pointer;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
  
  &:hover {
    transform: translateX(-2px);
  }
  
  svg {
    width: 24px;
    height: 24px;
    
    @media (max-width: 768px) {
      width: 22px;
      height: 22px;
    }
    
    @media (max-width: 576px) {
      width: 20px;
      height: 20px;
    }
  }
`;

// Improved NavMenu with better scrolling behavior
const NavMenu = styled.nav`
  padding: ${({ theme }) => theme.spacing.md} 0;
  flex: 1;
  overflow-y: auto;
  background-color: #009F9F;
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 10px;
  }
  
  @media (max-width: 992px) {
    padding: ${({ theme }) => `${theme.spacing.sm} 0`};
  }
  
  @media (max-width: 576px) {
    padding: ${({ theme }) => `${theme.spacing.xs} 0`};
  }
`;

const NavSection = styled.div`
  padding: ${({ theme }) => `0 ${theme.spacing.sm} ${theme.spacing.md}`};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  
  h3 {
    font-size: 0.8rem;
    text-transform: uppercase;
    color: white;
    opacity: 0.8;
    padding: ${({ theme }) => `${theme.spacing.md} ${theme.spacing.md}`};
    margin: 0;
    font-weight: 600;
    letter-spacing: 0.5px;
  }
  
  @media (max-width: 992px) {
    padding: ${({ theme }) => `0 ${theme.spacing.xs} ${theme.spacing.sm}`};
    margin-bottom: ${({ theme }) => theme.spacing.sm};
    
    h3 {
      padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.sm}`};
      font-size: 0.75rem;
    }
  }
  
  @media (max-width: 576px) {
    padding: ${({ theme }) => `0 ${theme.spacing.xs} ${theme.spacing.xs}`};
    
    h3 {
      padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.xs}`};
      font-size: 0.7rem;
    }
  }
`;

// Simplified StyledNavLink to avoid redundancy
const StyledNavLink = styled(NavLink)`
  @media (max-width: 992px) {
    padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.md}`};
    font-size: 0.9rem;
  }
  
  @media (max-width: 576px) {
    padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.sm}`};
    font-size: 0.85rem;
  }
`;

const StyledIcon = styled.div`
  width: 20px;
  height: 20px;
  margin-right: ${({ theme }) => theme.spacing.md};
  color: white;
  flex-shrink: 0;
  
  @media (max-width: 992px) {
    margin-right: ${({ theme }) => theme.spacing.sm};
    width: 18px;
    height: 18px;
  }
  
  @media (max-width: 576px) {
    width: 16px;
    height: 16px;
    margin-right: ${({ theme }) => theme.spacing.xs};
  }
`;

// Improved MobileToggle positioning and responsiveness
const MobileToggle = styled.button`
  display: none;
  position: fixed;
  top: 15px;
  left: 15px;
  z-index: 200;
  background-color: ${({ isOpen }) => isOpen ? "white" : "#009F9F"};
  color: ${({ isOpen }) => isOpen ? "#009F9F" : "white"};
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: ${({ theme }) => theme.shadows.md};
  transition: all 0.3s ease;
  
  @media (max-width: 992px) {
    display: flex;
  }
  
  @media (max-width: 768px) {
    width: 40px;
    height: 40px;
    top: 12px;
    left: 12px;
  }
  
  @media (max-width: 576px) {
    width: 36px;
    height: 36px;
    top: 10px;
    left: 10px;
  }
  
  &:hover {
    transform: scale(1.05);
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  svg {
    width: 24px;
    height: 24px;
    
    @media (max-width: 768px) {
      width: 20px;
      height: 20px;
    }
    
    @media (max-width: 576px) {
      width: 18px;
      height: 18px;
    }
  }
`;

// Enhanced Overlay with proper z-index
const Overlay = styled.div`
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
  backdrop-filter: blur(2px);
  transition: opacity 0.3s ease;
  opacity: ${({ isOpen }) => (isOpen ? 1 : 0)};
  pointer-events: ${({ isOpen }) => (isOpen ? "all" : "none")};
  
  @media (max-width: 992px) {
    display: block;
  }
`;

const FooterSection = styled.div`
  padding: ${({ theme }) => theme.spacing.md};
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 0.75rem;
  color: white;
  text-align: center;
  background-color: #009F9F;
  
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing.sm};
    font-size: 0.7rem;
  }
  
  @media (max-width: 576px) {
    padding: ${({ theme }) => theme.spacing.xs};
    font-size: 0.65rem;
  }
`;

const NavGroup = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.sm};
  
  @media (max-width: 576px) {
    margin-bottom: ${({ theme }) => theme.spacing.xs};
  }
`;

// Improved EnhancedNavLink with better hover/active states
const EnhancedNavLink = styled(NavLink)`
  padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.md}`};
  display: flex;
  align-items: center;
  color: ${({ active }) => active ? "white" : "rgba(255, 255, 255, 0.8)"};
  background-color: ${({ active }) => active ? "rgba(255, 255, 255, 0.2)" : "transparent"};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  text-decoration: none;
  transition: all 0.2s ease;
  margin: ${({ theme }) => `${theme.spacing.xs} 0`};
  font-weight: ${({ active }) => (active ? "600" : "500")};
  font-size: 0.95rem;
  position: relative;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateX(2px);
  }
  
  ${({ active }) =>
    active &&
    `
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      height: 60%;
      width: 3px;
      background-color: white;
      border-radius: 0 3px 3px 0;
    }
  `}
  
  @media (max-width: 992px) {
    padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.md}`};
    font-size: 0.9rem;
  }
  
  @media (max-width: 768px) {
    padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.sm}`};
    margin: ${({ theme }) => `${theme.spacing.xs} 0`};
    font-size: 0.85rem;
  }
  
  @media (max-width: 576px) {
    font-size: 0.8rem;
    padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.sm}`};
    
    ${({ active }) =>
      active &&
      `
      &::before {
        width: 2px;
      }
    `}
  }
`;

const Sidebar = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const sidebarRef = useRef(null);
  const isAdmin = user && user.isAdmin;
  
  // Improved resize handler with proper breakpoints
  const handleResize = () => {
    if (window.innerWidth > 992) {
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  };
  
  // Initial setup on component mount
  useEffect(() => {
    // Set initial state based on screen size
    setIsOpen(window.innerWidth > 992);
    
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);
  
  // Close sidebar on navigation for mobile view
  useEffect(() => {
    if (window.innerWidth <= 992) {
      setIsOpen(false);
    }
  }, [location.pathname]);
  
  // Handle clicks outside sidebar
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target) &&
        window.innerWidth <= 992 &&
        isOpen
      ) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);
  
  const isActive = (path) => {
    return location.pathname === path;
  };
  
  return (
    <>
      <MobileToggle
        onClick={() => setIsOpen(!isOpen)}
        isOpen={isOpen}
        aria-label={isOpen ? "Close menu" : "Open menu"}
      >
        {isOpen ? <CloseIcon /> : <MenuIcon />}
      </MobileToggle>
      
      <Overlay isOpen={isOpen} onClick={() => setIsOpen(false)} />
      
      <SidebarContainer isOpen={isOpen} ref={sidebarRef}>
        <Logo>
          <BackButton onClick={() => navigate("/")} aria-label="Go to home">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" />
            </svg>
          </BackButton>
          <h1>PatentPionner</h1>
        </Logo>
        
        <NavMenu>
          <NavSection>
            <EnhancedNavLink
              to="/dashboard"
              active={isActive("/dashboard") ? "true" : undefined}
            >
              <StyledIcon as={DashboardIcon} />
              Dashboard
            </EnhancedNavLink>
            
            <NavGroup>
              <EnhancedNavLink
                to={isAdmin ? "/courses" : "/courses/my"}
                active={
                  location.pathname.startsWith("/courses") ? "true" : undefined
                }
              >
                <StyledIcon as={CoursesIcon} />
                {isAdmin ? "All Courses" : "My Courses"}
              </EnhancedNavLink>
            </NavGroup>
            
            <NavGroup>
              <EnhancedNavLink
                to={isAdmin ? "/shipment" : "/my-shipments"}
                active={
                  isActive(isAdmin ? "/shipment" : "/my-shipments")
                    ? "true"
                    : undefined
                }
              >
                <StyledIcon as={ShippingIcon} />
                {isAdmin ? "Shipment Management" : "My Shipments"}
              </EnhancedNavLink>
            </NavGroup>
            
            <NavGroup>
              <EnhancedNavLink
                to={isAdmin ? "/books" : "/books/my"}
                active={
                  location.pathname.startsWith("/books") ? "true" : undefined
                }
              >
                <StyledIcon as={isAdmin ? BookIcon : MyBookIcon} />
                {isAdmin ? "All Books" : "My Books"}
              </EnhancedNavLink>
            </NavGroup>
            
            {isAdmin && (
              <NavGroup>
                <EnhancedNavLink
                  to="/admin/insights"
                  active={
                    location.pathname.startsWith("/admin/insights") ? "true" : undefined
                  }
                >
                  <StyledIcon as={LightBulbIcon} />
                  Insights Management
                </EnhancedNavLink>
              </NavGroup>
            )}
            
            {isAdmin && (
              <EnhancedNavLink
                to="/users"
                active={isActive("/users") ? "true" : undefined}
              >
                <StyledIcon as={UsersIcon} />
                Registered Users
              </EnhancedNavLink>
            )}
            
            <EnhancedNavLink
              to="/purchases"
              active={isActive("/purchases") ? "true" : undefined}
            >
              <StyledIcon as={PurchasesIcon} />
              {isAdmin ? "All Purchases" : "My Purchases"}
            </EnhancedNavLink>
          </NavSection>
        </NavMenu>
        
        <FooterSection>
          <p>
            © {new Date().getFullYear()} PatentPionner. All rights reserved.
          </p>
        </FooterSection>
      </SidebarContainer>
    </>
  );
};

export default Sidebar;