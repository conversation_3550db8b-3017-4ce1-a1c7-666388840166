import React, { useState, useEffect } from "react";
import styled, { keyframes } from "styled-components";
const slideIn = keyframes`
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
`;
const slideOut = keyframes`
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-20px);
    opacity: 0;
  }
`;
const ToastContainer = styled.div`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: auto;
  max-width: 90%;
`;
const ToastItem = styled.div`
  min-width: 350px;
  padding: 16px 24px;
  background-color: ${({ theme, type }) =>
    type === "success"
      ? theme.colors.success
      : type === "error"
        ? theme.colors.danger
        : type === "info"
          ? theme.colors.info
          : theme.colors.warning};
  color: white;
  border-radius: 8px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: space-between;
  align-items: center;
  animation: ${({ isExiting }) => (isExiting ? slideOut : slideIn)} 0.3s
    ease-in-out;
  font-weight: 500;
  text-align: center;
`;
const ToastMessage = styled.div`
  font-size: 16px;
  font-weight: 600;
  flex: 1;
  text-align: center;
`;
const CloseButton = styled.button`
  background: transparent;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin-left: 12px;
  opacity: 0.8;
  &:hover {
    opacity: 1;
  }
`;
const Toast = ({ toasts, removeToast }) => {
  return (
    <ToastContainer>
      {toasts.map((toast) => (
        <ToastItem key={toast.id} type={toast.type} isExiting={toast.isExiting}>
          <ToastMessage>{toast.message}</ToastMessage>
          <CloseButton onClick={() => removeToast(toast.id)}>×</CloseButton>
        </ToastItem>
      ))}
    </ToastContainer>
  );
};
export default Toast;
