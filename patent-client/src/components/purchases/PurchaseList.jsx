import React, { useState, useEffect } from "react";
import styled from "styled-components";
import {
  PageTitle,
  Card,
  Table,
  SearchInput,
} from "../../styles/StyledComponents";
import * as courseService from "../../services/courseService";
import Loader from "../layout/Loader";
import { useAuth } from "../../contexts/AuthContext";
import Pagination from "../common/Pagination";
import { API_BASE_URL } from "../../config/config";
const breakpoints = {
  mobile: "480px",
  tablet: "768px",
  desktop: "1024px",
};
const PurchaseHeader = styled.div`
  display: flex;
  flex-direction: column;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  @media (max-width: ${breakpoints.tablet}) {
    margin-top: 0px;
  }
  @media (min-width: ${breakpoints.tablet}) {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: ${({ theme }) => theme.spacing.lg};
  }
`;
const SearchContainer = styled.div`
  width: 100%;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  @media (min-width: ${breakpoints.tablet}) {
    margin-bottom: ${({ theme }) => theme.spacing.lg};
  }
`;
const NoDataMessage = styled.div`
  text-align: center;
  margin: ${({ theme }) => theme.spacing.lg} 0;
  color: ${({ theme }) => theme.colors.textSecondary};
  padding: ${({ theme }) => theme.spacing.md};
`;
const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colors.error};
  padding: ${({ theme }) => theme.spacing.md};
  border: 1px solid ${({ theme }) => theme.colors.error};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin: ${({ theme }) => theme.spacing.md} 0;
`;
const TableContainer = styled.div`
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;

`;
const StyledTable = styled(Table)`
  width: 100%;
  min-width: 600px;
  border-collapse: collapse;
  thead tr {
    border-bottom: 1px solid #e0e0e0;
    background-color: #f9f9f9;
  }
  th {
    text-align: left;
    padding: ${({ theme }) => theme.spacing.sm};
    font-weight: 500;
    white-space: nowrap;
    @media (min-width: ${breakpoints.tablet}) {
      padding: ${({ theme }) => theme.spacing.md};
    }
  }
  td {
    padding: ${({ theme }) => theme.spacing.sm};
    border-bottom: 1px solid #f0f0f0;
    word-break: break-word;
    @media (min-width: ${breakpoints.tablet}) {
      padding: ${({ theme }) => theme.spacing.md};
    }
  }
  tr:last-child td {
    border-bottom: none;
  }
  @media (max-width: ${breakpoints.tablet}) {
    font-size: 0.9rem;
  }
  @media (max-width: ${breakpoints.mobile}) {
    font-size: 0.85rem;
  }
`;
const CardList = styled.div`
  display: none; /* Always hide card list */
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.md};
  @media (min-width: ${breakpoints.tablet}) {
    display: none;
  }
`;
const PurchaseCard = styled(Card)`
  padding: ${({ theme }) => theme.spacing.md};
`;
const CardRow = styled.div`
  display: flex;
  justify-content: space-between;
  padding: ${({ theme }) => theme.spacing.xs} 0;
  &:not(:last-child) {
    border-bottom: 1px solid #f0f0f0;
  }
`;
const CardLabel = styled.span`
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const CardValue = styled.span`
  text-align: right;
`;
const TableView = styled.div`
  display: block; /* Always show table view */
  @media (min-width: ${breakpoints.tablet}) {
    display: block;
  }
`;
const StatusBadge = styled.span`
  padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.sm}`};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-weight: 500;
  font-size: 0.9rem;
  display: inline-block;
  &.completed {
    background-color: #e6f7ed;
    color: #0d963c;
  }
  &.pending {
    background-color: #fff8e6;
    color: #b78105;
  }
  &.failed {
    background-color: #ffebee;
    color: #d32f2f;
  }
  &.refunded {
    background-color: #e8eaf6;
    color: #3f51b5;
  }
  @media (max-width: ${breakpoints.mobile}) {
    padding: ${({ theme }) => `${theme.spacing.xxs} ${theme.spacing.xs}`};
    font-size: 0.8rem;
  }
`;
const PurchaseList = () => {
  const [purchases, setPurchases] = useState([]);
  const [filteredPurchases, setFilteredPurchases] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [coursesLoading, setCoursesLoading] = useState({});
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState(null);
  const { user } = useAuth();
  const isAdmin = user && user.isAdmin;
  useEffect(() => {
    const fetchPurchases = async () => {
      try {
        setLoading(true);
        let response;
        if (isAdmin) {
          try {
            const directResponse = await fetch(
              `${API_BASE_URL}/purchases?page=${currentPage}&limit=6`,
              {
                method: "GET",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${user.accessToken || user.token}`,
                },
              },
            );
            if (directResponse.ok) {
              const data = await directResponse.json();
              if (Array.isArray(data)) {
                setPurchases(data);
                setFilteredPurchases(data);
                setPagination(null);
              } else if (data && data.purchases) {
                setPurchases(data.purchases);
                setFilteredPurchases(data.purchases);
                setPagination(data.pagination);
              } else {
                setPurchases([]);
                setFilteredPurchases([]);
                setPagination(null);
              }
              response = data;
            } else {
              console.error(
                "Admin purchases fetch failed:",
                directResponse.status,
              );
              setPurchases([]);
              setFilteredPurchases([]);
              setPagination(null);
            }
          } catch (error) {
            console.error("Admin purchases fetch error:", error);
            setPurchases([]);
            setFilteredPurchases([]);
            setPagination(null);
          }
        } else {
          try {
            const directResponse = await fetch(
              `${API_BASE_URL}/purchases/my?page=${currentPage}&limit=6`,
              {
                method: "GET",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${user.accessToken || user.token}`,
                },
              },
            );
            if (directResponse.ok) {
              const data = await directResponse.json();
              if (Array.isArray(data)) {
                setPurchases(data);
                setFilteredPurchases(data);
                setPagination(null);
              } else if (data && data.purchases) {
                setPurchases(data.purchases);
                setFilteredPurchases(data.purchases);
                setPagination(data.pagination);
              } else {
                setPurchases([]);
                setFilteredPurchases([]);
                setPagination(null);
              }
              response = data;
            } else {
              console.error(
                "User purchases fetch failed:",
                directResponse.status,
              );
              setPurchases([]);
              setFilteredPurchases([]);
              setPagination(null);
            }
          } catch (error) {
            console.error("User purchases fetch error:", error);
            setPurchases([]);
            setFilteredPurchases([]);
            setPagination(null);
          }
        }
        const data = Array.isArray(response)
          ? response
          : response && response.purchases
            ? response.purchases
            : [];
        const enhancedData = data.map((purchase) => {
          const enhancedPurchase = { ...purchase };
          if (purchase.courseId) {
            let enhancedCourseId;
            if (typeof purchase.courseId === "object") {
              enhancedCourseId = {
                ...purchase.courseId,
                title: purchase.courseId.title || "Untitled Course",
              };
            } else {
              enhancedCourseId = {
                _id: purchase.courseId,
                title: "Loading...",
              };
            }
            enhancedPurchase.courseId = enhancedCourseId;
          }
          if (purchase.bookId) {
            let enhancedBookId;
            if (typeof purchase.bookId === "object") {
              enhancedBookId = {
                ...purchase.bookId,
                title: purchase.bookId.title || "Untitled Book",
              };
            } else {
              enhancedBookId = {
                _id: purchase.bookId,
                title: "Loading Book...",
              };
            }
            enhancedPurchase.bookId = enhancedBookId;
          }
          return enhancedPurchase;
        });
        setPurchases(enhancedData);
        setFilteredPurchases(enhancedData);
        const purchasesNeedingCourseDetails = enhancedData.filter((p) => {
          if (!p.courseId || p.courseId._id === "unknown") {
            return false;
          }
          return (
            p.courseId.title === "Loading..." ||
            p.courseId.title === "Untitled Course"
          );
        });
        if (purchasesNeedingCourseDetails.length > 0) {
          const updatedPurchases = [...enhancedData];
          for (const purchase of purchasesNeedingCourseDetails) {
            const courseId =
              typeof purchase.courseId === "object"
                ? purchase.courseId._id
                : purchase.courseId;
            if (courseId && courseId !== "unknown") {
              try {
                setCoursesLoading((prev) => ({ ...prev, [courseId]: true }));
                const courseDetails =
                  await courseService.getCourseById(courseId);
                const index = updatedPurchases.findIndex(
                  (p) => p._id === purchase._id,
                );
                if (index !== -1) {
                  updatedPurchases[index] = {
                    ...updatedPurchases[index],
                    courseId: {
                      _id: courseDetails._id || courseId,
                      title: courseDetails.title || "Untitled Course",
                      ...(courseDetails || {}),
                    },
                  };
                }
              } catch (err) {
                console.error(
                  `Failed to fetch details for course: ${courseId}`,
                  err,
                );
                const index = updatedPurchases.findIndex(
                  (p) => p._id === purchase._id,
                );
                if (index !== -1) {
                  updatedPurchases[index] = {
                    ...updatedPurchases[index],
                    courseId: {
                      _id: courseId,
                      title: "Course Not Found",
                      error: true,
                    },
                  };
                }
              } finally {
                setCoursesLoading((prev) => ({ ...prev, [courseId]: false }));
              }
            }
          }
          setPurchases(updatedPurchases);
          setFilteredPurchases(updatedPurchases);
        }
        setError(null);
      } catch (err) {
        console.error("Failed to fetch purchases:", err);
        setError("Failed to load purchase data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    if (user) {
      fetchPurchases();
    }
  }, [user, isAdmin, currentPage]);
  useEffect(() => {
    if (purchases.length > 0 && searchTerm.trim() !== "") {
      const searchTermLower = searchTerm.toLowerCase();
      const filtered = purchases.filter((purchase) => {
        const userMatch =
          purchase.userId && typeof purchase.userId === "object"
            ? (purchase.userId.firstName &&
                purchase.userId.firstName
                  .toLowerCase()
                  .includes(searchTermLower)) ||
              (purchase.userId.lastName &&
                purchase.userId.lastName
                  .toLowerCase()
                  .includes(searchTermLower)) ||
              (purchase.userId.firstName &&
                purchase.userId.lastName &&
                `${purchase.userId.firstName} ${purchase.userId.lastName}`
                  .toLowerCase()
                  .includes(searchTermLower)) ||
              (purchase.userId.username &&
                purchase.userId.username
                  .toLowerCase()
                  .includes(searchTermLower)) ||
              (purchase.userId.email &&
                purchase.userId.email.toLowerCase().includes(searchTermLower))
            : false;
        const courseNameMatch =
          purchase.courseId &&
          typeof purchase.courseId === "object" &&
          purchase.courseId.title &&
          typeof purchase.courseId.title === "string"
            ? purchase.courseId.title.toLowerCase().includes(searchTermLower)
            : false;
        const bookNameMatch =
          purchase.bookId &&
          typeof purchase.bookId === "object" &&
          purchase.bookId.title &&
          typeof purchase.bookId.title === "string"
            ? purchase.bookId.title.toLowerCase().includes(searchTermLower)
            : false;
        const amountMatch = purchase.amount
          ? purchase.amount.toString().includes(searchTermLower)
          : false;
        return userMatch || courseNameMatch || bookNameMatch || amountMatch;
      });
      setFilteredPurchases(filtered);
    } else {
      setFilteredPurchases(purchases);
    }
  }, [purchases, searchTerm]);
  const formatDate = (dateString) => {
    try {
      if (!dateString) return "N/A";
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return "Invalid date";
      }
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      return "Error";
    }
  };
  const getItemName = (purchase) => {
    const courseId =
      purchase.courseId && typeof purchase.courseId === "object"
        ? purchase.courseId._id
        : purchase.courseId || "unknown";
    let courseTitle = "Unknown Course";
    let courseError = false;
    if (purchase.courseId && typeof purchase.courseId === "object") {
      courseTitle = purchase.courseId.title || "Untitled Course";
      courseError = purchase.courseId.error || false;
    }
    if (purchase.bookId) {
      return typeof purchase.bookId === "object"
        ? `Book: ${purchase.bookId.title || "Unknown Book"}`
        : `Book ID: ${purchase.bookId}`;
    } else if (purchase.courseId) {
      return coursesLoading[courseId]
        ? "Loading item info..."
        : courseError
          ? "Course not found"
          : courseTitle === "Loading..."
            ? "Fetching item details..."
            : `Course: ${courseTitle}`;
    } else {
      return "Unknown item";
    }
  };
  const getUserName = (purchase) => {
    if (purchase.userId && typeof purchase.userId === "object") {
      return purchase.userId.firstName && purchase.userId.lastName
        ? `${purchase.userId.firstName} ${purchase.userId.lastName}`
        : purchase.userId.username || purchase.userId.email || "Unknown User";
    }
    return "Unknown User";
  };
  if (loading) {
    return <Loader section="purchases" />;
  }
  if (error) {
    return <ErrorMessage>{error}</ErrorMessage>;
  }
  return (
    <div>
      <PurchaseHeader>
        <PageTitle>{isAdmin ? "All Purchases" : "My Purchases"}</PageTitle>
      </PurchaseHeader>
      <SearchContainer>
        <SearchInput
          placeholder={
            isAdmin
              ? "Search by customer, item or amount..."
              : "Search by item or amount..."
          }
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </SearchContainer>
      {filteredPurchases.length > 0 ? (
        <>
          <TableView>
            <Card>
              <TableContainer>
                <StyledTable>
                  <thead>
                    <tr>
                      {isAdmin && <th>Customer</th>}
                      <th>Item</th>
                      <th>Amount</th>
                      <th>Date</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredPurchases.map((purchase) => (
                      <tr key={purchase._id}>
                        {isAdmin && <td>{getUserName(purchase)}</td>}
                        <td>{getItemName(purchase)}</td>
                        <td>${parseFloat(purchase.amount).toFixed(2)}</td>
                        <td>{formatDate(purchase.date)}</td>
                        <td>
                          <StatusBadge
                            className={purchase.status.toLowerCase()}
                          >
                            {purchase.status}
                          </StatusBadge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </StyledTable>
              </TableContainer>
            </Card>
          </TableView>
          
          {/* CardList component is kept in the code but will never be displayed */}
          
          {pagination &&
            !searchTerm &&
            pagination.totalCount > 6 &&
            pagination.totalPages > 1 && (
              <Pagination
                pagination={pagination}
                onPageChange={(page) => {
                  setCurrentPage(page);
                  window.scrollTo(0, 0);
                }}
                variant="default"
              />
            )}
        </>
      ) : (
        <NoDataMessage>
          {searchTerm
            ? `No purchases found for "${searchTerm}". Try a different search.`
            : isAdmin
              ? "No purchase records available."
              : "You have not made any purchases yet."}
        </NoDataMessage>
      )}
    </div>
  );
};
export default PurchaseList;
