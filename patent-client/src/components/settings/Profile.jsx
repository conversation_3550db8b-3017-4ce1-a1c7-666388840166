"use client";
import { useState, useEffect } from "react";
import styled from "styled-components";
import {
  PageTitle,
  Card,
  FormGroup,
  Label,
  Input,
  Button,
} from "../../styles/StyledComponents";
import * as userService from "../../services/userService";
import { useToast } from "../../contexts/ToastContext";
import { useAuth } from "../../contexts/AuthContext";
import Loader from "../layout/Loader";
import { Person, Email, Lock } from "@styled-icons/material";
const ProfileContainer = styled.div`
  width: 100%;
  padding: 0 ${({ theme }) => theme.spacing.sm};
  @media (max-width: 768px) {
    padding: 0 ${({ theme }) => theme.spacing.xs};
  }
`;
const ProfileCard = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding: ${({ theme }) => theme.spacing.lg};
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing.md};
    margin-bottom: ${({ theme }) => theme.spacing.md};
  }
  @media (max-width: 480px) {
    padding: ${({ theme }) => theme.spacing.sm};
  }
`;
const ProfileHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  @media (max-width: 480px) {
    margin-bottom: ${({ theme }) => theme.spacing.md};
  }
`;
const SectionTitle = styled.h3`
  font-size: 1.2rem;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  color: ${({ theme }) => theme.colors.dark};
`;
const ButtonsContainer = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.lg};
  @media (max-width: 480px) {
    flex-direction: column;
    width: 100%;
    button {
      width: 100%;
      margin-bottom: ${({ theme }) => theme.spacing.xs};
    }
  }
`;
const UserInfo = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  @media (max-width: 480px) {
    flex-direction: column;
    text-align: center;
  }
  .avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: ${({ theme }) => theme.colors.primary + "20"};
    color: ${({ theme }) => theme.colors.primary};
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: ${({ theme }) => theme.spacing.lg};
    font-size: 2rem;
    font-weight: bold;
    @media (max-width: 480px) {
      margin-right: 0;
      margin-bottom: ${({ theme }) => theme.spacing.md};
    }
  }
  .user-details {
    h2 {
      margin: 0 0 ${({ theme }) => theme.spacing.xs};
      font-size: 1.5rem;
      @media (max-width: 480px) {
        font-size: 1.3rem;
      }
    }
    p {
      margin: 0;
      color: ${({ theme }) => theme.colors.textSecondary};
      margin-bottom: ${({ theme }) => theme.spacing.xs};
      @media (max-width: 768px) {
        font-size: 0.9rem;
      }
    }
    .badge {
      display: inline-block;
      padding: 2px 8px;
      background-color: ${({ theme, isAdmin }) =>
        isAdmin ? theme.colors.warning + "30" : theme.colors.success + "30"};
      color: ${({ theme, isAdmin }) =>
        isAdmin ? theme.colors.warning : theme.colors.success};
      border-radius: 12px;
      font-size: 0.8rem;
      margin-left: ${({ theme }) => theme.spacing.xs};
    }
  }
`;
const InfoRow = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  @media (max-width: 480px) {
    flex-direction: column;
    align-items: flex-start;
    padding-bottom: ${({ theme }) => theme.spacing.sm};
    border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  }
  svg {
    width: 20px;
    height: 20px;
    color: ${({ theme }) => theme.colors.primary};
    margin-right: ${({ theme }) => theme.spacing.md};
    @media (max-width: 480px) {
      margin-bottom: ${({ theme }) => theme.spacing.xs};
    }
  }
  .label {
    width: 100px;
    font-weight: 500;
    color: ${({ theme }) => theme.colors.textSecondary};
    @media (max-width: 480px) {
      width: 100%;
      margin-bottom: ${({ theme }) => theme.spacing.xs};
    }
  }
  .value {
    flex: 1;
    @media (max-width: 480px) {
      width: 100%;
      word-break: break-word;
    }
  }
`;
const InfoRowMobile = styled.div`
  @media (max-width: 480px) {
    display: flex;
    flex-direction: column;
  }
`;
const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colors.danger};
  padding: ${({ theme }) => theme.spacing.md};
  border: 1px solid ${({ theme }) => theme.colors.danger};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin: ${({ theme }) => theme.spacing.md} 0;
  @media (max-width: 480px) {
    padding: ${({ theme }) => theme.spacing.sm};
    font-size: 0.9rem;
  }
`;
const SuccessMessage = styled.div`
  color: ${({ theme }) => theme.colors.success};
  padding: ${({ theme }) => theme.spacing.md};
  border: 1px solid ${({ theme }) => theme.colors.success};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin: ${({ theme }) => theme.spacing.md} 0;
  @media (max-width: 480px) {
    padding: ${({ theme }) => theme.spacing.sm};
    font-size: 0.9rem;
  }
`;
const EmailGroup = styled(FormGroup)`
  @media (max-width: 768px) {
    .email-actions {
      margin-top: ${({ theme }) => theme.spacing.sm};
      width: 100%;
      button {
        width: 100%;
      }
    }
  }
`;
const Profile = () => {
  const { user, updateCurrentUser } = useAuth();
  const { addToast } = useToast();
  const [loading, setLoading] = useState(true);
  const [savingProfile, setSavingProfile] = useState(false);
  const [savingPassword, setSavingPassword] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [passwordEditMode, setPasswordEditMode] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const getInitialUserData = () => {
    if (user) return user;
    try {
      const storedData = localStorage.getItem("patentpionner_user");
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        return {
          name: parsedData?.name,
          email: parsedData?.email,
          username: parsedData?.username,
          isAdmin: parsedData?.isAdmin,
          role: parsedData?.role,
          joinDate: parsedData?.joinDate || parsedData?.createdAt,
          status: parsedData?.status,
          courses: parsedData?.courses,
        };
      }
    } catch (error) {
      console.error("Error parsing user data from localStorage:", error);
    }
    return null;
  };
  const initialUserData = getInitialUserData();
  const [formData, setFormData] = useState({
    email: initialUserData?.email || "",
    username: initialUserData?.username || "",
  });
  const [displayUserData, setDisplayUserData] = useState(initialUserData);
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoading(true);
        const apiUserData = await userService.getUserProfile();
        setFormData({
          email: apiUserData.email || "",
          username: apiUserData.username || "",
        });
        setDisplayUserData(apiUserData);
      } catch (err) {
        console.error("Failed to fetch user data:", err);
        addToast(
          "Failed to load user profile. Please try again later.",
          "error",
        );
        if (!displayUserData) setDisplayUserData(initialUserData);
      } finally {
        setLoading(false);
      }
    };
    fetchUserData();
  }, [addToast, initialUserData]);
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };
  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordForm((prev) => ({
      ...prev,
      [name]: value,
    }));
  };
  const validatePasswordForm = () => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      addToast("New password and confirmation do not match", "error");
      return false;
    }
    const passwordValidation = {
      minLength: passwordForm.newPassword.length >= 6,
      hasUpperCase: /[A-Z]/.test(passwordForm.newPassword),
      hasSpecialChar: /[!@#$%^/&*(),.?":{}|<>]/.test(passwordForm.newPassword),
    };
    if (!passwordValidation.minLength) {
      addToast("Password must be at least 6 characters long", "error");
      return false;
    }
    if (!passwordValidation.hasUpperCase) {
      addToast("Password must contain at least one uppercase letter", "error");
      return false;
    }
    if (!passwordValidation.hasSpecialChar) {
      addToast("Password must contain at least one special character", "error");
      return false;
    }
    return true;
  };
  const handleUpdateProfile = async (e) => {
    e.preventDefault();
    setSavingProfile(true);
    try {
      const updatePayload = {
        email: formData.email.trim(),
        username: formData.username.trim(),
      };
      if (!updatePayload.email) {
        addToast("Email cannot be empty", "error");
        setSavingProfile(false);
        return;
      }
      if (!updatePayload.username) {
        addToast("Username cannot be empty", "error");
        setSavingProfile(false);
        return;
      }
      const updatedUser = await userService.updateUserProfile(updatePayload);
      const hasChanges =
        updatedUser.email !== displayUserData.email ||
        updatedUser.username !== displayUserData.username;
      if (!hasChanges) {
        console.warn(
          "Response contains same data as before. The update might not have been saved.",
        );
      }
      setDisplayUserData((prevData) => ({
        ...prevData,
        email: updatedUser.email,
        username: updatedUser.username,
      }));
      updateCurrentUser(updatedUser);
      setEditMode(false);
      addToast("Profile updated successfully", "success");
      try {
        const storedData = localStorage.getItem("patentpionner_user");
        if (storedData) {
          const parsedData = JSON.parse(storedData);
          const updatedData = {
            ...parsedData,
            email: updatedUser.email,
            username: updatedUser.username,
          };
          localStorage.setItem(
            "patentpionner_user",
            JSON.stringify(updatedData),
          );
        }
      } catch (err) {
        console.error("Error updating local storage:", err);
      }
    } catch (err) {
      addToast(err.message);
    } finally {
      setSavingProfile(false);
    }
  };
  const handleUpdatePassword = async (e) => {
    e.preventDefault();
    if (!validatePasswordForm()) {
      return;
    }
    setSavingPassword(true);
    try {
      await userService.updatePassword({
        currentPassword: passwordForm.currentPassword,
        password: passwordForm.newPassword,
      });
      setPasswordEditMode(false);
      setPasswordForm({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
      addToast("Password updated successfully", "success");
    } catch (err) {
      console.error("Failed to update password:", err);
      const errorMessage =
        err.response?.data?.message ||
        err.message ||
        "Failed to update password";
      addToast(errorMessage, "error");
    } finally {
      setSavingPassword(false);
    }
  };
  if (loading && !displayUserData && !initialUserData) {
    return <Loader section="profile" />;
  }
  const currentUserData = displayUserData || {};
  return (
    <ProfileContainer>
      <ProfileHeader>
        <PageTitle>My Profile</PageTitle>
      </ProfileHeader>
      <ProfileCard>
        {!editMode ? (
          <>
            <SectionTitle>Account Information</SectionTitle>
            <InfoRow>
              <Email />
              <InfoRowMobile>
                <div className="label">Email:</div>
                <div className="value">{currentUserData?.email || "N/A"}</div>
              </InfoRowMobile>
            </InfoRow>
            <InfoRow>
              <Person />
              <InfoRowMobile>
                <div className="label">Username:</div>
                <div className="value">
                  {currentUserData?.username || "N/A"}
                </div>
              </InfoRowMobile>
            </InfoRow>
            <Button
              variant="secondary"
              onClick={() => {
                setFormData({
                  email: currentUserData?.email || "",
                  username: currentUserData?.username || "",
                });
                setEditMode(true);
              }}
            >
              Edit Profile
            </Button>
          </>
        ) : (
          <form onSubmit={handleUpdateProfile}>
            <SectionTitle>Edit Profile</SectionTitle>
            <FormGroup>
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                name="username"
                value={formData.username}
                onChange={handleChange}
                placeholder="Your username"
                required
              />
            </FormGroup>
            <ButtonsContainer>
              <Button
                type="button"
                variant="primary"
                onClick={() => {
                  setEditMode(false);
                }}
                disabled={savingProfile}
              >
                Cancel
              </Button>
              <Button type="submit" variant="primary" disabled={savingProfile}>
                {savingProfile ? "Saving..." : "Save Changes"}
              </Button>
            </ButtonsContainer>
          </form>
        )}
      </ProfileCard>
      <ProfileCard>
        {!passwordEditMode ? (
          <>
            <SectionTitle>Password</SectionTitle>
            <InfoRow>
              <Lock />
              <InfoRowMobile>
                <div className="label">Password:</div>
                <div className="value">
                  <span>••••••••</span>
                </div>
              </InfoRowMobile>
            </InfoRow>
            <Button
              variant="secondary"
              onClick={() => {
                setPasswordEditMode(true);
                setPasswordForm({
                  currentPassword: "",
                  newPassword: "",
                  confirmPassword: "",
                });
                setShowCurrentPassword(false);
                setShowNewPassword(false);
                setShowConfirmPassword(false);
              }}
            >
              Change Password
            </Button>
          </>
        ) : (
          <form onSubmit={handleUpdatePassword}>
            <SectionTitle>Change Password</SectionTitle>
            <FormGroup>
              <Label htmlFor="currentPassword">Current Password</Label>
              <div style={{ position: "relative" }}>
                <Input
                  id="currentPassword"
                  name="currentPassword"
                  type={showCurrentPassword ? "text" : "password"}
                  value={passwordForm.currentPassword}
                  onChange={handlePasswordChange}
                  placeholder="Enter your current password"
                  required
                  style={{ paddingRight: "60px" }}
                />
                <button
                  type="button"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                  style={{
                    position: "absolute",
                    right: "10px",
                    top: "50%",
                    transform: "translateY(-50%)",
                    background: "none",
                    border: "none",
                    cursor: "pointer",
                    color: "#666",
                    fontSize: "0.8rem",
                  }}
                >
                  {showCurrentPassword ? "Hide" : "Show"}
                </button>
              </div>
            </FormGroup>
            <FormGroup>
              <Label htmlFor="newPassword">New Password</Label>
              <div style={{ position: "relative" }}>
                <Input
                  id="newPassword"
                  name="newPassword"
                  type={showNewPassword ? "text" : "password"}
                  value={passwordForm.newPassword}
                  onChange={handlePasswordChange}
                  placeholder="Enter new password"
                  required
                  minLength={6}
                  style={{ paddingRight: "60px" }}
                />
                <button
                  type="button"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                  style={{
                    position: "absolute",
                    right: "10px",
                    top: "50%",
                    transform: "translateY(-50%)",
                    background: "none",
                    border: "none",
                    cursor: "pointer",
                    color: "#666",
                    fontSize: "0.8rem",
                  }}
                >
                  {showNewPassword ? "Hide" : "Show"}
                </button>
              </div>
              <Label>
                {" "}
                Password must be at least 6 characters long, contain one
                uppercase letter and one special character
              </Label>
            </FormGroup>
            <FormGroup>
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <div style={{ position: "relative" }}>
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  value={passwordForm.confirmPassword}
                  onChange={handlePasswordChange}
                  placeholder="Confirm new password"
                  required
                  style={{ paddingRight: "60px" }}
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  style={{
                    position: "absolute",
                    right: "10px",
                    top: "50%",
                    transform: "translateY(-50%)",
                    background: "none",
                    border: "none",
                    cursor: "pointer",
                    color: "#666",
                    fontSize: "0.8rem",
                  }}
                >
                  {showConfirmPassword ? "Hide" : "Show"}
                </button>
              </div>
            </FormGroup>
            <ButtonsContainer>
              <Button
                type="button"
                onClick={() => setPasswordEditMode(false)}
                disabled={savingPassword}
              >
                Cancel
              </Button>
              <Button type="submit" variant="primary" disabled={savingPassword}>
                {savingPassword ? "Updating..." : "Update Password"}
              </Button>
            </ButtonsContainer>
          </form>
        )}
      </ProfileCard>
    </ProfileContainer>
  );
};
export default Profile;
