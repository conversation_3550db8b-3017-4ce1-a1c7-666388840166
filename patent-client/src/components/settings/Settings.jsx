import React, { useState } from "react";
import styled from "styled-components";
import {
  PageTitle,
  Card,
  Button,
  Input,
  FormGroup,
  Label,
  ErrorText,
} from "../../styles/StyledComponents";
import { useAuth } from "../../contexts/AuthContext";
const SettingsContainer = styled.div`
  width: 100%;
  margin-bottom: ${({ theme }) => theme.spacing.xl};
`;
const ProfileCard = styled(Card)`
  width: 100%;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const PasswordCard = styled(Card)`
  width: 100%;
`;
const SuccessMessage = styled.div`
  background-color: ${({ theme }) => theme.colors.success + "20"};
  color: ${({ theme }) => theme.colors.success};
  padding: ${({ theme }) => theme.spacing.md};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const Settings = () => {
  const { user, resetPassword } = useAuth();
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const handleResetPassword = (e) => {
    e.preventDefault();
    setError("");
    setSuccess("");
    if (!currentPassword || !newPassword || !confirmPassword) {
      setError("All fields are required");
      return;
    }
    if (newPassword !== confirmPassword) {
      setError("New passwords do not match");
      return;
    }
    if (newPassword.length < 6) {
      setError("Password must be at least 6 characters");
      return;
    }
    const success = resetPassword(currentPassword, newPassword);
    if (success) {
      setSuccess("Password changed successfully");
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
    } else {
      setError("Current password is incorrect");
    }
  };
  return (
    <SettingsContainer>
      <PageTitle>Account Settings</PageTitle>
      <ProfileCard>
        <h2>Admin Profile</h2>
        <p>Username: {user?.username}</p>
        <p>Email: {user?.email}</p>
      </ProfileCard>
      <PasswordCard>
        <h2>Reset Password</h2>
        {error && <ErrorText>{error}</ErrorText>}
        {success && <SuccessMessage>{success}</SuccessMessage>}
        <form onSubmit={handleResetPassword}>
          <FormGroup>
            <Label htmlFor="currentPassword">Current Password</Label>
            <Input
              id="currentPassword"
              type="password"
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
              placeholder="Enter current password"
            />
          </FormGroup>
          <FormGroup>
            <Label htmlFor="newPassword">New Password</Label>
            <Input
              id="newPassword"
              type="password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              placeholder="Enter new password"
            />
          </FormGroup>
          <FormGroup>
            <Label htmlFor="confirmPassword">Confirm New Password</Label>
            <Input
              id="confirmPassword"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="Confirm new password"
            />
          </FormGroup>
          <Button type="submit">Reset Password</Button>
        </form>
      </PasswordCard>
    </SettingsContainer>
  );
};
export default Settings;
