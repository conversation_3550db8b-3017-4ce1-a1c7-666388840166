import React, { useState, useEffect, useCallback } from "react";
import styled from "styled-components";
import { PageTitle, Card, SearchInput } from "../../styles/StyledComponents";
import * as bookService from "../../services/bookService";
import Loader from "../layout/Loader";
import { useAuth } from "../../contexts/AuthContext";
import {
  LocalShipping as ShippingIcon,
  CheckCircle as DeliveredIcon,
  Pending as PendingIcon,
  Settings as ProcessingIcon,
} from "@styled-icons/material";
const breakpoints = {
  mobile: "480px",
  tablet: "768px",
  desktop: "1024px",
};
const ShipmentHeader = styled.div`
  display: flex;
  flex-direction: column;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  @media (max-width: ${breakpoints.tablet}) {
    margin-top: 0px;
  }
  @media (min-width: ${breakpoints.tablet}) {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: ${({ theme }) => theme.spacing.lg};
  }
`;
const SearchContainer = styled.div`
  width: 100%;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  @media (min-width: ${breakpoints.tablet}) {
    margin-bottom: ${({ theme }) => theme.spacing.lg};
  }
`;
const StatusIcon = styled.div`
  color: ${({ theme, status }) =>
    status === "delivered"
      ? theme.colors.success
      : status === "shipped"
        ? theme.colors.primary
        : status === "processing"
          ? theme.colors.info
          : status === "pending"
            ? theme.colors.warning
            : theme.colors.warning};
  display: flex;
  align-items: center;
  svg {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    @media (min-width: ${breakpoints.tablet}) {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
  }
`;
const NoDataMessage = styled.div`
  text-align: center;
  margin: ${({ theme }) => theme.spacing.lg} 0;
  color: ${({ theme }) => theme.colors.text?.secondary || "#666"};
  padding: ${({ theme }) => theme.spacing.md};
`;
const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colors.error};
  padding: ${({ theme }) => theme.spacing.md};
  border: 1px solid ${({ theme }) => theme.colors.error};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin: ${({ theme }) => theme.spacing.md} 0;
`;
const TableContainer = styled.div`
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;

`;
const StyledTable = styled.table`
  width: 100%;
  min-width: 500px;
  border-collapse: collapse;
  th {
    text-align: left;
    padding: 8px 12px;
    border-bottom: 1px solid #e0e0e0;
    font-weight: 500;
    color: #333;
    background-color: #f9f9f9;
    white-space: nowrap;
    @media (min-width: ${breakpoints.tablet}) {
      padding: 12px 16px;
    }
  }
  td {
    padding: 8px 12px;
    border-bottom: 1px solid #e0e0e0;
    word-break: break-word;
    @media (min-width: ${breakpoints.tablet}) {
      padding: 12px 16px;
    }
  }
  tr:last-child td {
    border-bottom: none;
  }
  @media (max-width: ${breakpoints.mobile}) {
    th, td {
      padding: 6px 10px;
      font-size: 0.9rem;
    }
  }
`;
const TableView = styled.div`
  display: block; /* Always show table view */
`;
const CardList = styled.div`
  display: none; /* Always hide card list */
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.md};
  @media (min-width: ${breakpoints.tablet}) {
    display: none;
  }
`;
const ShipmentCard = styled(Card)`
  padding: ${({ theme }) => theme.spacing.md};
`;
const CardRow = styled.div`
  display: flex;
  justify-content: space-between;
  padding: ${({ theme }) => theme.spacing.xs} 0;
  &:not(:last-child) {
    border-bottom: 1px solid #f0f0f0;
  }
`;
const CardLabel = styled.span`
  font-weight: 500;
  color: #666;
`;
const CardValue = styled.span`
  text-align: right;
`;
const BookInfo = styled.div`
  .author {
    font-size: 0.85rem;
    color: #666;
    margin-top: 2px;
  }
  @media (max-width: ${breakpoints.mobile}) {
    .author {
      font-size: 0.8rem;
    }
  }
`;
const StatusBadge = styled.span`
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: ${({ status, theme }) =>
    status === "delivered"
      ? theme.colors.success + "20" || "#e6f7ed"
      : status === "shipped"
        ? theme.colors.primary + "20" || "#e3f2fd"
        : status === "processing"
          ? theme.colors.info + "20" || "#e1f5fe"
          : status === "pending"
            ? theme.colors.warning + "20" || "#fff8e1"
            : "#fff8e1"};
  color: ${({ status, theme }) =>
    status === "delivered"
      ? theme.colors.success || "#0d963c"
      : status === "shipped"
        ? theme.colors.primary || "#1976d2"
        : status === "processing"
          ? theme.colors.info || "#0288d1"
          : status === "pending"
            ? theme.colors.warning || "#f57c00"
            : "#f57c00"};
  @media (max-width: ${breakpoints.mobile}) {
    padding: 3px 6px;
    font-size: 0.7rem;
  }
`;
const StatusDisplay = styled.div`
  display: flex;
  align-items: center;
  @media (max-width: ${breakpoints.mobile}) {
    flex-wrap: nowrap;
  }
`;
const MyShipments = () => {
  const [books, setBooks] = useState([]);
  const [filteredBooks, setFilteredBooks] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useAuth();
  const fetchBooks = useCallback(async () => {
    try {
      setLoading(true);
      if (!user) {
        setError("You must be logged in to view your shipments");
        setLoading(false);
        return;
      }
      const response = await bookService.getMyBooks();
      let booksData = [];
      if (Array.isArray(response)) {
        booksData = response;
      } else if (response && response.books) {
        booksData = response.books;
      } else {
        booksData = [];
      }
      const hardcopyBooks = booksData.filter(
        (book) => book.type === "hardcopy",
      );
      setBooks(hardcopyBooks);
      setFilteredBooks(hardcopyBooks);
      setError(null);
    } catch (err) {
      console.error("Failed to fetch books:", err);
      setError("Failed to load shipment data. Please try again later.");
    } finally {
      setLoading(false);
    }
  }, [user]);
  useEffect(() => {
    fetchBooks();
  }, [fetchBooks]);
  useEffect(() => {
    if (books.length > 0 && searchTerm.trim() !== "") {
      const filtered = books.filter(
        (book) =>
          (book.title &&
            book.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (book.author &&
            book.author.toLowerCase().includes(searchTerm.toLowerCase())),
      );
      setFilteredBooks(filtered);
    } else {
      setFilteredBooks(books);
    }
  }, [books, searchTerm]);
  const formatDate = (dateString) => {
    try {
      if (!dateString) return "N/A";
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return "Invalid date";
      }
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      return "Error";
    }
  };
  const getStatusIcon = (status) => {
    switch (status) {
      case "delivered":
        return <DeliveredIcon />;
      case "shipped":
        return <ShippingIcon />;
      case "processing":
        return <ProcessingIcon />;
      case "pending":
      default:
        return <PendingIcon />;
    }
  };
  if (loading) {
    return <Loader section="shipments" />;
  }
  if (error) {
    return <ErrorMessage>{error}</ErrorMessage>;
  }
  return (
    <div>
      <ShipmentHeader>
        <PageTitle>My Shipments</PageTitle>
      </ShipmentHeader>
      <SearchContainer>
        <SearchInput
          placeholder="Search by book title or author..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </SearchContainer>
      {filteredBooks.length > 0 ? (
        <>
          <TableView>
            <Card>
              <TableContainer>
                <StyledTable>
                  <thead>
                    <tr>
                      <th>Book</th>
                      <th>Order Date</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredBooks.map((book) => (
                      <tr key={book._id}>
                        <td>
                          <BookInfo>
                            <strong>{book.title}</strong>
                            {book.author && (
                              <div className="author">by {book.author}</div>
                            )}
                          </BookInfo>
                        </td>
                        <td>{formatDate(book.createdAt)}</td>
                        <td>
                          <StatusDisplay>
                            <StatusIcon status={book.status || "pending"}>
                              {getStatusIcon(book.status)}
                            </StatusIcon>
                            <StatusBadge status={book.status || "pending"}>
                              {book.status
                                ? book.status.charAt(0).toUpperCase() +
                                  book.status.slice(1)
                                : "Pending"}
                            </StatusBadge>
                          </StatusDisplay>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </StyledTable>
              </TableContainer>
            </Card>
          </TableView>
          
          {/* CardList is kept in the code but will never be displayed */}
          
        </>
      ) : (
        <NoDataMessage>
          {searchTerm
            ? `No shipments found for "${searchTerm}". Try a different search.`
            : "You have no hardcopy book orders yet."}
        </NoDataMessage>
      )}
    </div>
  );
};
export default MyShipments;
