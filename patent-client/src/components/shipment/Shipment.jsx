import React, { useState, useEffect, useCallback } from "react";
import styled from "styled-components";
import { <PERSON>T<PERSON><PERSON>, Card, SearchInput } from "../../styles/StyledComponents";
import * as bookService from "../../services/bookService";
import Loader from "../layout/Loader";
import { useToast } from "../../contexts/ToastContext";
import { useAuth } from "../../contexts/AuthContext";
import {
  LocalShipping as ShippingIcon,
  CheckCircle as DeliveredIcon,
  Settings as ProcessingIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  Close as CloseIcon,
} from "@styled-icons/material";
const ShipmentHeader = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing.sm};
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    margin-top: 0px;
  }
`;
const SearchContainer = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.md};
  position: relative;
  svg {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 18px;
    height: 18px;
    color: ${({ theme }) => theme.colors.textSecondary};
  }
  input {
    padding-left: 40px;
  }
`;
const ActionButton = styled.button`
  background-color: transparent;
  border: none;
  color: ${({ theme, variant }) =>
    variant === "in process"
      ? theme.colors.info
      : variant === "shipped"
        ? theme.colors.primary
        : variant === "delivered"
          ? theme.colors.success
          : theme.colors.primary};
  cursor: pointer;
  padding: ${({ theme }) => theme.spacing.xs};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
  }
  &:active {
    transform: translateY(0);
  }
  svg {
    width: 20px;
    height: 20px;
  }
`;
const NoDataMessage = styled.div`
  text-align: center;
  margin: ${({ theme }) => theme.spacing.xl} 0;
  color: ${({ theme }) => theme.colors.textSecondary};
  padding: ${({ theme }) => theme.spacing.lg};
  background-color: ${({ theme }) => theme.colors.light};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;
const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colors.error};
  padding: ${({ theme }) => theme.spacing.md};
  border: 1px solid ${({ theme }) => theme.colors.error};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin: ${({ theme }) => theme.spacing.lg} 0;
  background-color: ${({ theme }) => theme.colors.error + "10"};
`;
const TableContainer = styled.div`
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  &::-webkit-scrollbar {
    height: 6px;
  }
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: #aaa;
  }
  @media (max-width: 768px) {
    border-radius: ${({ theme }) => theme.borderRadius.sm};
    margin-bottom: ${({ theme }) => theme.spacing.md};
  }
`;
const StyledTable = styled.table`
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  min-width: 650px;
  th {
    text-align: left;
    padding: 14px 16px;
    border-bottom: 1px solid #e0e0e0;
    font-weight: 600;
    color: #333;
    background-color: #f9f9f9;
    position: sticky;
    top: 0;
    z-index: 10;
    white-space: nowrap;
    &:first-child {
      border-top-left-radius: ${({ theme }) => theme.borderRadius.md};
    }
    &:last-child {
      border-top-right-radius: ${({ theme }) => theme.borderRadius.md};
    }
  }
  td {
    padding: 14px 16px;
    border-bottom: 1px solid #e0e0e0;
    vertical-align: middle;
  }
  tr:last-child td {
    border-bottom: none;
    &:first-child {
      border-bottom-left-radius: ${({ theme }) => theme.borderRadius.md};
    }
    &:last-child {
      border-bottom-right-radius: ${({ theme }) => theme.borderRadius.md};
    }
  }
  tr:hover {
    background-color: #f5f5f5;
  }
  @media (max-width: 768px) {
    th, td {
      padding: 10px 12px;
      font-size: 0.9rem;
    }
  }
  @media (max-width: 576px) {
    th, td {
      padding: 8px 10px;
      font-size: 0.85rem;
    }
  }
`;
const StatusBadge = styled.span`
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
  background-color: ${({ status, theme }) =>
    status === "delivered"
      ? theme.colors.success + "20"
      : status === "shipped"
        ? theme.colors.primary + "20"
        : status === "in process"
          ? theme.colors.info + "20"
          : theme.colors.info + "20"};
  color: ${({ status, theme }) =>
    status === "delivered"
      ? theme.colors.success
      : status === "shipped"
        ? theme.colors.primary
        : status === "in process"
          ? theme.colors.info
          : theme.colors.info};
  svg {
    width: 14px;
    height: 14px;
    margin-right: 4px;
  }
`;
const StatusSelect = styled.select`
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #ccc;
  background-color: white;
  font-size: 0.875rem;
  color: #333;
  cursor: pointer;
  width: 100%;
  max-width: 140px;
  transition: all 0.2s ease;
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary}20;
  }
  @media (max-width: 576px) {
    max-width: 100px;
    padding: 6px 8px;
    font-size: 0.8rem;
  }
`;
const FilterContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  @media (max-width: 576px) {
    display: ${({ isMobileFilterOpen }) =>
      isMobileFilterOpen ? "flex" : "none"};
    flex-direction: column;
    position: absolute;
    top: 60px;
    left: 0;
    right: 0;
    background: white;
    padding: 12px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 100;
  }
`;
const FilterButton = styled.button`
  padding: 8px 14px;
  border-radius: 6px;
  border: 1px solid
    ${({ active, theme }) => (active ? theme.colors.primary : "#ccc")};
  background-color: ${({ active, theme }) =>
    active ? theme.colors.primary : "white"};
  color: ${({ active }) => (active ? "white" : "#333")};
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  &:hover {
    background-color: ${({ active, theme }) =>
      active ? theme.colors.primary : "#f5f5f5"};
    transform: translateY(-1px);
  }
  &:active {
    transform: translateY(0);
  }
  @media (max-width: 576px) {
    width: 100%;
    justify-content: center;
  }
`;
const BookInfo = styled.div`
  display: flex;
  flex-direction: column;
  .title {
    font-weight: 600;
    margin-bottom: 4px;
  }
  .author {
    font-size: 0.85rem;
    color: #666;
  }
`;
const CustomerInfo = styled.div`
  display: flex;
  flex-direction: column;
  .name {
    font-weight: 500;
    margin-bottom: 2px;
  }
  .email {
    font-size: 0.85rem;
    color: #666;
  }
`;
const MobileFilterToggle = styled.button`
  display: none;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #ccc;
  background-color: white;
  color: #333;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  svg {
    width: 18px;
    height: 18px;
    margin-right: 6px;
  }
  @media (max-width: 576px) {
    display: flex;
  }
`;
const FilterAndSearchContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing.md};
  position: relative;
  width: 100%;
  @media (max-width: 768px) {
    flex-direction: column;
  }
`;
const CardList = styled.div`
  display: none;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  @media (max-width: 768px) {
    display: none; /* Keep it hidden on mobile */
  }
`;
const MobileCard = styled(Card)`
  padding: ${({ theme }) => theme.spacing.md};
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;
const MobileCardRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: ${({ alignTop }) => (alignTop ? "flex-start" : "center")};
  width: 100%;
  .label {
    font-weight: 500;
    color: #666;
    font-size: 0.85rem;
  }
  .value {
    text-align: right;
    font-weight: ${({ isBold }) => (isBold ? 600 : 400)};
  }
`;
const TableView = styled.div`
  @media (max-width: 768px) {
    display: block; /* Always show table view */
  }
`;
const Shipment = () => {
  const [books, setBooks] = useState([]);
  const [filteredBooks, setFilteredBooks] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [statusFilter, setStatusFilter] = useState("");
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);
  const { addToast } = useToast();
  const { user } = useAuth();
  const fetchBooks = useCallback(async () => {
    try {
      setLoading(true);
      if (!user || !user.isAdmin) {
        setError("You are not authorized to view shipment data");
        setLoading(false);
        return;
      }
      const data = await bookService.getHardcopyBooksByStatus(statusFilter);
      setBooks(data);
      setFilteredBooks(data);
      setError(null);
    } catch (err) {
      console.error("Failed to fetch books:", err);
      setError("Failed to load shipment data. Please try again later.");
    } finally {
      setLoading(false);
    }
  }, [user, statusFilter]);
  useEffect(() => {
    fetchBooks();
  }, [fetchBooks, statusFilter]);
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isMobileFilterOpen && !event.target.closest(".filter-container")) {
        setIsMobileFilterOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMobileFilterOpen]);
  useEffect(() => {
    if (books.length > 0) {
      const filtered = books.filter(
        (book) =>
          (book.title &&
            book.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (book.author &&
            book.author.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (book.userId?.name &&
            book.userId.name
              .toLowerCase()
              .includes(searchTerm.toLowerCase())) ||
          (book.userId?.email &&
            book.userId.email
              .toLowerCase()
              .includes(searchTerm.toLowerCase())) ||
          (book.userId?.firstName &&
            book.userId.firstName
              .toLowerCase()
              .includes(searchTerm.toLowerCase())) ||
          (book.userId?.lastName &&
            book.userId.lastName
              .toLowerCase()
              .includes(searchTerm.toLowerCase())),
      );
      setFilteredBooks(filtered);
    }
  }, [books, searchTerm]);
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };
  const handleStatusChange = async (bookId, newStatus) => {
    try {
      await bookService.updateBookStatus(bookId, newStatus);
      setBooks((prevBooks) =>
        prevBooks.map((book) =>
          book._id === bookId ? { ...book, status: newStatus } : book,
        ),
      );
      addToast(`Book status updated to ${newStatus} successfully!`, "success");
    } catch (err) {
      console.error("Failed to update book status:", err);
      addToast("Failed to update book status. Please try again.", "error");
    }
  };
  const getStatusIcon = (status) => {
    switch (status) {
      case "delivered":
        return <DeliveredIcon />;
      case "shipped":
        return <ShippingIcon />;
      case "in process":
      default:
        return <ProcessingIcon />;
    }
  };
  const getCustomerName = (user) => {
    if (!user) return "Unknown User";
    if (user.name) return user.name;
    if (user.firstName || user.lastName) {
      return `${user.firstName || ""} ${user.lastName || ""}`.trim();
    }
    return user.username || user.email || "User";
  };
  if (loading) {
    return <Loader section="shipments" />;
  }
  if (error) {
    return <ErrorMessage>{error}</ErrorMessage>;
  }
  return (
    <div>
      <ShipmentHeader>
        <PageTitle>Shipment Management</PageTitle>
      </ShipmentHeader>
      <FilterAndSearchContainer>
        <div
          className="filter-container"
          style={{ position: "relative", flex: 1 }}
        >
          <MobileFilterToggle
            onClick={() => setIsMobileFilterOpen(!isMobileFilterOpen)}
          >
            {isMobileFilterOpen ? <CloseIcon /> : <FilterIcon />}
            Filters
          </MobileFilterToggle>
          <FilterContainer isMobileFilterOpen={isMobileFilterOpen}>
            <FilterButton
              active={statusFilter === ""}
              onClick={() => {
                setStatusFilter("");
                setIsMobileFilterOpen(false);
              }}
            >
              All
            </FilterButton>
            <FilterButton
              active={statusFilter === "in process"}
              onClick={() => {
                setStatusFilter("in process");
                setIsMobileFilterOpen(false);
              }}
            >
              In Process
            </FilterButton>
            <FilterButton
              active={statusFilter === "shipped"}
              onClick={() => {
                setStatusFilter("shipped");
                setIsMobileFilterOpen(false);
              }}
            >
              Shipped
            </FilterButton>
            <FilterButton
              active={statusFilter === "delivered"}
              onClick={() => {
                setStatusFilter("delivered");
                setIsMobileFilterOpen(false);
              }}
            >
              Delivered
            </FilterButton>
          </FilterContainer>
        </div>
        <SearchContainer style={{ flex: 2 }}>
          <SearchIcon />
          <SearchInput
            placeholder="Search by book title, author, or customer..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </SearchContainer>
      </FilterAndSearchContainer>
      {filteredBooks.length > 0 ? (
        <>
          <TableView>
            <TableContainer>
              <StyledTable>
                <thead>
                  <tr>
                    <th>Book</th>
                    <th>Customer</th>
                    <th>Order Date</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredBooks.map((book) => (
                    <tr key={book._id}>
                      <td>
                        <BookInfo>
                          <div className="title">{book.title}</div>
                          {book.author && (
                            <div className="author">by {book.author}</div>
                          )}
                        </BookInfo>
                      </td>
                      <td>
                        <CustomerInfo>
                          <div className="name">
                            {book.user?.username || "Unknown User"}
                          </div>
                          <div className="email">
                            {book.user?.email || "N/A"}
                          </div>
                        </CustomerInfo>
                      </td>
                      <td>{formatDate(book.createdAt)}</td>
                      <td>
                        <StatusBadge status={book.status || "in process"}>
                          {getStatusIcon(book.status)}
                          {book.status || "In Process"}
                        </StatusBadge>
                      </td>
                      <td>
                        {user && user.isAdmin ? (
                          <StatusSelect
                            value={book.status || "in process"}
                            onChange={(e) =>
                              handleStatusChange(book._id, e.target.value)
                            }
                          >
                            <option value="in process">In Process</option>
                            <option value="shipped">Shipped</option>
                            <option value="delivered">Delivered</option>
                          </StatusSelect>
                        ) : (
                          <ActionButton
                            variant={book.status || "in process"}
                            title={`Status: ${book.status || "In Process"}`}
                          >
                            {getStatusIcon(book.status)}
                          </ActionButton>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </StyledTable>
            </TableContainer>
          </TableView>
          {}
          <CardList>
            {filteredBooks.map((book) => (
              <MobileCard key={book._id}>
                <MobileCardRow alignTop isBold>
                  <BookInfo>
                    <div className="title">{book.title}</div>
                    {book.author && (
                      <div className="author">by {book.author}</div>
                    )}
                  </BookInfo>
                  <StatusBadge status={book.status || "in process"}>
                    {getStatusIcon(book.status)}
                    {book.status || "In Process"}
                  </StatusBadge>
                </MobileCardRow>
                <MobileCardRow>
                  <div className="label">Customer:</div>
                  <div className="value">{getCustomerName(book.userId)}</div>
                </MobileCardRow>
                <MobileCardRow>
                  <div className="label">Email:</div>
                  <div className="value">{book.userId?.email || "N/A"}</div>
                </MobileCardRow>
                <MobileCardRow>
                  <div className="label">Order Date:</div>
                  <div className="value">{formatDate(book.createdAt)}</div>
                </MobileCardRow>
                <MobileCardRow>
                  <div className="label">Update Status:</div>
                  <div className="value">
                    {user && user.isAdmin ? (
                      <StatusSelect
                        value={book.status || "in process"}
                        onChange={(e) =>
                          handleStatusChange(book._id, e.target.value)
                        }
                      >
                        <option value="in process">In Process</option>
                        <option value="shipped">Shipped</option>
                        <option value="delivered">Delivered</option>
                      </StatusSelect>
                    ) : (
                      <ActionButton
                        variant={book.status || "in process"}
                        title={`Status: ${book.status || "In Process"}`}
                      >
                        {getStatusIcon(book.status)}
                      </ActionButton>
                    )}
                  </div>
                </MobileCardRow>
              </MobileCard>
            ))}
          </CardList>
        </>
      ) : (
        <NoDataMessage>
          {searchTerm
            ? `No shipments found for "${searchTerm}". Try a different search.`
            : statusFilter
              ? `No ${statusFilter} shipments available.`
              : "No shipments available."}
        </NoDataMessage>
      )}
    </div>
  );
};
export default Shipment;
