import React, { useState, useEffect } from "react";
import styled from "styled-components";
import StripeCheckout from "./StripeCheckout";
import { getUserProfile } from "../../services/userService";
const CheckoutModal = ({
  isOpen,
  onClose,
  clientSecret,
  onSuccess,
  onError,
  bookTitle,
  amount,
  cartItems,
  requiresShipping = false,
}) => {
  const [title, setTitle] = useState("Purchase Book");
  const [subtitle, setSubtitle] = useState(
    "Complete your purchase to access this book",
  );
  const [itemName, setItemName] = useState(bookTitle || "Item");
  const [userProfile, setUserProfile] = useState(null);
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const profile = await getUserProfile();
        setUserProfile(profile);
      } catch (error) {
        console.error("Error fetching user profile:", error);
      }
    };

    if (isOpen) {
      fetchUserProfile();
    }
  }, [isOpen]);

  useEffect(() => {
    if (cartItems && cartItems.length > 0) {
      const onlyCourses = cartItems.every((item) => item.type === "course");
      const onlyBooks = cartItems.every(
        (item) => !item.type || item.type !== "course",
      );
      if (onlyCourses) {
        setTitle("Purchase Course");
        setSubtitle("Complete your purchase to access this course");
        setItemName(cartItems.length === 1 ? cartItems[0].title : "Courses");
      } else if (onlyBooks) {
        setTitle("Purchase Book");
        setSubtitle("Complete your purchase to access this book");
        setItemName(cartItems.length === 1 ? cartItems[0].title : "Books");
      } else {
        setTitle("Complete Your Purchase");
        setSubtitle("Complete your purchase to access your items");
        setItemName("Items");
      }
    } else if (bookTitle) {
      setItemName(bookTitle);
    }
  }, [cartItems, bookTitle]);
  if (!isOpen) return null;
  return (
    <ModalOverlay>
      <ModalContainer>
        <ModalHeader>
          <h3>{title}</h3>
          <CloseButton onClick={onClose}>×</CloseButton>
        </ModalHeader>
        <ModalBody>
          <BookInfo>
            <h4>{itemName}</h4>
            <p>{subtitle}</p>
          </BookInfo>
          <StripeCheckout
            clientSecret={clientSecret}
            onSuccess={onSuccess}
            onError={onError}
            amount={amount}
            userInfo={userProfile}
            saveCard={true}
            customerId={userProfile?.stripeCustomerId}
            requiresShipping={requiresShipping}
          />
        </ModalBody>
      </ModalContainer>
    </ModalOverlay>
  );
};
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 10px;

  @media (max-width: 768px) {
    align-items: flex-start;
    padding: 0;
  }
`;

const ModalContainer = styled.div`
  background-color: white;
  border-radius: 8px;
  width: 95%;
  max-width: 500px;
  max-height: 90vh;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  @media (max-width: 768px) {
    width: 100%;
    max-width: 100%;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
  }
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e6e6e6;
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 10;

  h3 {
    margin: 0;
    color: ${({ theme }) => theme.colors.primary || '#009F9F'};
    font-size: 1.25rem;

    @media (max-width: 480px) {
      font-size: 1.1rem;
    }
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #333;
  }

  @media (max-width: 480px) {
    font-size: 20px;
  }
`;

const ModalBody = styled.div`
  padding: 20px;
  flex: 1;
  overflow-y: auto;

  @media (max-width: 480px) {
    padding: 15px;
  }
`;

const BookInfo = styled.div`
  margin-bottom: 20px;
  text-align: center;

  h4 {
    margin: 0 0 10px;
    font-size: 1.1rem;
    word-break: break-word;

    @media (max-width: 480px) {
      font-size: 1rem;
    }
  }

  p {
    color: #666;
    margin: 0;
    font-size: 0.9rem;

    @media (max-width: 480px) {
      font-size: 0.85rem;
    }
  }
`;
export default CheckoutModal;
