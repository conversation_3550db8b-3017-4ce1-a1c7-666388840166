import React, { useState, useEffect } from "react";
import {
  Elements,
  CardNumberElement,
  CardExpiryElement,
  CardCvcElement,
  useStripe,
  useElements,
} from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import styled from "styled-components";
import { Button } from "../../styles/StyledComponents";
import { getUserProfile } from "../../services/userService";
import apiRequest from "../../services/api";
const STRIPE_PUBLISHABLE_KEY =
  "pk_test_51RGxXXL5S1uufvuOySp2kUxHbl8Mk8cPXCSWfaFMpG8sqvQRhi9ZpOEFhxBlw1UrjOjDhtQYrggkiqpeOhgqkdOa00If77G3kj";
const stripePromise = loadStripe(STRIPE_PUBLISHABLE_KEY);
const CheckoutForm = ({
  clientSecret,
  setupIntentSecret,
  onSuccess,
  onError,
  amount,
  saveCard,
  userInfo,
  requiresShipping = false,
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [savedCards, setSavedCards] = useState([]);
  const [selectedCard, setSelectedCard] = useState(null);
  const [shouldSaveCard, setShouldSaveCard] = useState(saveCard || false);
  const [deletingCard, setDeletingCard] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [cardToDelete, setCardToDelete] = useState(null);
  const [country, setCountry] = useState("US");

  // Shipping address fields
  const [street, setStreet] = useState("");
  const [city, setCity] = useState("");
  const [state, setState] = useState("");
  const [zipCode, setZipCode] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  useEffect(() => {
    const fetchSavedCards = async () => {
      try {
        const userProfile = await getUserProfile();

        if (!userProfile || !userProfile.stripeCustomerId) {
          console.log("User has no saved payment methods");
          return;
        }

        console.log(
          "Fetching saved cards for user with stripeCustomerId:",
          userProfile.stripeCustomerId
        );
        const cards = await apiRequest("/stripe/saved-cards");
        console.log("Saved cards response:", cards);

        if (cards && cards.data) {
          console.log("Setting saved cards:", cards.data);
          setSavedCards(cards.data);

          if (cards.data.length > 0) {
            console.log("Selecting first card:", cards.data[0]);
            setSelectedCard(cards.data[0]);
          } else {
            console.log("No saved cards found in the response data");
          }
        } else {
          console.log("No data property found in the cards response:", cards);
        }
      } catch (err) {
        console.error("Error fetching saved cards:", err);
      }
    };

    if (userInfo) {
      fetchSavedCards();
    }
  }, [userInfo]);
  const handleSubmit = async (event) => {
    event.preventDefault();
    if (!stripe || !elements) {
      return;
    }
    setProcessing(true);
    setError(null);
    try {
      if (selectedCard) {
        const { error, paymentIntent } = await stripe.confirmCardPayment(
          clientSecret,
          {
            payment_method: selectedCard.id,
          }
        );
        if (error) {
          setError(`Payment failed: ${error.message}`);
          onError(error.message);
          setProcessing(false);
          return;
        }
        if (paymentIntent.status === "succeeded") {
          await processPayment(paymentIntent);
        }
      } else {
        const cardNumberElement = elements.getElement(CardNumberElement);
        const cardExpiryElement = elements.getElement(CardExpiryElement);
        const cardCvcElement = elements.getElement(CardCvcElement);

        if (!cardNumberElement || !cardExpiryElement || !cardCvcElement) {
          setError("Please fill in all card details");
          setProcessing(false);
          return;
        }

        const paymentMethod = {
          card: cardNumberElement,
          billing_details: {
            name: userInfo?.name || "Customer Name",
            email: userInfo?.email,
            address: {
              ...userInfo?.address,
              country: country,
            },
          },
        };
        if (shouldSaveCard && setupIntentSecret) {
          const { setupIntent, error: setupError } =
            await stripe.confirmCardSetup(setupIntentSecret, {
              payment_method: paymentMethod,
            });
          if (setupError) {
            setError(`Card setup failed: ${setupError.message}`);
            onError(setupError.message);
            setProcessing(false);
            return;
          }
          const { error, paymentIntent } = await stripe.confirmCardPayment(
            clientSecret,
            {
              payment_method: setupIntent.payment_method,
            }
          );
          if (error) {
            setError(`Payment failed: ${error.message}`);
            onError(error.message);
          } else if (paymentIntent.status === "succeeded") {
            await processPayment(paymentIntent, setupIntent.payment_method);
          }
        } else {
          // Prepare shipping address if required
          let shippingAddressData = null;
          if (requiresShipping) {
            shippingAddressData = {
              street,
              city,
              state,
              zipCode,
              country,
              phoneNumber
            };
          }

          const { error, paymentIntent } = await stripe.confirmCardPayment(
            clientSecret,
            {
              payment_method: paymentMethod,
              ...(requiresShipping && shippingAddressData ? {
                shipping: {
                  address: {
                    line1: street,
                    city: city,
                    state: state,
                    postal_code: zipCode,
                    country: country
                  },
                  phone: phoneNumber,
                  name: userInfo?.name || "Customer Name"
                }
              } : {}),
            }
          );
          if (error) {
            setError(`Payment failed: ${error.message}`);
            onError(error.message);
          } else if (paymentIntent.status === "succeeded") {
            await processPayment(paymentIntent);
          }
        }
      }
    } catch (err) {
      setError("An unexpected error occurred.");
      onError(err.message);
    } finally {
      setProcessing(false);
    }
  };
  const processPayment = async (paymentIntent, savedPaymentMethod = null) => {
    try {
      // Prepare shipping address data if required
      let shippingAddressData = null;
      if (requiresShipping) {
        shippingAddressData = {
          street,
          city,
          state,
          zipCode,
          country,
          phoneNumber
        };
      }

      await apiRequest("/stripe/client-payment-confirmation", "POST", {
        paymentIntentId: paymentIntent.id,
        status: paymentIntent.status,
        metadata: {
          ...paymentIntent.metadata,
          ...(requiresShipping ? {
            requiresShipping: "true",
            shippingAddress: JSON.stringify(shippingAddressData)
          } : {})
        },
        savedPaymentMethod: savedPaymentMethod,
      });

      onSuccess(paymentIntent);
    } catch (error) {
      console.error("Error processing payment:", error);

      onSuccess(paymentIntent);
    }
  };

  const openDeleteModal = (card, event) => {
    event.preventDefault(); // Prevent default action
    event.stopPropagation(); // Prevent card selection when clicking delete
    setCardToDelete(card);
    setShowDeleteModal(true);
  };

  const closeDeleteModal = () => {
    setShowDeleteModal(false);
    setCardToDelete(null);
  };

  const confirmDeleteCard = async () => {
    if (!cardToDelete) return;

    try {
      setDeletingCard(true);
      setError(null);

      const response = await apiRequest("/stripe/delete-card", "POST", {
        paymentMethodId: cardToDelete.id,
      });

      if (response && response.success) {
        // Remove the card from the list
        setSavedCards(savedCards.filter((card) => card.id !== cardToDelete.id));

        // If the deleted card was selected, clear the selection
        if (selectedCard && selectedCard.id === cardToDelete.id) {
          setSelectedCard(null);
        }

        console.log("Card deleted successfully:", response.message);
      } else {
        setError("Failed to delete card. Please try again.");
        console.error("Failed to delete card:", response);
      }
    } catch (err) {
      setError(`Error deleting card: ${err.message}`);
      console.error("Error deleting card:", err);
    } finally {
      setDeletingCard(false);
      closeDeleteModal();
    }
  };
  return (
    <Form onSubmit={handleSubmit}>
      {/* Delete Confirmation Modal */}
      {showDeleteModal && cardToDelete && (
        <DeleteConfirmationModal
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            if (e.target === e.currentTarget) {
              closeDeleteModal();
            }
          }}
        >
          <ModalContent
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
          >
            <h4>Delete Payment Method</h4>
            <p>
              Are you sure you want to delete this card?
              <br />
              <strong>{cardToDelete.card.brand.toUpperCase()}</strong> ending in{" "}
              {cardToDelete.card.last4}
            </p>
            <ModalButtonContainer>
              <CancelButton
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  closeDeleteModal();
                }}
                disabled={deletingCard}
                type="button"
              >
                Cancel
              </CancelButton>
              <ConfirmButton
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  confirmDeleteCard();
                }}
                disabled={deletingCard}
                type="button"
              >
                {deletingCard ? "Deleting..." : "Delete"}
              </ConfirmButton>
            </ModalButtonContainer>
          </ModalContent>
        </DeleteConfirmationModal>
      )}

      {savedCards.length > 0 && (
        <SavedCardsContainer>
          <h4>Your Saved Payment Methods</h4>
          <p style={{ marginBottom: "10px", color: "#666" }}>
            Select a saved card or enter a new one
          </p>
          {savedCards.map((card) => (
            <CardOption
              key={card.id}
              onClick={() => setSelectedCard(card)}
              selected={selectedCard && selectedCard.id === card.id}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <div>
                  <strong>{card.card.brand.toUpperCase()}</strong> ending in{" "}
                  {card.card.last4}
                </div>
                <div style={{ display: "flex", alignItems: "center" }}>
                  <div style={{ marginRight: "15px" }}>
                    Expires: {card.card.exp_month}/{card.card.exp_year}
                  </div>
                  <DeleteButton
                    onClick={(e) => {
                      e.preventDefault();
                      openDeleteModal(card, e);
                    }}
                    disabled={deletingCard}
                    type="button"
                  >
                    {deletingCard ? "..." : "×"}
                  </DeleteButton>
                </div>
              </div>
              {selectedCard && selectedCard.id === card.id && (
                <div
                  style={{
                    color: "#007bff",
                    marginTop: "5px",
                    fontWeight: "bold",
                  }}
                >
                  ✓ Selected
                </div>
              )}
            </CardOption>
          ))}
          <CardOption
            onClick={() => setSelectedCard(null)}
            selected={!selectedCard}
          >
            <div style={{ display: "flex", alignItems: "center" }}>
              <div>Use a new card</div>
              {!selectedCard && (
                <div
                  style={{
                    color: "#007bff",
                    marginLeft: "10px",
                    fontWeight: "bold",
                  }}
                >
                  ✓ Selected
                </div>
              )}
            </div>
          </CardOption>
        </SavedCardsContainer>
      )}
      {!selectedCard && (
        <>
          <CardFormContainer>
            <CardTitle>
              <CardIcon>💳</CardIcon> Card
            </CardTitle>

            <CardFieldLabel>Card number</CardFieldLabel>
            <CardInputContainer>
              <CardNumberElement
                options={{
                  style: {
                    base: {
                      fontSize: "16px",
                      color: "#424770",
                      "::placeholder": {
                        color: "#aab7c4",
                      },
                    },
                    invalid: {
                      color: "#9e2146",
                    },
                  },
                }}
              />
            </CardInputContainer>

            <CardFieldsRow>
              <CardFieldColumn>
                <CardFieldLabel>Expiration date</CardFieldLabel>
                <CardInputContainer>
                  <CardExpiryElement
                    options={{
                      style: {
                        base: {
                          fontSize: "16px",
                          color: "#424770",
                          "::placeholder": {
                            color: "#aab7c4",
                          },
                        },
                        invalid: {
                          color: "#9e2146",
                        },
                      },
                    }}
                  />
                </CardInputContainer>
              </CardFieldColumn>

              <CardFieldColumn>
                <CardFieldLabel>Security code</CardFieldLabel>
                <CardInputContainer>
                  <CardCvcElement
                    options={{
                      style: {
                        base: {
                          fontSize: "16px",
                          color: "#424770",
                          "::placeholder": {
                            color: "#aab7c4",
                          },
                        },
                        invalid: {
                          color: "#9e2146",
                        },
                      },
                    }}
                  />
                </CardInputContainer>
              </CardFieldColumn>
            </CardFieldsRow>

            <CardFieldLabel>Country</CardFieldLabel>
            <CountrySelect
              value={country}
              onChange={(e) => setCountry(e.target.value)}
            >
              <option value="US">United States</option>
              <option value="CA">Canada</option>
              <option value="GB">United Kingdom</option>
              <option value="AU">Australia</option>
              <option value="DE">Germany</option>
              <option value="FR">France</option>
              <option value="PK">Pakistan</option>
              <option value="IN">India</option>
              <option value="CN">China</option>
              <option value="JP">Japan</option>
            </CountrySelect>

            {/* Shipping Address Fields (only shown for hardcopy books) */}
            {requiresShipping && (
              <ShippingAddressContainer>
                <ShippingTitle>
                  <ShippingIcon>📦</ShippingIcon> Shipping Information
                </ShippingTitle>

                <CardFieldLabel>Street Address</CardFieldLabel>
                <ShippingInput
                  type="text"
                  value={street}
                  onChange={(e) => setStreet(e.target.value)}
                  placeholder="123 Main St"
                  required
                />

                <CardFieldsRow>
                  <CardFieldColumn>
                    <CardFieldLabel>City</CardFieldLabel>
                    <ShippingInput
                      type="text"
                      value={city}
                      onChange={(e) => setCity(e.target.value)}
                      placeholder="New York"
                      required
                    />
                  </CardFieldColumn>

                  <CardFieldColumn>
                    <CardFieldLabel>State/Province</CardFieldLabel>
                    <ShippingInput
                      type="text"
                      value={state}
                      onChange={(e) => setState(e.target.value)}
                      placeholder="NY"
                      required
                    />
                  </CardFieldColumn>
                </CardFieldsRow>

                <CardFieldsRow>
                  <CardFieldColumn>
                    <CardFieldLabel>Zip/Postal Code</CardFieldLabel>
                    <ShippingInput
                      type="text"
                      value={zipCode}
                      onChange={(e) => setZipCode(e.target.value)}
                      placeholder="10001"
                      required
                    />
                  </CardFieldColumn>

                  <CardFieldColumn>
                    <CardFieldLabel>Phone Number</CardFieldLabel>
                    <ShippingInput
                      type="tel"
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                      placeholder="+****************"
                      required
                    />
                  </CardFieldColumn>
                </CardFieldsRow>
              </ShippingAddressContainer>
            )}

            <div
              style={{
                marginTop: "15px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <input
                type="checkbox"
                id="save-card"
                checked={shouldSaveCard}
                onChange={(e) => setShouldSaveCard(e.target.checked)}
                style={{ marginRight: "10px" }}
              />
              <label htmlFor="save-card">
                Save this card for future purchases
              </label>
            </div>
          </CardFormContainer>
        </>
      )}
      {error && <ErrorMessage>{error}</ErrorMessage>}
      <TotalAmount>Total: ${amount}</TotalAmount>
      <PayButton
        type="submit"
        disabled={!stripe || processing}
        variant="primary"
      >
        {processing ? "Processing..." : "Pay Now"}
      </PayButton>
    </Form>
  );
};
const StripeCheckout = ({
  clientSecret,
  setupIntentSecret: initialSetupIntentSecret,
  onSuccess,
  onError,
  amount,
  saveCard,
  userInfo,
  requiresShipping = false,
}) => {
  const [setupIntentSecret, setSetupIntentSecret] = useState(
    initialSetupIntentSecret
  );

  useEffect(() => {
    const fetchSetupIntent = async () => {
      if (saveCard && !initialSetupIntentSecret) {
        try {
          console.log("Fetching setup intent for saving card");
          const response = await apiRequest("/stripe/setup-intent", "POST");
          if (response && response.clientSecret) {
            console.log("Received setup intent secret");
            setSetupIntentSecret(response.clientSecret);
          }
        } catch (error) {
          console.error("Error fetching setup intent:", error);
        }
      }
    };

    fetchSetupIntent();
  }, [saveCard, initialSetupIntentSecret]);

  return (
    <Elements stripe={stripePromise}>
      <CheckoutForm
        clientSecret={clientSecret}
        setupIntentSecret={setupIntentSecret || initialSetupIntentSecret}
        onSuccess={onSuccess}
        onError={onError}
        amount={amount}
        saveCard={saveCard}
        userInfo={userInfo}
        requiresShipping={requiresShipping}
      />
    </Elements>
  );
};
const Form = styled.form`
  width: 100%;
  padding: 0;

  @media (max-width: 480px) {
    padding: 0;
  }
`;
const SavedCardsContainer = styled.div`
  margin-bottom: 20px;

  h4 {
    margin: 0 0 10px;
    font-size: 16px;

    @media (max-width: 480px) {
      font-size: 15px;
    }
  }

  @media (max-width: 480px) {
    margin-bottom: 15px;
  }
`;
const CardOption = styled.div`
  padding: 10px;
  border: 1px solid ${({ selected }) => (selected ? "#007bff" : "#e6e6e6")};
  border-radius: 4px;
  margin-bottom: 10px;
  cursor: pointer;
  background-color: ${({ selected }) => (selected ? "#f8f9fa" : "white")};

  &:hover {
    background-color: #f8f9fa;
  }

  @media (max-width: 480px) {
    padding: 8px;
    font-size: 14px;
    display: flex;
    flex-direction: column;
  }
`;
const ErrorMessage = styled.div`
  color: #e25950;
  margin: 10px 0;
  font-size: 14px;
`;
const TotalAmount = styled.div`
  margin: 15px 0;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;

  @media (max-width: 480px) {
    margin: 10px 0 15px;
    font-size: 16px;
    padding: 12px;
  }
`;
const PayButton = styled(Button)`
  width: 100%;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 600;
  margin-top: 10px;

  @media (max-width: 480px) {
    padding: 14px 20px;
    font-size: 16px;
    margin-top: 15px;
    position: sticky;
    bottom: 0;
    z-index: 5;
  }
`;

const DeleteButton = styled.button`
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  font-size: 16px;
  line-height: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: background-color 0.2s;

  &:hover {
    background-color: #d32f2f;
  }

  &:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }
`;

const DeleteConfirmationModal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999; /* Ensure it's above everything else */
  isolation: isolate; /* Create a new stacking context */
`;

const ModalContent = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  position: relative; /* Ensure it's positioned */
  z-index: 10000; /* Higher than the modal backdrop */

  h4 {
    margin-top: 0;
    color: #333;
    font-size: 18px;
  }

  p {
    margin-bottom: 20px;
    color: #666;
    line-height: 1.5;
  }

  @media (max-width: 480px) {
    width: 95%;
    padding: 15px;

    h4 {
      font-size: 16px;
    }

    p {
      font-size: 14px;
      margin-bottom: 15px;
    }
  }
`;

const ModalButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;

  @media (max-width: 480px) {
    margin-top: 15px;
    gap: 8px;
  }
`;

const ModalButton = styled.button`
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: background-color 0.2s;

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }

  @media (max-width: 480px) {
    padding: 10px 14px;
    font-size: 13px;
  }
`;

const CancelButton = styled(ModalButton)`
  background-color: #f1f1f1;
  color: #333;

  &:hover:not(:disabled) {
    background-color: #e1e1e1;
  }
`;

const ConfirmButton = styled(ModalButton)`
  background-color: #f44336;
  color: white;

  &:hover:not(:disabled) {
    background-color: #d32f2f;
  }
`;

const CardFormContainer = styled.div`
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #e6e6e6;
  padding: 20px;
  margin-bottom: 20px;

  @media (max-width: 480px) {
    padding: 15px;
    border-radius: 6px;
  }
`;

const CardTitle = styled.div`
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #424770;
  margin-bottom: 20px;

  @media (max-width: 480px) {
    font-size: 16px;
    margin-bottom: 15px;
  }
`;

const CardIcon = styled.span`
  margin-right: 10px;
`;

const CardFieldLabel = styled.label`
  display: block;
  font-size: 14px;
  color: #6b7c93;
  margin-bottom: 8px;
`;

const CardInputContainer = styled.div`
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  padding: 10px 14px;
  background-color: #fff;
  margin-bottom: 16px;
  transition: border-color 0.15s ease;

  &:hover {
    border-color: #aab7c4;
  }

  &:focus-within {
    border-color: #007bff;
    box-shadow: 0 0 0 1px #007bff;
  }
`;

const CardFieldsRow = styled.div`
  display: flex;
  gap: 16px;
  margin-bottom: 16px;

  @media (max-width: 480px) {
    flex-direction: column;
    gap: 0;
  }
`;

const CardFieldColumn = styled.div`
  flex: 1;

  @media (max-width: 480px) {
    width: 100%;
  }
`;

const CountrySelect = styled.select`
  width: 100%;
  padding: 10px 14px;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  background-color: #fff;
  font-size: 16px;
  color: #424770;
  margin-bottom: 16px;
  transition: border-color 0.15s ease;

  &:hover {
    border-color: #aab7c4;
  }

  &:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 1px #007bff;
    outline: none;
  }
`;

const ShippingAddressContainer = styled.div`
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  background-color: #f8f9fa;

  @media (max-width: 480px) {
    padding: 15px;
    margin-top: 15px;
    border-radius: 6px;
  }
`;

const ShippingTitle = styled.div`
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #424770;
  margin-bottom: 20px;

  @media (max-width: 480px) {
    font-size: 16px;
    margin-bottom: 15px;
  }
`;

const ShippingIcon = styled.span`
  margin-right: 10px;
`;

const ShippingInput = styled.input`
  width: 100%;
  padding: 10px 14px;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  background-color: #fff;
  font-size: 16px;
  color: #424770;
  margin-bottom: 16px;
  transition: border-color 0.15s ease;

  &:hover {
    border-color: #aab7c4;
  }

  &:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 1px #007bff;
    outline: none;
  }
`;

export default StripeCheckout;
