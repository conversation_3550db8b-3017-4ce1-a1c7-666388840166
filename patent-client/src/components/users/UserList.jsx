import React, { useState, useEffect, useCallback } from "react";
import styled from "styled-components";
import { Link } from "react-router-dom";
import { PageTitle, Card, SearchInput } from "../../styles/StyledComponents";
import * as userService from "../../services/userService";
import Loader from "../layout/Loader";
import ConfirmationModal from "../common/ConfirmationModal";
import Pagination from "../common/Pagination";
import { useToast } from "../../contexts/ToastContext";
import { useAuth } from "../../contexts/AuthContext";
import {
  Delete as DeleteIcon,
  Lock as LockIcon,
  LockOpen as UnlockIcon,
} from "@styled-icons/material";
const UserHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing.md};
  @media (max-width: 768px) {
    margin-bottom: ${({ theme }) => theme.spacing.md};
    margin-top: 0px;
  }
  @media (max-width: 480px) {
    flex-direction: column;
    align-items: flex-start;
  }
`;
const SearchContainer = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  width: 100%;
  @media (max-width: 768px) {
    margin-bottom: ${({ theme }) => theme.spacing.md};
  }
`;
const ActionButton = styled.button`
  background-color: transparent;
  border: none;
  color: ${({ theme, variant }) =>
    variant === "delete"
      ? theme.colors.danger
      : variant === "deactivate"
        ? theme.colors.warning
        : variant === "activate"
          ? theme.colors.success
          : theme.colors.primary};
  cursor: pointer;
  margin-left: ${({ theme }) => theme.spacing.xs};
  padding: ${({ theme }) => theme.spacing.xs};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: background-color ${({ theme }) => theme.transition};
  display: flex;
  align-items: center;
  justify-content: center;
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
  svg {
    width: 18px;
    height: 18px;
  }
  @media (max-width: 480px) {
    padding: ${({ theme }) => theme.spacing.xs}
      ${({ theme }) => theme.spacing.sm};
    svg {
      width: 16px;
      height: 16px;
    }
  }
`;
const ActionButtonsContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 4px;
`;
const NoDataMessage = styled.div`
  text-align: center;
  margin: ${({ theme }) => theme.spacing.xl} 0;
  color: ${({ theme }) => theme.colors.text.secondary};
  @media (max-width: 768px) {
    margin: ${({ theme }) => theme.spacing.lg} 0;
  }
`;
const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colors.error};
  padding: ${({ theme }) => theme.spacing.md};
  border: 1px solid ${({ theme }) => theme.colors.error};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin: ${({ theme }) => theme.spacing.lg} 0;
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing.sm};
    margin: ${({ theme }) => theme.spacing.md} 0;
  }
`;
const TableContainer = styled.div`
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  &::-webkit-scrollbar {
    height: 6px;
  }
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
  }
`;
const StyledTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  min-width: 650px;
  th {
    text-align: left;
    padding: 12px 16px;
    border-bottom: 1px solid #e0e0e0;
    font-weight: 500;
    color: #333;
    background-color: #f9f9f9;
    white-space: nowrap;
  }
  td {
    padding: 12px 16px;
    border-bottom: 1px solid #e0e0e0;
  }
  tr:last-child td {
    border-bottom: none;
  }
  @media (max-width: 768px) {
    th,
    td {
      padding: 8px 10px;
      font-size: 0.9rem;
    }
  }
  @media (max-width: 480px) {
    min-width: 500px;
    th,
    td {
      padding: 6px 8px;
      font-size: 0.8rem;
    }
  }
`;
const StatusBadge = styled.span`
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: ${({ status, theme }) =>
    status === "active"
      ? theme.colors.success + "20"
      : status === "pending"
        ? theme.colors.warning + "20"
        : status === "deactivated"
          ? theme.colors.danger + "20"
          : theme.colors.danger + "20"};
  color: ${({ status, theme }) =>
    status === "active"
      ? theme.colors.success
      : status === "pending"
        ? theme.colors.warning
        : status === "deactivated"
          ? theme.colors.danger
          : theme.colors.danger};
  @media (max-width: 480px) {
    padding: 3px 6px;
    font-size: 0.7rem;
  }
`;
const UserList = () => {
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState(null);
  const { addToast } = useToast();
  const { user } = useAuth();
  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      if (!user || !user.isAdmin) {
        setError("You are not authorized to view user data");
        setLoading(false);
        return;
      }
      const response = await userService.getUsers(currentPage, 6);
      if (Array.isArray(response)) {
        setUsers(response);
        setFilteredUsers(response);
        setPagination(null);
      } else if (response && response.users) {
        setUsers(response.users);
        setFilteredUsers(response.users);
        setPagination(response.pagination);
      } else {
        setUsers([]);
        setFilteredUsers([]);
        setPagination(null);
      }
      setError(null);
    } catch (err) {
      console.error("Failed to fetch users:", err);
      setError("Failed to load users. Please try again later.");
    } finally {
      setLoading(false);
    }
  }, [user, currentPage]);
  useEffect(() => {
    fetchUsers();
  }, [user, fetchUsers, currentPage]);
  useEffect(() => {
    if (users.length > 0) {
      const filtered = users.filter(
        (user) =>
          !user.isAdmin &&
          user.role !== "Admin" &&
          ((user.name &&
            user.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (user.email &&
              user.email.toLowerCase().includes(searchTerm.toLowerCase()))),
      );
      setFilteredUsers(filtered);
    }
  }, [users, searchTerm]);
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };
  const handleStatusToggle = async (id, currentStatus) => {
    try {
      const newStatus = currentStatus === "active" ? "deactivated" : "active";
      await userService.updateUserStatus(id, { status: newStatus });
      setUsers((prevUsers) =>
        prevUsers.map((user) =>
          user._id === id ? { ...user, status: newStatus } : user,
        ),
      );
      addToast(
        `User ${newStatus === "active" ? "activated" : "deactivated"} successfully!`,
        "success",
      );
    } catch (err) {
      console.error("Failed to update user status:", err);
      addToast("Failed to update user status. Please try again.", "error");
    }
  };
  const handleDeleteClick = (id) => {
    setUserToDelete(id);
    setDeleteModalOpen(true);
  };
  const handleDeleteConfirm = async () => {
    try {
      setIsDeleting(true);
      await userService.deleteUser(userToDelete);
      setUsers((prevUsers) =>
        prevUsers.filter((user) => user._id !== userToDelete),
      );
      addToast("User deleted successfully!", "success");
    } catch (err) {
      console.error("Failed to delete user:", err);
      addToast("Failed to delete user. Please try again.", "error");
    } finally {
      setTimeout(() => {
        setIsDeleting(false);
        setDeleteModalOpen(false);
        setUserToDelete(null);
      }, 500);
    }
  };
  if (loading) {
    return <Loader section="users" />;
  }
  if (error) {
    return (
      <ErrorMessage>
        {error}
        {!user && (
          <p>
            Please <Link to="/login">login</Link> with administrator credentials
            to access this page.
          </p>
        )}
      </ErrorMessage>
    );
  }
  return (
    <div>
      <UserHeader>
        <PageTitle>Users</PageTitle>
      </UserHeader>
      <SearchContainer>
        <SearchInput
          placeholder="Search users..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </SearchContainer>
      {filteredUsers.length > 0 ? (
        <>
          {}
          <Card>
            <TableContainer>
              <StyledTable>
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Joined</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredUsers.map((user) => (
                    <tr key={user._id}>
                      <td>{`${user.firstName || ""} ${user.lastName || ""}`}</td>
                      <td>{user.email}</td>
                      <td>{formatDate(user.createdAt || user.joinDate)}</td>
                      <td>
                        <StatusBadge status={user.status || "pending"}>
                          {user.status || "Pending"}
                        </StatusBadge>
                      </td>
                      <td>
                        <ActionButtonsContainer>
                          {}
                          <ActionButton
                            onClick={() =>
                              handleStatusToggle(
                                user._id,
                                user.status || "active",
                              )
                            }
                            variant={
                              user.status === "active"
                                ? "deactivate"
                                : "activate"
                            }
                            title={
                              user.status === "active"
                                ? "Deactivate user"
                                : "Activate user"
                            }
                          >
                            {user.status === "active" ? (
                              <LockIcon />
                            ) : (
                              <UnlockIcon />
                            )}
                          </ActionButton>
                          {}
                          <ActionButton
                            onClick={() => handleDeleteClick(user._id)}
                            variant="delete"
                            title="Delete user"
                          >
                            <DeleteIcon />
                          </ActionButton>
                        </ActionButtonsContainer>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </StyledTable>
            </TableContainer>
          </Card>
          {}
          {pagination &&
            !searchTerm &&
            pagination.totalCount > 6 &&
            pagination.totalPages > 1 && (
              <Pagination
                pagination={pagination}
                onPageChange={(page) => {
                  setCurrentPage(page);
                  window.scrollTo(0, 0);
                }}
                variant="default"
              />
            )}
        </>
      ) : (
        <NoDataMessage>
          {searchTerm
            ? `No users found for "${searchTerm}". Try a different search.`
            : "No users available."}
        </NoDataMessage>
      )}
      {}
      <ConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => !isDeleting && setDeleteModalOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="Delete User"
        message="Are you sure you want to delete this user? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        variant="danger"
        isLoading={isDeleting}
      />
    </div>
  );
};
export default UserList;
