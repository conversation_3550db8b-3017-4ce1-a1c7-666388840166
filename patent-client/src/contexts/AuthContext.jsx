import { createContext, useState, useContext, useEffect } from "react";
import * as authService from "../services/authService";
const AuthContext = createContext();
export const useAuth = () => useContext(AuthContext);
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    const loadUserFromStorage = () => {
      try {
        let savedUser =
          localStorage.getItem("patentpionner_user") ||
          sessionStorage.getItem("patentpionner_user");
        if (!savedUser) {
          savedUser = localStorage.getItem("patentpionner_user");
          if (savedUser) {
          }
        } else {
        }
        if (savedUser) {
          const parsedUser = JSON.parse(savedUser);
          const loginTime = parsedUser.loginTimestamp
            ? new Date(parsedUser.loginTimestamp)
            : null;
          const currentTime = new Date();
          const tokenAgeHours = loginTime
            ? (currentTime - loginTime) / (1000 * 60 * 60)
            : 0;
          setUser({
            ...parsedUser,
            isAdmin: parsedUser.isAdmin || parsedUser.role === "Admin",
          });
        } else {
          setUser(null);
        }
      } catch (e) {
        console.error("Error parsing user data from storage", e);
        localStorage.removeItem("patentpionner_user");
        sessionStorage.removeItem("patentpionner_user");
        setUser(null);
      } finally {
        setLoading(false);
      }
    };
    loadUserFromStorage();
    const handleStorageChange = (e) => {
      if (e.key === "patentpionner_user" || e.type === "storage") {
        loadUserFromStorage();
      }
    };
    window.addEventListener("storage", handleStorageChange);
    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);
  const login = async (usernameOrEmail, password, rememberMe = false) => {
    try {
      const isEmail = /\S+@\S+\.\S+/.test(usernameOrEmail);
      const loginPayload = {
        password,
        [isEmail ? "email" : "username"]: usernameOrEmail,
      };
      const userData = await authService.login(loginPayload);
      if (userData.needsVerification) {
        return {
          success: false,
          needsVerification: true,
          email: userData.email,
          message:
            userData.message || "Please verify your email before logging in.",
        };
      }
      if (userData) {
        const isAdmin =
          usernameOrEmail.toLowerCase() === "admin" ||
          (userData.user && userData.user.isAdmin) ||
          (userData.user && userData.user.role === "Admin") ||
          userData.isAdmin ||
          userData.role === "Admin" ||
          false;
        const normalizedUser = {
          ...userData,
          accessToken: userData.accessToken,
          refreshToken: userData.refreshToken,
          isAdmin: isAdmin,
          loginTimestamp: new Date().toISOString(),
        };
        if (userData.user) {
          Object.keys(userData.user).forEach((key) => {
            normalizedUser[key] = userData.user[key];
          });
        }
        setUser(normalizedUser);
        const storageData = JSON.stringify(normalizedUser);
        if (rememberMe) {
          localStorage.setItem("patentpionner_user", storageData);
          sessionStorage.removeItem("patentpionner_user");
        } else {
          sessionStorage.setItem("patentpionner_user", storageData);
          localStorage.removeItem("patentpionner_user");
        }
        return { success: true, isAdmin };
      }
      return { success: false, error: "Invalid credentials" };
    } catch (error) {
      return {
        success: false,
        error: error.message || "Failed to login. Please try again.",
      };
    }
  };
  const register = async (userData) => {
    try {
      const response = await authService.register(userData);
      if (response.message && response.message.includes("verification")) {
        return {
          success: true,
          data: response,
          needsVerification: true,
          email: userData.email,
        };
      }
      return { success: true, data: response };
    } catch (error) {
      console.error("Registration failed:", error);
      let errorMessage = "Registration failed. Please try again.";
      if (error.message) {
        errorMessage = error.message;
        if (error.message.includes("already exists")) {
          errorMessage =
            "An account with this email already exists. Please use a different email or try logging in.";
        }
      }
      if (error.response && error.response.message) {
        errorMessage = error.response.message;
      }
      if (error.request && !error.response) {
        errorMessage =
          "No response from server. Please check your internet connection and try again.";
      }
      return { success: false, error: errorMessage };
    }
  };
  const logout = () => {
    setUser(null);
    localStorage.removeItem("patentpionner_user");
    sessionStorage.removeItem("patentpionner_user");
    localStorage.removeItem("patentpionner_cart");
    localStorage.removeItem("pending_purchase_course");
    localStorage.removeItem("user_preferences");
    localStorage.removeItem("last_visited");
  };
  const resetPassword = async (currentPassword, newPassword) => {
    try {
      const result = await fetch("/api/v1/auth/reset-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${user.token}`,
        },
        body: JSON.stringify({
          currentPassword,
          newPassword,
        }),
      });
      if (!result.ok) {
        throw new Error("Password reset failed");
      }
      return true;
    } catch (error) {
      console.error("Password reset failed:", error);
      return false;
    }
  };
  const updateCurrentUser = (updatedUserData) => {
    const updatedUser = user
      ? {
          ...user,
          ...updatedUserData,
        }
      : updatedUserData;
    setUser(updatedUser);
    const storageData = JSON.stringify(updatedUser);
    if (localStorage.getItem("patentpionner_user")) {
      localStorage.setItem("patentpionner_user", storageData);
    } else if (sessionStorage.getItem("patentpionner_user")) {
      sessionStorage.setItem("patentpionner_user", storageData);
    } else {
      sessionStorage.setItem("patentpionner_user", storageData);
    }
    return true;
  };
  const value = {
    user,
    login,
    register,
    logout,
    resetPassword,
    updateCurrentUser,
    loading,
  };
  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
