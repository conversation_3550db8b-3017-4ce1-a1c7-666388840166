import React, {
  createContext,
  useState,
  useContext,
  useCallback,
  useRef,
} from "react";
import Toast from "../components/layout/Toast";
const ToastContext = createContext();
export const ToastProvider = ({ children }) => {
  const [toasts, setToasts] = useState([]);
  const nextIdRef = useRef(1);
  const lastToastRef = useRef({ message: "", type: "", timestamp: 0 });
  const addToast = useCallback(
    (message, type = "info", duration = 5000) => {
      const now = Date.now();
      if (
        lastToastRef.current.message === message &&
        lastToastRef.current.type === type &&
        now - lastToastRef.current.timestamp < 3000
      ) {
        return null;
      }
      lastToastRef.current = {
        message,
        type,
        timestamp: now,
      };
      const existingToast = toasts.find(
        (toast) => toast.message === message && toast.type === type,
      );
      if (existingToast) {
        return existingToast.id;
      }
      const id = nextIdRef.current++;
      setToasts((prev) => [...prev, { id, message, type, isExiting: false }]);
      setTimeout(() => {
        setToasts((prev) =>
          prev.map((toast) =>
            toast.id === id ? { ...toast, isExiting: true } : toast,
          ),
        );
        setTimeout(() => {
          setToasts((prev) => prev.filter((toast) => toast.id !== id));
        }, 300);
      }, duration);
      return id;
    },
    [toasts],
  );
  const removeToast = useCallback((id) => {
    setToasts((prev) =>
      prev.map((toast) =>
        toast.id === id ? { ...toast, isExiting: true } : toast,
      ),
    );
    setTimeout(() => {
      setToasts((prev) => prev.filter((toast) => toast.id !== id));
    }, 300);
  }, []);
  return (
    <ToastContext.Provider value={{ addToast, removeToast }}>
      {children}
      <Toast toasts={toasts} removeToast={removeToast} />
    </ToastContext.Provider>
  );
};
export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider");
  }
  return context;
};
