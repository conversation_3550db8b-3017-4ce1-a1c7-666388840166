// Mock data for PatentPionner dashboard
let courses = [
  {
    id: 1,
    title: "Patent Basics",
    description:
      "Learn the fundamentals of patents and intellectual property protection.",
    price: 199.99,
    image:
      "https://images.unsplash.com/photo-1589829085413-56de8ae18c73?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80",
    modules: [
      {
        id: 1,
        title: "Introduction to Patents",
        type: "text",
        content:
          "A patent is an exclusive right granted for an invention, which is a product or a process that provides a new way of doing something, or offers a new technical solution to a problem.",
      },
      {
        id: 2,
        title: "Patent Search",
        type: "pdf",
        content: "/documents/patent-search-guide.pdf",
      },
      {
        id: 3,
        title: "Filing a Patent",
        type: "video",
        content: "https://example.com/videos/filing-patent.mp4",
      },
    ],
  },
  {
    id: 2,
    title: "Patent Drafting Masterclass",
    description:
      "Advanced techniques for drafting effective patent applications.",
    price: 299.99,
    image:
      "https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80",
    modules: [
      {
        id: 1,
        title: "Crafting Claims",
        type: "text",
        content:
          "Claims are the most important part of a patent application as they define the scope of protection.",
      },
      {
        id: 2,
        title: "Example Claims",
        type: "pdf",
        content: "/documents/example-claims.pdf",
      },
    ],
  },
  {
    id: 3,
    title: "Patent Law for Startups",
    description:
      "Essential patent knowledge for entrepreneurs and startup founders.",
    price: 149.99,
    image:
      "https://images.unsplash.com/photo-1556761175-5973dc0f32e7?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80",
    modules: [
      {
        id: 1,
        title: "IP Strategy",
        type: "text",
        content:
          "Developing an effective IP strategy is crucial for startups to protect their innovations and attract investors.",
      },
      {
        id: 2,
        title: "Funding and Patents",
        type: "image",
        content: "/images/funding-patents-chart.jpg",
      },
    ],
  },
  {
    id: 4,
    title: "Software Patents",
    description:
      "Specialized course on patenting software innovations and algorithms.",
    price: 249.99,
    image:
      "https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80",
    modules: [
      {
        id: 1,
        title: "Software Patentability",
        type: "text",
        content:
          "Understanding what types of software innovations can be patented in different jurisdictions.",
      },
      {
        id: 2,
        title: "Case Studies",
        type: "pdf",
        content: "/documents/software-patent-cases.pdf",
      },
    ],
  },
  {
    id: 5,
    title: "Dummy Video Course",
    description: "A course with a dummy video for testing purposes.",
    price: 99.99,
    image: "https://via.placeholder.com/150",
    modules: [
      {
        id: 1,
        title: "Introduction",
        type: "video",
        content:
          "https://videos.pexels.com/video-files/5199861/5199861-sd_360_640_25fps.mp4",
      },
      {
        id: 2,
        title: "Conclusion",
        type: "text",
        content: "Thank you for watching the dummy video course.",
      },
    ],
  },
];
const users = [
  {
    id: 1,
    name: "John Smith",
    email: "<EMAIL>",
    role: "Student",
    joinDate: "2023-01-15",
    courses: [1, 2],
  },
  {
    id: 2,
    name: "Sarah Johnson",
    email: "<EMAIL>",
    role: "Student",
    joinDate: "2023-02-22",
    courses: [1],
  },
  {
    id: 3,
    name: "Michael Brown",
    email: "<EMAIL>",
    role: "Instructor",
    joinDate: "2022-11-05",
    courses: [1, 2, 3, 4],
  },
  {
    id: 4,
    name: "Emily Davis",
    email: "<EMAIL>",
    role: "Student",
    joinDate: "2023-03-10",
    courses: [3],
  },
];
const purchases = [
  {
    id: 1,
    userId: 1,
    courseId: 1,
    amount: 199.99,
    date: "2023-01-15",
    status: "Completed",
  },
  {
    id: 2,
    userId: 1,
    courseId: 2,
    amount: 299.99,
    date: "2023-02-03",
    status: "Completed",
  },
  {
    id: 3,
    userId: 2,
    courseId: 1,
    amount: 199.99,
    date: "2023-02-22",
    status: "Completed",
  },
  {
    id: 4,
    userId: 4,
    courseId: 3,
    amount: 149.99,
    date: "2023-03-10",
    status: "Completed",
  },
];
const admin = {
  username: "admin",
  password: "admin123",
  email: "<EMAIL>",
};
const addCourse = (course) => {
  try {
    const newId = Math.floor(Math.random() * 9900) + 100;
    const parsedPrice =
      typeof course.price === "string"
        ? parseFloat(course.price)
        : course.price;
    const newCourse = {
      ...course,
      id: newId,
      price: parsedPrice,
      image:
        course.image instanceof File
          ? URL.createObjectURL(course.image)
          : course.image,
      modules: course.modules.map((module, index) => ({
        ...module,
        id: index + 1,
        content: convertFileContentToUrl(module.content, module.type),
      })),
    };
    courses = [...courses, newCourse];
    return newCourse;
  } catch (error) {
    console.error("Error adding course:", error);
    return null;
  }
};
const deleteCourse = (courseId) => {
  courses = courses.filter((course) => course.id !== courseId);
  return courses;
};
const updateCourse = (courseId, courseData) => {
  try {
    const parsedPrice =
      typeof courseData.price === "string"
        ? parseFloat(courseData.price)
        : courseData.price;
    const existingCourse = courses.find((course) => course.id === courseId);
    if (!existingCourse) {
      console.error(`Course with ID ${courseId} not found`);
      return null;
    }
    courses = courses.map((course) =>
      course.id === courseId
        ? {
            ...courseData,
            id: courseId,
            price: parsedPrice,
            image:
              courseData.image instanceof File
                ? URL.createObjectURL(courseData.image)
                : courseData.image || course.image,
            modules: courseData.modules.map((module, index) => ({
              ...module,
              id: index + 1,
              content: convertFileContentToUrl(module.content, module.type),
            })),
          }
        : course,
    );
    return courses.find((course) => course.id === courseId);
  } catch (error) {
    console.error("Error updating course:", error);
    return null;
  }
};
const convertFileContentToUrl = (content, type) => {
  if (!content) return "";
  if (typeof content === "string") return content;
  if (content instanceof File) {
    return URL.createObjectURL(content);
  }
  if (
    Array.isArray(content) &&
    content.length > 0 &&
    content[0] instanceof File
  ) {
    return content.map((file) => URL.createObjectURL(file));
  }
  return content.toString ? content.toString() : "";
};
export {
  courses,
  users,
  purchases,
  addCourse,
  deleteCourse,
  updateCourse,
  admin,
};
