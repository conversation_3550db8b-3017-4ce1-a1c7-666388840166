@import "tailwindcss";

/* Ensure the entire page does not show a scrollbar */
html, body {
    height: 100%;                  /* Ensure full height */
    margin: 0;                     /* Remove default margin */
    overflow: unset;              /* Hide scrollbars completely */
}

/* For Firefox */
body {
    scrollbar-width: none;         /* Hide scrollbar for Firefox */
}

/* For IE and Edge */
body {
    -ms-overflow-style: none;      /* Hide scrollbar for IE/Edge */
}

/* For Chrome, Safari, and Brave */
body::-webkit-scrollbar {
    display: none; /* Hide the scrollbar for Chrome/Safari/Brave */
}

/* Optional: Hide scrollbars in all elements */
* {
    scrollbar-width: none; /* Hide scrollbar for Firefox */
    -ms-overflow-style: none; /* Hide scrollbar for IE/Edge */
}

*::-webkit-scrollbar {
    display: none; /* Hide scrollbar for Chrome/Safari/Brave */
}

/* index.css */
.custom-checkbox {
    width: 20px;
    height: 20px;
    border: 2px solid #ccc;
    border-radius: 4px;
    background-color: white;
    appearance: defalut ;
    position: relative;
  }
  
  .custom-checkbox:checked {
    background-color: #009f9f25;
    border-color: #009F9F;
  }
  