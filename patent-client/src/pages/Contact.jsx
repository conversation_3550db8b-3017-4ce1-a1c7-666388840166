import React, { useState, useEffect, useRef } from "react";
import { Check, Loader, AlertCircle } from "lucide-react";
import contactService from "../services/contactService";

const Contact = ({ showHeader = true }) => {
  const [formStage, setFormStage] = useState("greeting");
  const [userName, setUserName] = useState("");
  const [userEmail, setUserEmail] = useState("");
  const [userFeedback, setUserFeedback] = useState("");
  const [inputValue, setInputValue] = useState("");
  const [submitted, setSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const inputRef = useRef(null);
  
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 18) return "Good afternoon";
    return "Good evening";
  };
  
  useEffect(() => {
    if (inputRef.current && !submitted) {
      inputRef.current.focus();
    }
  }, [formStage, submitted]);
  
  const getCurrentQuestion = () => {
    switch (formStage) {
      case "greeting":
        return `${getGreeting()}! Please share your name:`;
      case "name":
        return `Thank you, ${userName}! Please provide your email address:`;
      case "email":
        return `Great! How can we help you today? Please share your questions or feedback:`;
      default:
        return "";
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!inputValue.trim()) return;
    
    // Reset error state
    setError(null);
    
    switch (formStage) {
      case "greeting":
        setUserName(inputValue);
        setFormStage("name");
        break;
      case "name":
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (emailRegex.test(inputValue)) {
          setUserEmail(inputValue);
          setFormStage("email");
        } else {
          setError("Please enter a valid email address");
          return; // Don't clear input to allow user to correct it
        }
        break;
      case "email":
        setUserFeedback(inputValue);
        
        const submissionData = {
          name: userName,
          email: userEmail,
          feedback: inputValue,
          to: "<EMAIL>"
        };
        
        // Submit form data to API
        try {
          setIsLoading(true);
          await contactService.submitContactForm(submissionData);
          setSubmitted(true);
        } catch (err) {
          setError(err.message || "Failed to submit your message. Please try again later.");
          console.error("Contact form submission error:", err);
        } finally {
          setIsLoading(false);
        }
        break;
    }
    
    setInputValue("");
  };

  return (
    <div className="shop-container relative overflow-hidden">
      {showHeader && (
        <section className="banner overflow-hidden bg-[#009F9F] pt-40 py-16 px-4 md:py-20 gap-4 text-white text-center relative">
          <svg
            width="277"
            height="291"
            viewBox="0 0 277 291"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="absolute bottom-30 -right-20 md:bottom-20 md:right-0 opacity-10 transform scale-60 md:scale-100 z-0"
          >
            <circle
              cx="238.543"
              cy="53.3657"
              r="187.562"
              stroke="black"
              strokeWidth="100"
            />
          </svg>
          <svg
            width="277"
            height="291"
            viewBox="0 0 277 291"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="absolute top-60 -left-20 md:top-20 md:left-0 opacity-10 rotate-180 transform scale-60 md:scale-100 z-0"
          >
            <circle
              cx="238.543"
              cy="53.3657"
              r="187.562"
              stroke="black"
              strokeWidth="100"
            />
          </svg>
          <div className="flex justify-center items-center max-w-4xl mx-auto">
            <div className="text-center">
              <h2 className="relative text-4xl font-bold mb-4 z-10">
                Get in Touch with Us
              </h2>
              <p className="relative text-xl mb-4 z-10">
                Have questions or feedback? We'd love to hear from you!
              </p>
            </div>
          </div>
        </section>
      )}
      
      <div className={`container mx-auto px-4 ${showHeader ? 'py-12' : 'py-0'}`}>
        <div className="max-w-2xl mx-auto">
          {!submitted ? (
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="bg-[#009F9F] text-white p-6">
                <h3 className="text-2xl font-semibold">{showHeader ? "Contact Patent Pioneer" : "Have Questions?"}</h3>
                <p className="text-sm opacity-80 mt-1">{showHeader ? "We're here to answer your queries and receive your feedback" : "We're here to assist you with any inquiries"}</p>
              </div>
              
              <div className="p-6 space-y-6">
                <div className="mb-8">
                  <h3 className="text-xl font-medium text-gray-800 mb-2">
                    {getCurrentQuestion()}
                  </h3>
                  <div className="h-1 w-full bg-gray-200 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-[#009F9F] rounded-full transition-all duration-300" 
                      style={{ 
                        width: formStage === "greeting" ? "33%" : 
                               formStage === "name" ? "66%" : "100%" 
                      }}
                    ></div>
                  </div>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  {formStage === "email" ? (
                    <textarea
                      ref={inputRef}
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value)}
                      placeholder={showHeader ? "Please type your feedback here..." : "Please type your question here..."}
                      rows={5}
                      className="w-full border-2 border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:border-[#009F9F] focus:ring-2 focus:ring-[#009F9F] resize-none text-gray-800"
                      required
                    />
                  ) : (
                    <input
                      type={formStage === "name" ? "email" : "text"}
                      ref={inputRef}
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value)}
                      placeholder={
                        formStage === "greeting" ? "Your name" : 
                        formStage === "name" ? "Your email address" : ""
                      }
                      className="w-full border-2 border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:border-[#009F9F] focus:ring-2 focus:ring-[#009F9F] text-gray-800"
                      required
                    />
                  )}
                  
                  {error && (
                    <div className="flex items-center space-x-2 text-red-500 bg-red-50 p-3 rounded-lg">
                      <AlertCircle size={18} />
                      <span>{error}</span>
                    </div>
                  )}
                  
                  <button
                    type="submit"
                    disabled={isLoading}
                    className={`w-full bg-[#009F9F] text-white py-3 px-6 rounded-lg hover:bg-[#008a8a] transition-colors font-medium flex justify-center items-center ${isLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
                  >
                    {isLoading ? (
                      <>
                        <Loader size={18} className="animate-spin mr-2" />
                        {formStage === "email" ? "Submitting..." : "Processing..."}
                      </>
                    ) : (
                      formStage === "email" ? 
                        (showHeader ? "Submit Feedback" : "Submit Question") : 
                        "Continue"
                    )}
                  </button>
                </form>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Check size={40} className="text-green-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-2">Thank You!</h3>
              <p className="text-gray-600 text-lg mb-6">
                {showHeader ? 
                  `We appreciate your feedback, ${userName}. We'll review it carefully and use it to improve our services.` :
                  `Thank you for reaching out, ${userName}. We'll respond to your question as soon as possible.`
                }
              </p>
              <p className="text-gray-500">
                A confirmation has been sent to your email address: <span className="font-medium">{userEmail}</span>
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Contact;
