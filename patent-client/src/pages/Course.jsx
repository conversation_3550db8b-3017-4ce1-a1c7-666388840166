import React, { useState, useEffect } from "react";
import CourseBanner from "../components/CoursePage/CourseBanner";
import TestimonialsSection from "../components/AboutPage/TestimonialsSection";
import AboutContactForm from "../components/AboutPage/AboutContactForm";
import CourseCard from "../components/AboutPage/CourseCard";
import { useToast } from "../contexts/ToastContext";
import { useAuth } from "../contexts/AuthContext";
import { API_BASE_URL } from ".././config/config.js";
import {staticCourses} from "../components/AboutPage/CoursesSection.jsx";
const Course = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [userPurchases, setUserPurchases] = useState([]);
  const { addToast } = useToast();
  const { user } = useAuth();
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const coursePromise = fetch(`${API_BASE_URL}/courses`);
        const purchasesPromise = user
          ? fetch(`${API_BASE_URL}/purchases/my`, {
              headers: {
                Authorization: `Bearer ${user.accessToken || user.token}`,
              },
            })
          : null;
        const [courseResponse, purchaseResponse] = await Promise.all(
          [coursePromise, purchasesPromise].filter(Boolean),
        );
        if (!courseResponse.ok) {
          throw new Error(`Failed to fetch courses: ${courseResponse.status}`);
        }
        const responseData = await courseResponse.json();
        const data = Array.isArray(responseData)
          ? responseData
          : responseData && responseData.courses
            ? responseData.courses
            : [];
        let purchases = [];
        if (purchaseResponse?.ok) {
          const purchaseData = await purchaseResponse.json();
          purchases = Array.isArray(purchaseData)
            ? purchaseData
            : purchaseData.purchases || [];
        }
        setUserPurchases(purchases);
        const formattedCourses = data.map((course) => {
          const isOwned = purchases.some((purchase) => {
            const purchaseCourseId =
              purchase.courseId && typeof purchase.courseId === "object"
                ? purchase.courseId._id
                : purchase.courseId;
            return purchaseCourseId === course._id;
          });
          return {
            id: course._id,
            title: course.title,
            level: course.level || "Beginner",
            hours: course.hours || 0,
            duration: course.hours
              ? `${course.hours} ${course.hours === 1 ? "Hour" : "Hours"}`
              : "35 Hours",
            certificate:
              course.certificate !== undefined ? course.certificate : true,
            description: course.description,
            link: `/course-detail/${course._id}`,
            type: "course",
            price: course.price,
            imageUrl: course.image,
            isOwned,
            apiData: course,
          };
        });
        const ctaCard = staticCourses.find((course) => course.type === "cta");
        if (ctaCard && formattedCourses.length >= 4) {
          formattedCourses.splice(4, 0, ctaCard);
        } else if (ctaCard) {
          formattedCourses.push(ctaCard);
        }
        setCourses(formattedCourses);
        setError(null);
      } catch (err) {
        console.error("Error fetching courses:", err);
        setError("Failed to load courses");
        setCourses(staticCourses);
        addToast("Using demo courses due to connection issues", "warning");
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [user, addToast]);
  useEffect(() => {
    const handlePurchaseSuccess = () => {};
    window.addEventListener("purchaseSuccess", handlePurchaseSuccess);
    return () =>
      window.removeEventListener("purchaseSuccess", handlePurchaseSuccess);
  }, [user]);
  if (loading) {
    return (
      <div>
        <CourseBanner />
        <div className="container  h-full mx-auto py-12 px-4 flex justify-center">
          <div className="w-full text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#009F9F] mr-2"></div>
            <span>Loading Course...</span>
          </div>
        </div>
      </div>
    );
  }
  return (
    <div>
      <CourseBanner />
      <div className="container  h-full mx-auto py-12 px-4">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {courses.map((course, index) => (
            <CourseCard
              key={course.id || index}
              course={course}
              showBuyButton={course.type !== "cta"}
              isOwned={course.isOwned}
            />
          ))}
        </div>
      </div>
      <TestimonialsSection />
      {/* <AboutContactForm /> */}
    </div>
  );
};
export default Course;