import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import CourseDetailBanner from "../components/CourseDetail/CourseDetailBanner";
import PatentCourseOutline from "../components/CourseDetail/PatentCourseOutline";
import TestimonialsSection from "../components/AboutPage/TestimonialsSection";
import InstructorBio from "../components/CourseDetail/InstructorBio";
import CourseFeatures from "../components/CourseDetail/CourseFeatures";
import FAQSection from "../components/CourseDetail/FAQSection";
import { useAuth } from "../contexts/AuthContext";
import { useToast } from "../contexts/ToastContext";
import { API_BASE_URL } from ".././config/config.js";
const CourseDetail = () => {
  const { id } = useParams();
  const [course, setCourse] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useAuth();
  const navigate = useNavigate();
  const { addToast } = useToast();
  useEffect(() => {
    const fetchCourseDetails = async () => {
      if (!id) {
        setLoading(false);
        return;
      }
      try {
        setLoading(true);
        const response = await fetch(`${API_BASE_URL}/courses/${id}`, {
          headers: user
            ? {
                Authorization: `Bearer ${user.accessToken || user.token}`,
              }
            : {},
        });
        if (!response.ok) {
          throw new Error(`Failed to fetch course: ${response.status}`);
        }
        const courseData = await response.json();
        setCourse(courseData);
        setError(null);
      } catch (err) {
        console.error("Error fetching course details:", err);
        setError("Failed to load course details. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    fetchCourseDetails();
  }, [id, user]);
  const handleBuyNow = () => {
    if (!user) {
      localStorage.setItem("pending_purchase_course", JSON.stringify(course));
      const cartItem = {
        id: course._id,
        title: course.title,
        price: course.price,
        type: "course",
        quantity: 1,
        imageUrl: course.image?.url,
      };
      const existingCart = JSON.parse(
        localStorage.getItem("patentpionner_cart") || "[]",
      );
      const itemExists = existingCart.some(
        (item) => item.id === cartItem.id && item.type === "course",
      );
      if (!itemExists) {
        const updatedCart = [...existingCart, cartItem];
        localStorage.setItem("patentpionner_cart", JSON.stringify(updatedCart));
        setTimeout(() => {
          const itemAddedEvent = new CustomEvent("itemAddedToCart", {
            detail: { item: cartItem },
          });
          window.dispatchEvent(itemAddedEvent);
          window.dispatchEvent(new CustomEvent("cartUpdated"));
        }, 20);
      }
      addToast(
        "Please register or login first to purchase this course",
        "info",
      );
      navigate("/signup");
      return;
    }
    if (course) {
      const cartItem = {
        id: course._id,
        title: course.title,
        price: course.price,
        type: "course",
        quantity: 1,
        imageUrl: course.image?.url,
      };
      const existingCart = JSON.parse(
        localStorage.getItem("patentpionner_cart") || "[]",
      );
      const itemExists = existingCart.some(
        (item) => item.id === cartItem.id && item.type === "course",
      );
      if (!itemExists) {
        const updatedCart = [...existingCart, cartItem];
        localStorage.setItem("patentpionner_cart", JSON.stringify(updatedCart));
        setTimeout(() => {
          const itemAddedEvent = new CustomEvent("itemAddedToCart", {
            detail: { item: cartItem },
          });
          window.dispatchEvent(itemAddedEvent);
          window.dispatchEvent(new CustomEvent("cartUpdated"));
          
          // Add a small delay before opening the cart to ensure events are processed in order
          setTimeout(() => {
            window.dispatchEvent(new CustomEvent("openCart"));
          }, 100);
        }, 50);
      } else {
        addToast("This course is already in your cart", "info");
        // Add a small delay before opening the cart
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent("openCart"));
        }, 100);
      }
    }
  };
  if (loading) {
    return (
      <div className="w-full text-center py-12">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#009F9F] mr-2"></div>
        <span>Loading Details...</span>
      </div>
    );
  }
  if (error) {
    return (
      <div className="max-w-6xl mx-auto py-16 px-4 text-center">
        <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
        <p className="text-gray-700">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 bg-[#009F9F] text-white px-4 py-2 rounded-md"
        >
          Try Again
        </button>
      </div>
    );
  }
  return (
    <>
      <CourseDetailBanner course={course} onBuyNow={handleBuyNow} />
      <PatentCourseOutline course={course} />
      <TestimonialsSection
        color="[#242B2B]"
        textcolor="[#FFFFFF]"
        hideButton={true}
      />
      <InstructorBio />
      <CourseFeatures />
      <FAQSection />
    </>
  );
};
export default CourseDetail;
