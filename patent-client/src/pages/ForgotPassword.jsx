import React, { useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { Link, useNavigate } from "react-router-dom";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { LucideArrowLeft } from "lucide-react";
import Button from "../components/Button";
import Logo from "../assets/Logo.png";
import LoginImage from "../assets/login.jpg";
import * as authService from "../services/authService";
const ForgotPassword = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const formik = useFormik({
    initialValues: {
      email: "",
    },
    validationSchema: Yup.object({
      email: Yup.string()
        .email("Invalid email address")
        .required("Email is required"),
    }),
    onSubmit: async (values, { setSubmitting }) => {
      try {
        setIsLoading(true);
        const response = await authService.forgotPassword(values.email);
        setEmailSent(true);
        toast.success(
          "Password reset instructions have been sent to your email!",
          {
            position: "top-right",
            autoClose: 5000,
          },
        );
      } catch (err) {
        console.error("Forgot password error:", err);
        toast.error(
          err.message || "Failed to send reset email. Please try again.",
          {
            position: "top-right",
            autoClose: 5000,
          },
        );
      } finally {
        setSubmitting(false);
        setIsLoading(false);
      }
    },
  });
  return (
    <div className="flex h-screen">
      <ToastContainer />
      {}
      <div
        className="hidden md:flex w-[80%] h-full bg-cover items-end justify-start bg-center p-8"
        style={{
          backgroundImage: `linear-gradient(to top right, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.2)), url(${LoginImage})`,
        }}
      >
        <h1 className="text-5xl font-medium italic text-gray-100">
          Reset Your Password with Ease.
        </h1>
      </div>
      {}
      <div className="w-full flex flex-col justify-start items-center bg-white px-4 py-6">
        {}
        <div className="flex w-full items-center">
          <LucideArrowLeft
            className="w-6 h-6 text-gray-500 cursor-pointer"
            onClick={() => window.history.back()}
          />
          <p className="ml-2 text-md text-gray-500">back</p>
        </div>
        <div className="flex flex-col justify-center max-w-lg w-full h-full gap-4">
          {}
          <div className="flex justify-center">
            <img src={Logo} alt="Logo" className="w-24 h-24" />
          </div>
          <h2 className="text-2xl text-center font-bold text-gray-800">
            Forgot Your Password?
          </h2>
          <p className="text-md text-center text-gray-400">
            Enter your email address and we'll send you instructions to reset
            your password.
          </p>
          {emailSent ? (
            <div className="flex flex-col items-center mt-6">
              <div className="bg-green-100 text-green-700 p-4 rounded-md mb-6 text-center">
                <p className="font-medium">Email Sent!</p>
                <p className="text-sm mt-1">
                  Please check your inbox for password reset instructions.
                </p>
              </div>
              <Button
                text="Back to Login"
                className="w-full mb-4"
                onClick={() => navigate("/login")}
              />
            </div>
          ) : (
            <form
              onSubmit={formik.handleSubmit}
              className="flex flex-col gap-4 w-full"
            >
              {}
              <div className="flex flex-col gap-2">
                <label htmlFor="email" className="block text-sm text-gray-600">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  {...formik.getFieldProps("email")}
                  placeholder="Enter your email address"
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#009F9F] focus:outline-none"
                  disabled={isLoading}
                />
                {formik.touched.email && formik.errors.email ? (
                  <div className="text-red-500 text-sm">
                    {formik.errors.email}
                  </div>
                ) : null}
              </div>
              {}
              <Button
                text={isLoading ? "Sending..." : "Reset Password"}
                className="w-full mb-4"
                disabled={formik.isSubmitting || isLoading}
              />
            </form>
          )}
        </div>
        <div className="text-center">
          <p className="text-md text-gray-600">
            Remember your password?{" "}
            <Link to="/login" className="text-[#009F9F]">
              Login
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};
export default ForgotPassword;
