import React, { useState, useEffect } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { LucideArrowLeft, LucideEye, LucideEyeOff } from "lucide-react";
import Button from "../components/Button";
import Logo from "../assets/Logo.png";
import LoginImage from "../assets/login.jpg";
import * as authService from "../services/authService";
const PasswordRule = ({ label, valid }) => (
  <span
    className={`inline-flex items-center gap-1 mr-3 ${valid ? "text-green-600" : "text-gray-400"}`}
  >
    <span className="text-sm font-bold">{valid ? "✓" : "•"}</span>
    <span className="text-xs whitespace-nowrap">{label}</span>
  </span>
);
const ResetPassword = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [resetSuccess, setResetSuccess] = useState(false);
  const [token, setToken] = useState("");
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const tokenParam = searchParams.get("token");
    if (tokenParam) {
      setToken(tokenParam);
    } else {
      toast.error("Invalid or missing reset token", {
        position: "top-right",
        autoClose: 5000,
      });
    }
  }, [location]);
  const passwordRules = {
    minLength: (formik) => formik.values.password.length >= 8,
    hasUppercase: (formik) => /[A-Z]/.test(formik.values.password),
    hasLowercase: (formik) => /[a-z]/.test(formik.values.password),
    hasNumber: (formik) => /[0-9]/.test(formik.values.password),
    hasSpecialChar: (formik) =>
      /[!@#$%^&*(),.?":{}|<>]/.test(formik.values.password),
    passwordsMatch: (formik) =>
      formik.values.password === formik.values.confirmPassword &&
      formik.values.confirmPassword !== "",
  };
  const formik = useFormik({
    initialValues: {
      password: "",
      confirmPassword: "",
    },
    validationSchema: Yup.object({
      password: Yup.string()
        .min(8, "Password must be at least 8 characters")
        .matches(/[A-Z]/, "Password must contain at least one uppercase letter")
        .matches(/[a-z]/, "Password must contain at least one lowercase letter")
        .matches(/[0-9]/, "Password must contain at least one number")
        .matches(
          /[!@#$%^&*(),.?":{}|<>]/,
          "Password must contain at least one special character",
        )
        .required("Password is required"),
      confirmPassword: Yup.string()
        .oneOf([Yup.ref("password"), null], "Passwords must match")
        .required("Confirm password is required"),
    }),
    onSubmit: async (values, { setSubmitting }) => {
      if (!token) {
        toast.error("Missing reset token", {
          position: "top-right",
          autoClose: 5000,
        });
        return;
      }
      try {
        setIsLoading(true);
        const response = await authService.resetPassword(
          token,
          values.password,
        );
        setResetSuccess(true);
        toast.success("Your password has been reset successfully!", {
          position: "top-right",
          autoClose: 5000,
        });
        setTimeout(() => {
          navigate("/login");
        }, 3000);
      } catch (err) {
        console.error("Reset password error:", err);
        toast.error(
          err.message || "Failed to reset password. Please try again.",
          {
            position: "top-right",
            autoClose: 5000,
          },
        );
      } finally {
        setSubmitting(false);
        setIsLoading(false);
      }
    },
  });
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };
  return (
    <div className="flex h-screen">
      <ToastContainer />
      {}
      <div
        className="hidden md:flex w-[80%] h-full bg-cover items-end justify-start bg-center p-8"
        style={{
          backgroundImage: `linear-gradient(to top right, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.2)), url(${LoginImage})`,
        }}
      >
        <h1 className="text-5xl font-medium italic text-gray-100">
          Create a New Password for Your Account.
        </h1>
      </div>
      {}
      <div className="w-full flex flex-col justify-start items-center bg-white px-4 py-6">
        {}
        <div className="flex w-full items-center">
          <LucideArrowLeft
            className="w-6 h-6 text-gray-500 cursor-pointer"
            onClick={() => navigate("/login")}
          />
          <p className="ml-2 text-md text-gray-500">back to login</p>
        </div>
        <div className="flex flex-col justify-center max-w-lg w-full h-full gap-4">
          {}
          <div className="flex justify-center">
            <img src={Logo} alt="Logo" className="w-24 h-24" />
          </div>
          <h2 className="text-2xl text-center font-bold text-gray-800">
            Reset Your Password
          </h2>
          <p className="text-md text-center text-gray-400">
            Create a new secure password for your account.
          </p>
          {resetSuccess ? (
            <div className="flex flex-col items-center mt-6">
              <div className="bg-green-100 text-green-700 p-4 rounded-md mb-6 text-center">
                <p className="font-medium">Password Reset Successful!</p>
                <p className="text-sm mt-1">
                  Your password has been changed. You will be redirected to the
                  login page.
                </p>
              </div>
              <Button
                text="Go to Login"
                className="w-full mb-4"
                onClick={() => navigate("/login")}
              />
            </div>
          ) : (
            <form
              onSubmit={formik.handleSubmit}
              className="flex flex-col gap-4 w-full"
            >
              {}
              <div className="flex flex-col gap-2 relative">
                <label
                  htmlFor="password"
                  className="block text-sm text-gray-600"
                >
                  New Password
                </label>
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  {...formik.getFieldProps("password")}
                  placeholder="Enter your new password"
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#009F9F] focus:outline-none"
                  disabled={isLoading}
                />
                <div
                  className="absolute right-4 top-[55%] transform cursor-pointer"
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? (
                    <LucideEyeOff className="w-5 h-5 text-gray-600" />
                  ) : (
                    <LucideEye className="w-5 h-5 text-gray-600" />
                  )}
                </div>
                {formik.touched.password && formik.errors.password ? (
                  <div className="text-red-500 text-sm">
                    {formik.errors.password}
                  </div>
                ) : null}
              </div>
              {}
              <div className="flex flex-wrap gap-y-2 mb-2">
                <PasswordRule
                  label="8+ Characters"
                  valid={passwordRules.minLength(formik)}
                />
                <PasswordRule
                  label="Uppercase"
                  valid={passwordRules.hasUppercase(formik)}
                />
                <PasswordRule
                  label="Lowercase"
                  valid={passwordRules.hasLowercase(formik)}
                />
                <PasswordRule
                  label="Number"
                  valid={passwordRules.hasNumber(formik)}
                />
                <PasswordRule
                  label="Special Char"
                  valid={passwordRules.hasSpecialChar(formik)}
                />
              </div>
              {}
              <div className="flex flex-col gap-2 relative">
                <label
                  htmlFor="confirmPassword"
                  className="block text-sm text-gray-600"
                >
                  Confirm Password
                </label>
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  id="confirmPassword"
                  {...formik.getFieldProps("confirmPassword")}
                  placeholder="Confirm your new password"
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#009F9F] focus:outline-none"
                  disabled={isLoading}
                />
                <div
                  className="absolute right-4 top-[55%] transform cursor-pointer"
                  onClick={toggleConfirmPasswordVisibility}
                >
                  {showConfirmPassword ? (
                    <LucideEyeOff className="w-5 h-5 text-gray-600" />
                  ) : (
                    <LucideEye className="w-5 h-5 text-gray-600" />
                  )}
                </div>
                {formik.touched.confirmPassword &&
                formik.errors.confirmPassword ? (
                  <div className="text-red-500 text-sm">
                    {formik.errors.confirmPassword}
                  </div>
                ) : null}
              </div>
              {}
              <Button
                text={isLoading ? "Resetting..." : "Reset Password"}
                className="w-full mb-4"
                disabled={
                  formik.isSubmitting ||
                  isLoading ||
                  !Object.values(passwordRules).every((rule) => rule(formik))
                }
              />
            </form>
          )}
        </div>
      </div>
    </div>
  );
};
export default ResetPassword;
