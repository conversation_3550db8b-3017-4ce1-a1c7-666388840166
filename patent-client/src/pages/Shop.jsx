"use client";
import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import BookCard from "../components/BookCard";
// import { getAllBooks, getMyBooks } from "../services/bookService";
import Pagination from "../components/Pagination";
import Button from "../components/Button";
import CartModal from "../components/CartModal";
import { useAuth } from "../contexts/AuthContext";
import { useToast } from "../contexts/ToastContext";
import { API_BASE_URL } from ".././config/config.js";
const Shop = ({ addToCart, cartItems, removeFromCart }) => {
  const [books, setBooks] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [booksPerPage] = useState(6);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showCart, setShowCart] = useState(false);
  const [userBooks, setUserBooks] = useState([]);
  const { user } = useAuth();
  const { addToast } = useToast();
  const location = useLocation();
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    if (searchParams.get("cart") === "open") {
      setShowCart(true);
    }
  }, [location.search]);
  useEffect(() => {
    const getBooks = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${API_BASE_URL}/books`, {
          headers: user
            ? {
                Authorization: `Bearer ${user.accessToken || user.token}`,
              }
            : {},
        });
        if (!response.ok) {
          throw new Error(`Failed to fetch books: ${response.status}`);
        }
        const data = await response.json();
        if (Array.isArray(data)) {
          setBooks(data);
        } else if (data && data.books) {
          setBooks(data.books);
        } else {
          setBooks([]);
        }
        if (user) {
          try {
            const myBooksResponse = await fetch(`${API_BASE_URL}/books/my`, {
              headers: {
                Authorization: `Bearer ${user.accessToken || user.token}`,
              },
            });
            if (!myBooksResponse.ok) {
              throw new Error(
                `Failed to fetch user books: ${myBooksResponse.status}`,
              );
            }
            const myBooksData = await myBooksResponse.json();
            if (Array.isArray(myBooksData)) {
              setUserBooks(myBooksData);
            } else if (myBooksData && myBooksData.books) {
              setUserBooks(myBooksData.books);
            } else {
              setUserBooks([]);
            }
          } catch (err) {
            console.error("Error fetching user's books:", err);
            setUserBooks([]);
          }
        }
        setError(null);
      } catch (error) {
        console.error("Error fetching books:", error);
        setError("Failed to load books. Please try again later.");
        setBooks([]);
      } finally {
        setLoading(false);
      }
    };
    getBooks();
  }, [user]);
  const indexOfLastBook = currentPage * booksPerPage;
  const indexOfFirstBook = indexOfLastBook - booksPerPage;
  const currentBooks = books.slice(indexOfFirstBook, indexOfLastBook);
  const paginate = (pageNumber) => setCurrentPage(pageNumber);
  const toggleCart = () => setShowCart(!showCart);
  const handleRemoveFromCart = (itemId) => {
    if (typeof removeFromCart === "function") {
      removeFromCart(itemId);
    }
  };
  const userOwnsBook = (bookId) => {
    return userBooks.some((book) => book._id === bookId);
  };
  return (
    <div className="shop-container relative overflow-hidden">
      <section className="banner overflow-hidden bg-[#009F9F] pt-40 py-16 px-4 md:py-20 gap-4 text-white text-center relative">
        <svg
          width="277"
          height="291"
          viewBox="0 0 277 291"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="absolute bottom-30 -right-20 md:bottom-20 md:right-0 opacity-10 transform scale-60 md:scale-100 z-0"
        >
          <circle
            cx="238.543"
            cy="53.3657"
            r="187.562"
            stroke="black"
            strokeWidth="100"
          />
        </svg>
        <svg
          width="277"
          height="291"
          viewBox="0 0 277 291"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="absolute top-60 -left-20 md:top-20 md:left-0 opacity-10 rotate-180 transform scale-60 md:scale-100 z-0"
        >
          <circle
            cx="238.543"
            cy="53.3657"
            r="187.562"
            stroke="black"
            strokeWidth="100"
          />
        </svg>
        {}
        <div className="flex justify-center items-center max-w-4xl mx-auto">
          <div className="text-center">
            <h2 className="relative text-4xl font-bold mb-4 z-10">
              Patent Prep Books
            </h2>
            <p className="relative text-xl mb-4 z-10">
              Embark on your journey to patent success with PatentPath.
            </p>
            <Button text="View Cart" secondary onClick={toggleCart} />
          </div>
        </div>
      </section>
      {}
      <section className="books-grid  py-12 px-4 container mx-auto">
        {loading ? (
          <div className="w-full text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#009F9F] mr-2"></div>
            <span>Loading books...</span>
          </div>
        ) : error ? (
          <div className="w-full text-center py-12 text-red-500">{error}</div>
        ) : books.length === 0 ? (
          <div className="w-full text-center py-12">
            No books available at the moment.
          </div>
        ) : (
          <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
            {currentBooks.map((book) => (
              <BookCard
                key={book._id}
                book={{
                  id: book._id,
                  title: book.title,
                  description: book.description,
                  price: book.price,
                  image:
                    book.image ||
                    "https://via.placeholder.com/300x400?text=Book+Cover",
                  author: book.author,
                  publishedYear: book.publishedYear,
                  type: book.type,
                }}
                addToCart={addToCart}
                isOwned={userOwnsBook(book._id)}
              />
            ))}
          </div>
        )}
      </section>
      {}
     
        {/* <Pagination
          totalBooks={books.length}
          booksPerPage={booksPerPage}
          paginate={paginate}
          currentPage={currentPage}
        />
     */}
      {}
      <section className="newsletter my-8 px-4">
        <div className="bg-[#041E35] container  h-full mx-auto rounded-2xl p-4 md:p-12 flex flex-col lg:flex-row items-center text-white text-center lg:text-left justify-between relative">
          {}
          <div className="w-full md:max-w-[400px] z-1">
            <h3 className="text-2xl font-semibold mb-4">
              Need Notifications for New Books?
            </h3>
            <p className="mb-6">
              Subscribe to our newsletter and get notified when we upload new
              books!
            </p>
          </div>
          <form className="flex flex-col md:flex-row justify-center w-full gap-4 z-1">
            <input
              type="email"
              placeholder="Enter your email"
              className="bg-white text-black px-4 py-2 md:w-80 rounded-md"
            />
            <Button text="Send Message" />
          </form>
          {}
          <svg
            width="277"
            height="291"
            viewBox="0 0 277 291"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="absolute top-0 -right-20 opacity-5 transform scale-60 md:scale-100 z-0"
          >
            <circle
              cx="238.543"
              cy="53.3657"
              r="187.562"
              stroke="white"
              strokeWidth="100"
            />
          </svg>
          <svg
            width="277"
            height="291"
            viewBox="0 0 277 291"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="absolute md:bottom-0 md:-left-20 opacity-5 rotate-180 transform scale-60 md:scale-100 z-0"
          >
            <circle
              cx="238.543"
              cy="53.3657"
              r="187.562"
              stroke="white"
              strokeWidth="100"
            />
          </svg>
        </div>
      </section>
      {}
      {showCart && (
        <CartModal
          cartItems={cartItems}
          closeModal={toggleCart}
          removeFromCart={handleRemoveFromCart}
        />
      )}
    </div>
  );
};
export default Shop;
