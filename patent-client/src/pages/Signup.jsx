import React, { useState, useEffect } from "react";
import { LucideArrowLeft, LucideEye, LucideEyeOff } from "lucide-react";
import Button from "../components/Button";
import Logo from "../assets/Logo.png";
import LoginImage from "../assets/login.jpg";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";

const PasswordRule = ({ label, valid }) => (
  <span
    className={`inline-flex items-center gap-1 mr-3 mb-1 ${valid ? "text-green-600" : "text-gray-400"}`}
  >
    <span className="text-sm font-bold">{valid ? "✓" : "•"}</span>
    <span className="text-xs whitespace-nowrap">{label}</span>
  </span>
);

const Signup = () => {
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [redirectPath, setRedirectPath] = useState("");
  const navigate = useNavigate();
  const location = useLocation();
  const { register } = useAuth();
  
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const redirect = searchParams.get("redirect");
    if (redirect) {
      setRedirectPath(redirect);
    }
  }, [location]);
  
  const handleGoBack = () => {
    window.history.back();
  };
  
  const isPasswordValid = (pass) => {
    return (
      pass.length >= 8 &&
      /[A-Z]/.test(pass) &&
      /[a-z]/.test(pass) &&
      /[0-9]/.test(pass) &&
      /[^A-Za-z0-9]/.test(pass)
    );
  };
  
  useEffect(() => {
    const validateFields = () => {
      const newErrors = {};
      if (firstName && firstName.trim().length < 2) {
        newErrors.firstName = "First name must be at least 2 characters";
      }
      if (lastName && lastName.trim().length < 2) {
        newErrors.lastName = "Last name must be at least 2 characters";
      }
      if (username && username.trim().length < 3) {
        newErrors.username = "Username must be at least 3 characters";
      }
      if (email && !/\S+@\S+\.\S+/.test(email)) {
        newErrors.email = "Please enter a valid email address";
      }
      if (password && !isPasswordValid(password)) {
        newErrors.password = "Password must meet all requirements";
      }
      if (confirmPassword && confirmPassword !== password) {
        newErrors.confirmPassword = "Passwords do not match";
      }
      setErrors(newErrors);
    };
    validateFields();
  }, [firstName, lastName, email, username, password, confirmPassword]);
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    const newErrors = {};
    if (!firstName.trim()) newErrors.firstName = "First name is required";
    else if (firstName.trim().length < 2)
      newErrors.firstName = "First name must be at least 2 characters";
    if (!lastName.trim()) newErrors.lastName = "Last name is required";
    else if (lastName.trim().length < 2)
      newErrors.lastName = "Last name must be at least 2 characters";
    if (!email.trim()) newErrors.email = "Email is required";
    else if (!/\S+@\S+\.\S+/.test(email))
      newErrors.email = "Please enter a valid email address";
    if (!username.trim()) newErrors.username = "Username is required";
    else if (username.trim().length < 3)
      newErrors.username = "Username must be at least 3 characters";
    if (!password) {
      newErrors.password = "Password is required";
    } else if (!isPasswordValid(password)) {
      newErrors.password = "Password must meet all requirements";
      return;
    }
    if (confirmPassword !== password) {
      newErrors.confirmPassword = "Passwords do not match";
      return;
    }
    if (!termsAccepted) {
      newErrors.terms = "You must accept the terms and conditions";
    }
    setErrors(newErrors);
    if (Object.keys(newErrors).length === 0) {
      setLoading(true);
      try {
        const userData = {
          firstName,
          lastName,
          email,
          password,
          username,
        };
        const result = await register(userData);
        if (result.success) {
          if (result.needsVerification) {
            navigate("/verify", {
              state: { email: userData.email, redirectPath },
            });
          } else {
            toast.success(
              "Account created! Please check your email to verify your account.",
              {
                position: "top-right",
                autoClose: 5000,
              },
            );
            const isAdmin =
              result.data &&
              (result.data.isAdmin ||
                (result.data.user && result.data.user.isAdmin) ||
                (result.data.user && result.data.user.role === "Admin") ||
                result.data.role === "Admin");
            setTimeout(() => {
              if (isAdmin) {
                navigate("/dashboard");
              } else if (redirectPath) {
                navigate(`/login?redirect=${redirectPath}`);
              } else {
                navigate("/login");
              }
            }, 3000);
          }
        } else {
          toast.error(result.error, {
            position: "top-right",
            autoClose: 5000,
          });
        }
      } catch (error) {
        console.error("Registration error:", error);
        let errorMessage = "Registration failed. Please try again.";
        if (error.message) {
          if (error.message.includes("already exists")) {
            errorMessage =
              "An account with this email already exists. Please use a different email or try logging in.";
          } else {
            errorMessage = error.message;
          }
        }
        toast.error(errorMessage, {
          position: "top-right",
          autoClose: 5000,
        });
      } finally {
        setLoading(false);
      }
    }
  };
  
  return (
    <div className="flex flex-col md:flex-row h-screen overflow-auto">
      <ToastContainer />
      {/* Left Side - Background Image */}
      <div
        className="hidden md:flex w-full  h-full bg-cover items-end justify-start bg-center p-8"
        style={{
          backgroundImage: `linear-gradient(to top right, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.2)), url(${LoginImage})`,
        }}
      >
        <h1 className="text-3xl md:text-4xl lg:text-5xl font-medium italic text-gray-100">
          Join the Future of Patent Intelligence.
        </h1>
      </div>
      {/* Right Side - Form */}
      <div className="w-full  flex flex-col justify-start items-center bg-white px-4 sm:px-6 py-6">
        <div className="flex w-full items-center">
          <LucideArrowLeft
            className="w-6 h-6 text-gray-500 cursor-pointer"
            onClick={handleGoBack}
          />
          <p className="ml-2 text-md text-gray-500">back</p>
        </div>
        <div className="flex flex-col justify-center max-w-lg w-full h-full gap-4 mx-auto">
          {/* Logo */}
          <div className="flex justify-center">
            <img src={Logo} alt="Logo" className="w-20 h-20 sm:w-24 sm:h-24" />
          </div>
          <h2 className="text-xl sm:text-2xl text-center font-bold text-gray-800">
            Create Your Account
          </h2>
          <p className="text-sm sm:text-md text-center text-gray-400">
            Let's get started with your personalized experience.
          </p>
          <form onSubmit={handleSubmit} className="flex flex-col gap-4 w-full mt-2">
            {/* Name Fields */}
            <div className="flex flex-col sm:flex-row gap-4">
              {/* First Name */}
              <div className="flex flex-col gap-2 flex-1">
                <label htmlFor="firstName" className="text-sm text-gray-600">
                  First Name
                </label>
                <input
                  type="text"
                  id="firstName"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  placeholder="Enter your first name"
                  className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-md focus:ring-[#009F9F] focus:outline-none text-sm sm:text-base"
                />
                {errors.firstName && (
                  <p className="text-xs sm:text-sm text-red-500">{errors.firstName}</p>
                )}
              </div>
              {/* Last Name */}
              <div className="flex flex-col gap-2 flex-1">
                <label htmlFor="lastName" className="text-sm text-gray-600">
                  Last Name
                </label>
                <input
                  type="text"
                  id="lastName"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  placeholder="Enter your last name"
                  className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-md focus:ring-[#009F9F] focus:outline-none text-sm sm:text-base"
                />
                {errors.lastName && (
                  <p className="text-xs sm:text-sm text-red-500">{errors.lastName}</p>
                )}
              </div>
            </div>
            <div className="flex flex-col gap-2">
              <label htmlFor="username" className="text-sm text-gray-600">
                Username
              </label>
              <input
                type="text"
                id="username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Choose a username"
                className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-md focus:ring-[#009F9F] focus:outline-none text-sm sm:text-base"
              />
              {errors.username && (
                <p className="text-xs sm:text-sm text-red-500">{errors.username}</p>
              )}
            </div>
            {/* Email Field */}
            <div className="flex flex-col gap-2">
              <label htmlFor="email" className="text-sm text-gray-600">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-md focus:ring-[#009F9F] focus:outline-none text-sm sm:text-base"
              />
              {errors.email && (
                <p className="text-xs sm:text-sm text-red-500">{errors.email}</p>
              )}
            </div>
            {/* Password Field */}
            {/* This is the password field */}
            <div className="flex flex-col gap-2 relative">
              <label htmlFor="password" className="text-sm text-gray-600">
                Password
              </label>
              <input
                type={showPassword ? "text" : "password"}
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                className={`w-full px-3 sm:px-4 py-2 border ${
                  password && !isPasswordValid(password)
                    ? "border-yellow-400"
                    : password
                      ? "border-green-500"
                      : "border-gray-300"
                } rounded-md focus:ring-[#009F9F] focus:outline-none text-sm sm:text-base`}
              />
              <div
                className="absolute right-3 sm:right-4 top-9 sm:top-10 cursor-pointer"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <LucideEyeOff className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
                ) : (
                  <LucideEye className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
                )}
              </div>
              {errors.password && (
                <p className="text-xs sm:text-sm text-red-500">{errors.password}</p>
              )}
              {/* Password Requirements */}
              {password.length > 0 && (
                <div className="flex flex-wrap mt-2 bg-gray-50 p-2 rounded-md">
                  <PasswordRule label="8+ chars" valid={password.length >= 8} />
                  <PasswordRule
                    label="Uppercase"
                    valid={/[A-Z]/.test(password)}
                  />
                  <PasswordRule
                    label="Lowercase"
                    valid={/[a-z]/.test(password)}
                  />
                  <PasswordRule label="Number" valid={/[0-9]/.test(password)} />
                  <PasswordRule
                    label="Special char"
                    valid={/[^A-Za-z0-9]/.test(password)}
                  />
                </div>
              )}
            </div>
            {/* Confirm Password Field */}
            <div className="flex flex-col gap-2 relative">
              <label
                htmlFor="confirmPassword"
                className="text-sm text-gray-600"
              >
                Confirm Password
              </label>
              <input
                type={showConfirm ? "text" : "password"}
                id="confirmPassword"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Re-enter your password"
                className={`w-full px-3 sm:px-4 py-2 border ${
                  confirmPassword && confirmPassword !== password
                    ? "border-red-500"
                    : confirmPassword
                      ? "border-green-500"
                      : "border-gray-300"
                } rounded-md focus:ring-[#009F9F] focus:outline-none text-sm sm:text-base`}
              />
              <div
                className="absolute right-3 sm:right-4 top-9 sm:top-10 cursor-pointer"
                onClick={() => setShowConfirm(!showConfirm)}
              >
                {showConfirm ? (
                  <LucideEyeOff className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
                ) : (
                  <LucideEye className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
                )}
              </div>
              {(confirmPassword && confirmPassword !== password) ||
              errors.confirmPassword ? (
                <p className="text-xs sm:text-sm text-red-500">
                  {errors.confirmPassword || "Passwords do not match"}
                </p>
              ) : null}
            </div>
            {/* Terms and Conditions */}
            <div className="flex items-start mt-2">
              <div className="flex items-center h-5 mt-0.5">
                <input
                  type="checkbox"
                  id="terms"
                  checked={termsAccepted}
                  onChange={(e) => setTermsAccepted(e.target.checked)}
                  className="h-4 w-4 text-[#009F9F] border-gray-300 rounded focus:ring-[#009F9F]"
                />
              </div>
              <label htmlFor="terms" className="ml-2 text-xs sm:text-sm text-gray-600">
                I agree with the{" "}
                <a href="#" className="text-[#009F9F] hover:underline">
                  Terms & Conditions
                </a>{" "}
                and the{" "}
                <a href="#" className="text-[#009F9F] hover:underline">
                  Privacy Policy
                </a>
              </label>
            </div>
            {errors.terms && (
              <p className="text-xs sm:text-sm text-red-500 -mt-2">{errors.terms}</p>
            )}
            {/* Submit Button */}
            <Button
              text={loading ? "Signing Up..." : "Sign Up"}
              className="w-full mb-4 mt-2"
              disabled={
                loading ||
                !firstName ||
                !lastName ||
                !email ||
                !username ||
                !password ||
                !confirmPassword ||
                !termsAccepted ||
                !isPasswordValid(password) ||
                password !== confirmPassword
              }
            />
          </form>
        </div>
        {/* Login Link */}
        <div className="text-center mt-auto pt-4">
          <p className="text-sm sm:text-md text-gray-600">
            Already have an account?{" "}
            <Link to="/login" className="text-[#009F9F]">
              Login
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Signup;