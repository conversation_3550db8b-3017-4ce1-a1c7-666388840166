import React from "react";
import { useLocation } from "react-router-dom";
import VerificationPrompt from "../components/auth/VerificationPrompt";
const VerificationPage = () => {
  const location = useLocation();
  const email = location.state?.email || "";
  const redirectPath = location.state?.redirectPath || "";
  return <VerificationPrompt email={email} redirectPath={redirectPath} />;
};
export default VerificationPage;
