import React, { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
const VerifyEmailRedirect = () => {
  const navigate = useNavigate();
  const { token } = useParams();
  useEffect(() => {
    if (token) {
      navigate(`/verify-email?token=${encodeURIComponent(token)}`);
      return;
    }
    const currentUrl = window.location.href;
    const tokenStartIndex =
      currentUrl.indexOf("/verify-email/") + "/verify-email/".length;
    const extractedToken = currentUrl.substring(tokenStartIndex);
    if (extractedToken) {
      navigate(`/verify-email?token=${encodeURIComponent(extractedToken)}`);
    } else {
      navigate("/verify-email");
    }
  }, [navigate, token]);
  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#009F9F]"></div>
      <p className="mt-4 text-lg">Redirecting to verification page...</p>
    </div>
  );
};
export default VerifyEmailRedirect;
