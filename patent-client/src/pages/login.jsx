import React, { useState, useEffect } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { LucideArrowLeft, LucideEye, LucideEyeOff } from "lucide-react";
import Button from "../components/Button";
import Logo from "../assets/Logo.png";
import LoginImage from "../assets/login.jpg";

const LoginLanding = () => {
  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [redirectPath, setRedirectPath] = useState("");

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const redirect = searchParams.get("redirect");
    if (redirect) {
      setRedirectPath(redirect);
    }
  }, [location]);

  const formik = useFormik({
    initialValues: {
      usernameOrEmail: "",
      password: "",
      rememberMe: false,
    },
    validationSchema: Yup.object({
      usernameOrEmail: Yup.string().required("Username or email is required"),
      password: Yup.string().required("Password is required"),
    }),
    onSubmit: async (values, { setSubmitting, setErrors }) => {
      try {
        setIsLoading(true);
        setError("");
        const result = await login(
          values.usernameOrEmail,
          values.password,
          values.rememberMe,
        );
        if (result.success) {
          toast.success("Login successful!", {
            position: "top-right",
            autoClose: 3000,
          });
          const isAdmin =
            result.isAdmin ||
            (result.user && result.user.isAdmin) ||
            (result.user && result.user.role === "Admin") ||
            result.role === "Admin";
          if (redirectPath) {
            navigate(`/${redirectPath}`);
          } else if (isAdmin) {
            navigate("/dashboard");
          } else {
            navigate("/");
          }
        } else if (result.needsVerification) {
          toast.info("Please verify your email before logging in.", {
            position: "top-right",
            autoClose: 5000,
          });
          navigate("/verify", { state: { email: result.email, redirectPath } });
        } else {
          const errorMessage = result.error || "Invalid username or password";
          setError(errorMessage);
          toast.error(errorMessage, {
            position: "top-right",
            autoClose: 5000,
          });
        }
      } catch (err) {
        const errorMessage =
          err.message || "Failed to login. Please try again.";
        setError(errorMessage);
        toast.error(errorMessage, {
          position: "top-right",
          autoClose: 5000,
        });
      } finally {
        setSubmitting(false);
        setIsLoading(false);
      }
    },
  });

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="flex flex-col md:flex-row h-screen overflow-auto">
      <ToastContainer />
      <div
        className="hidden md:flex w-full  h-full bg-cover items-end justify-start bg-center p-4 sm:p-6 md:p-8"
        style={{
          backgroundImage: `linear-gradient(to top right, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.2)), url(${LoginImage})`,
        }}
      >
        <h1 className="text-3xl md:text-4xl lg:text-5xl font-medium italic text-gray-100">
          Navigate Patents with Precision and Power.
        </h1>
      </div>

      <div className="w-full flex flex-col justify-start items-center bg-white px-4 sm:px-6 py-4 sm:py-6">
        <div className="flex w-full items-center mb-4">
          <LucideArrowLeft
            className="w-5 h-5 sm:w-6 sm:h-6 text-gray-500 cursor-pointer"
            onClick={() => navigate("/")}
          />
          <p className="ml-2 text-sm sm:text-md text-gray-500">back</p>
        </div>

        <div className="flex flex-col justify-center max-w-lg w-full h-full gap-4 mx-auto">
          <div className="flex justify-center">
            <img src={Logo} alt="Logo" className="w-20 h-20 sm:w-24 sm:h-24" />
          </div>

          <h2 className="text-xl sm:text-2xl text-center font-bold text-gray-800">
            Welcome Back, Let's Unlock Your Content!
          </h2>

          <p className="text-sm sm:text-md text-center text-gray-400">
            Log in to access your personalized experience and explore your content.
          </p>

          <form onSubmit={formik.handleSubmit} className="flex flex-col gap-4 w-full mt-2">
            <div className="flex flex-col gap-2">
              <label htmlFor="usernameOrEmail" className="text-sm text-gray-600">
                Username or Email
              </label>
              <input
                type="text"
                id="usernameOrEmail"
                {...formik.getFieldProps("usernameOrEmail")}
                placeholder="Enter your username or email"
                className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-md focus:ring-[#009F9F] focus:outline-none text-sm sm:text-base"
                disabled={isLoading}
              />
              {formik.touched.usernameOrEmail && formik.errors.usernameOrEmail ? (
                <div className="text-red-500 text-xs sm:text-sm">
                  {formik.errors.usernameOrEmail}
                </div>
              ) : null}
            </div>

            <div className="flex flex-col gap-2 relative">
              <label htmlFor="password" className="text-sm text-gray-600">
                Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  {...formik.getFieldProps("password")}
                  placeholder="Enter your password"
                  className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-md focus:ring-[#009F9F] focus:outline-none text-sm sm:text-base"
                  disabled={isLoading}
                />
                <div
                  className="absolute right-3 sm:right-4 top-1/2 transform -translate-y-1/2 cursor-pointer"
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? (
                    <LucideEyeOff className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
                  ) : (
                    <LucideEye className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
                  )}
                </div>
              </div>
              {formik.touched.password && formik.errors.password ? (
                <div className="text-red-500 text-xs sm:text-sm">
                  {formik.errors.password}
                </div>
              ) : null}
            </div>

            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0 mb-2">
              <div className="flex items-center">
                <input
                  id="rememberMe"
                  type="checkbox"
                  className="h-4 w-4 text-[#009F9F] border-gray-300 rounded focus:ring-[#009F9F]"
                  {...formik.getFieldProps("rememberMe")}
                  checked={formik.values.rememberMe}
                  disabled={isLoading}
                />
                <label htmlFor="rememberMe" className="ml-2 text-sm text-gray-600">
                  Remember me
                </label>
              </div>
              <div>
                <Link to="/forgot-password" className="text-sm text-[#009F9F] hover:underline">
                  Forgot password?
                </Link>
              </div>
            </div>

            <Button
              text={isLoading ? "Logging in..." : "Login"}
              className="w-full mb-4 mt-2"
              disabled={formik.isSubmitting || isLoading}
            />
          </form>
        </div>

        <div className="text-center mt-auto pt-4">
          <p className="text-md sm:text-md text-gray-600">
            Don't have an account?{" "}
            <Link
              to={redirectPath ? `/signup?redirect=${redirectPath}` : "/signup"}
              className="text-[#009F9F]"
            >
              Sign up
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginLanding;