import { API_BASE_URL } from "../config/config";
const getHeaders = (isFormData = false) => {
  const headers = isFormData ? {} : { "Content-Type": "application/json" };
  try {
    let userDataString = sessionStorage.getItem("patentpionner_user");
    if (!userDataString) {
      userDataString = localStorage.getItem("patentpionner_user");
    }
    let userData = null;
    if (userDataString) {
      userData = JSON.parse(userDataString);
    }
    if (userData) {
      const token =
        userData.accessToken ||
        userData.token ||
        (userData.user && userData.user.accessToken) ||
        (userData.user && userData.user.token);
      if (token) {
        headers["Authorization"] = `Bearer ${token}`;
      } else {
        console.warn("No token found in user data");
      }
    }
  } catch (error) {
    console.error("Error parsing user data:", error);
  }
  return headers;
};
const pendingRequests = {};
const apiRequest = async (endpoint, method = "GET", data = null) => {
  const requestKey = `${method}:${endpoint}:${JSON.stringify(data || {})}`;
  if (pendingRequests[requestKey]) {
    return pendingRequests[requestKey];
  }
  const requestPromise = (async () => {
    const config = {
      method,
      headers: getHeaders(),
      credentials: "include",
    };
    if (data && (method === "POST" || method === "PUT" || method === "PATCH")) {
      const cleanData = Object.entries(data)
        .filter(([, value]) => value !== undefined && value !== null)
        .reduce((obj, [key, value]) => {
          obj[key] = value;
          return obj;
        }, {});
      config.body = JSON.stringify(cleanData);
    }
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
      const contentType = response.headers.get("content-type");
      let result;
      if (contentType && contentType.includes("application/json")) {
        result = await response.json();
      } else {
        const text = await response.text();
        result = { message: text };
      }
      if (!response.ok) {
        let errorMessage = `Error ${response.status}: Something went wrong`;
        if (result.message) {
          errorMessage = result.message;
        } else if (result.error) {
          errorMessage = result.error;
        } else if (typeof result === "string") {
          errorMessage = result;
        }
        const error = new Error(errorMessage);
        error.response = result;
        error.status = response.status;
        console.error("API Error:", {
          status: response.status,
          message: errorMessage,
          response: result,
        });
        throw error;
      }
      return result;
    } catch (error) {
      console.error("API request failed:", error);
      throw error;
    }
  })();
  pendingRequests[requestKey] = requestPromise;
  try {
    const result = await requestPromise;
    delete pendingRequests[requestKey];
    return result;
  } catch (error) {
    delete pendingRequests[requestKey];
    throw error;
  }
};
export const formDataRequest = async (endpoint, method, formData) => {
  try {
    const priceValue = formData.get("price");
    if (priceValue !== null) {
      let cleanPrice = String(priceValue)
        .replace(/\([^)]*\)/g, "")
        .trim();
      const numPrice = Number(cleanPrice);
      if (isNaN(numPrice)) {
        throw new Error("Price must be a valid number");
      }
      formData.delete("price");
      formData.append("price", numPrice.toString());
    }
    if (endpoint.includes("/courses")) {
      const modulesValue = formData.get("modules");
      if (modulesValue) {
        try {
          const parsedModules = JSON.parse(modulesValue);
          if (!Array.isArray(parsedModules)) {
            if (typeof parsedModules === "object") {
              const fixedModules = JSON.stringify([parsedModules]);
              formData.delete("modules");
              formData.append("modules", fixedModules);
            } else {
              formData.delete("modules");
              formData.append("modules", JSON.stringify([]));
            }
          }
        } catch (e) {
          formData.delete("modules");
          formData.append("modules", JSON.stringify([]));
        }
      } else {
        formData.append("modules", JSON.stringify([]));
      }
    }
    const config = {
      method,
      headers: getHeaders(true),
      body: formData,
      credentials: "include",
    };
    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
    const contentType = response.headers.get("content-type");
    let result;
    if (contentType && contentType.includes("application/json")) {
      result = await response.json();
    } else {
      const text = await response.text();
      result = { message: text };
    }
    if (!response.ok) {
      const errorMessage =
        result.message || result.error || "Something went wrong";
      console.error("API Error:", {
        status: response.status,
        message: errorMessage,
        response: result,
      });
      throw new Error(errorMessage);
    }
    return result;
  } catch (error) {
    console.error("FormData API request failed:", error);
    throw error;
  }
};
export default apiRequest;
