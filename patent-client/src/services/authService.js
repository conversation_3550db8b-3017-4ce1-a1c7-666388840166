import apiRequest from "./api";
export const login = async (loginData) => {
  try {
    return await apiRequest("/auth/login", "POST", loginData);
  } catch (error) {
    throw new Error(error.message || "Failed to login");
  }
};
export const userLogin = async (username, password) => {
  return await apiRequest("/auth/login", "POST", { username, password });
};
export const register = async (userData) => {
  try {
    const response = await apiRequest("/auth/register", "POST", userData);
    return response;
  } catch (error) {
    console.error("Registration API error:", error);
    throw error;
  }
};
let verificationInProgress = false;
let lastVerifiedToken = null;
let cachedVerificationResponse = null;
export const verifyAccount = async (token) => {
  try {
    if (verificationInProgress && token === lastVerifiedToken) {
      await new Promise((resolve) => setTimeout(resolve, 500));
      return verifyAccount(token);
    }
    if (cachedVerificationResponse && token === lastVerifiedToken) {
      return cachedVerificationResponse;
    }
    verificationInProgress = true;
    lastVerifiedToken = token;
    const response = await apiRequest("/auth/verify-account", "POST", {
      token,
    });
    if (response) {
      const userData = {
        ...response,
        accessToken: response.token || response.accessToken,
        refreshToken: response.refreshToken,
        isAdmin:
          response.isAdmin ||
          (response.user && response.user.isAdmin) ||
          (response.user && response.user.role === "Admin") ||
          response.role === "Admin",
        loginTimestamp: new Date().toISOString(),
      };
      if (response.user) {
        Object.keys(response.user).forEach((key) => {
          userData[key] = response.user[key];
        });
      }
      localStorage.setItem("patentpionner_user", JSON.stringify(userData));
      sessionStorage.removeItem("patentpionner_user");
      window.dispatchEvent(new Event("storage"));
      cachedVerificationResponse = userData;
      return userData;
    }
    return response;
  } catch (error) {
    console.error("Verification API error:", error);
    throw error;
  } finally {
    verificationInProgress = false;
  }
};
export const resendVerificationEmail = async (data) => {
  try {
    const response = await apiRequest(
      "/auth/resend-verify-email",
      "POST",
      data,
    );
    return response;
  } catch (error) {
    console.error("Resend verification API error:", error);
    throw error;
  }
};
export const getUsers = async () => {
  return await apiRequest("/users");
};
export const getUserById = async (userId) => {
  return await apiRequest(`/users/${userId}`);
};
export const forgotPassword = async (email) => {
  try {
    const response = await apiRequest("/auth/forgot-password", "POST", {
      email,
    });
    return response;
  } catch (error) {
    console.error("Forgot password API error:", error);
    throw error;
  }
};
export const resetPassword = async (token, password) => {
  try {
    const response = await apiRequest("/auth/reset-password", "POST", {
      token,
      password,
    });
    return response;
  } catch (error) {
    console.error("Reset password API error:", error);
    throw error;
  }
};
export const getUserData = async () => {
  try {
    const savedUser = localStorage.getItem("patentpionner_user");
    if (savedUser) {
      return JSON.parse(savedUser);
    }
    const response = await apiRequest("/auth/me", "GET");
    return response;
  } catch (error) {
    console.error("Get user data error:", error);
    return null;
  }
};
