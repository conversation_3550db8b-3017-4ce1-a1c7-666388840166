import apiRequest, { formDataRequest } from "./api";
export const getAllBooks = async (page = 1, limit = 6) => {
  try {
    const response = await apiRequest(
      `/books?page=${page}&limit=${limit}`,
      "GET",
    );
    return response;
  } catch (error) {
    console.error("Error fetching books:", error);
    throw error;
  }
};
export const getAllBooksForDashboard = async () => {
  try {
    const response = await apiRequest("/books/dashboard", "GET");
    return response;
  } catch (error) {
    console.error("Error fetching books for dashboard:", error);
    throw error;
  }
};
export const getHardcopyBooksByStatus = async (status) => {
  try {
    let endpoint = "/books/hardcopy/status";
    if (status) {
      endpoint += `/${status}`;
    }
    const response = await apiRequest(endpoint, "GET");
    return response;
  } catch (error) {
    console.error("Error fetching hardcopy books by status:", error);
    throw error;
  }
};
export const getMyBooks = async (page = 1, limit = 6) => {
  try {
    const response = await apiRequest(
      `/books/my?page=${page}&limit=${limit}`,
      "GET",
    );
    return response;
  } catch (error) {
    console.error("Error fetching my books:", error);
    throw error;
  }
};
export const getMyBooksForDashboard = async () => {
  try {
    const response = await apiRequest("/books/my/dashboard", "GET");
    return response;
  } catch (error) {
    console.error("Error fetching my books for dashboard:", error);
    throw error;
  }
};
export const getBookById = async (id) => {
  try {
    const response = await apiRequest(`/books/${id}`, "GET");
    return response;
  } catch (error) {
    console.error(`Error fetching book ${id}:`, error);
    throw error;
  }
};
export const createBook = async (bookData) => {
  try {
    const formData = new FormData();
    Object.keys(bookData).forEach((key) => {
      if (key !== "image" && key !== "pdfFile") {
        formData.append(key, bookData[key]);
      }
    });
    if (bookData.image && bookData.image instanceof File) {
      formData.append("image", bookData.image);
    }
    if (
      bookData.type === "softcopy" &&
      bookData.pdfFile &&
      bookData.pdfFile instanceof File
    ) {
      formData.append("pdfFile", bookData.pdfFile);
    }
    const response = await formDataRequest("/books", "POST", formData);
    return response;
  } catch (error) {
    console.error("Error creating book:", error);
    throw error;
  }
};
export const updateBook = async (id, bookData) => {
  try {
    const formData = new FormData();
    Object.keys(bookData).forEach((key) => {
      if (key !== "image" && key !== "pdfFile") {
        formData.append(key, bookData[key]);
      }
    });
    if (bookData.image && bookData.image instanceof File) {
      formData.append("image", bookData.image);
    }
    if (
      bookData.type === "softcopy" &&
      bookData.pdfFile &&
      bookData.pdfFile instanceof File
    ) {
      formData.append("pdfFile", bookData.pdfFile);
    }
    const response = await formDataRequest(`/books/${id}`, "PUT", formData);
    return response;
  } catch (error) {
    console.error(`Error updating book ${id}:`, error);
    throw error;
  }
};
export const deleteBook = async (id) => {
  try {
    const response = await apiRequest(`/books/${id}`, "DELETE");
    return response;
  } catch (error) {
    console.error(`Error deleting book ${id}:`, error);
    throw error;
  }
};
export const createStripeCheckoutForBook = async (bookId) => {
  try {
    const response = await apiRequest("/stripe/payment-intent", "POST", {
      bookId,
    });
    return response;
  } catch (error) {
    console.error(`Error creating Stripe checkout for book ${bookId}:`, error);
    if (error.message && error.message.includes("already own")) {
      const ownedError = new Error(error.message);
      ownedError.code = "ALREADY_OWNED";
      throw ownedError;
    }
    throw error;
  }
};
export const purchaseBook = async (id) => {
  try {
    const response = await apiRequest(`/books/${id}/purchase`, "POST");
    return response;
  } catch (error) {
    console.error(`Error purchasing book ${id}:`, error);
    throw error;
  }
};
export const updateBookStatus = async (id, status) => {
  try {
    const response = await apiRequest(`/books/${id}/status`, "PUT", { status });
    return response;
  } catch (error) {
    console.error(`Error updating book status ${id}:`, error);
    throw error;
  }
};
export const getBookPdfUrl = async (id) => {
  try {
    const response = await apiRequest(`/books/${id}/pdf`, "GET");
    let result =
      response && typeof response === "object"
        ? response
        : { pdfUrl: response };
    if (result.pdfUrl && result.pdfUrl.includes("/image/")) {
      result.pdfUrl = result.pdfUrl.replace("/image/", "/raw/");
    }
    return result;
  } catch (error) {
    try {
      const response = await apiRequest(`/books/${id}/view-pdf`, "GET");
      let result =
        response && typeof response === "object"
          ? response
          : { pdfUrl: response };
      if (result.pdfUrl && result.pdfUrl.includes("/image/")) {
        result.pdfUrl = result.pdfUrl.replace("/image/", "/raw/");
      }
      return result;
    } catch (viewError) {
      console.error(`Error fetching PDF URL for book ${id}:`, error);
      throw error;
    }
  }
};
export const createBulkStripeCheckout = async (bookIds, totalAmount) => {
  try {
    const response = await apiRequest("/stripe/bulk-payment-intent", "POST", {
      bookIds,
      totalAmount,
    });
    return response;
  } catch (error) {
    console.error(`Error creating bulk Stripe checkout for books:`, error);
    if (error.message && error.message.includes("already own")) {
      const ownedError = new Error(error.message);
      ownedError.code = "ALREADY_OWNED";
      throw ownedError;
    }
    throw error;
  }
};




// export const fetchBooks = async () => {
//   try {
//     const response = await fetch("http://localhost:5000/api/books");
//     if (!response.ok) {
//       throw new Error("Failed to fetch books");
//     }
//     const data = await response.json();
//     return data;
//   } catch (error) {
//     console.error("Error fetching books:", error);
//     return [];
//   }
// };
// export const fetchBookById = async (bookId) => {
//   try {
//     const response = await fetch(`http://localhost:5000/api/books/${bookId}`);
//     if (!response.ok) {
//       throw new Error("Failed to fetch book details");
//     }
//     const data = await response.json();
//     return data;
//   } catch (error) {
//     console.error("Error fetching book details:", error);
//     return null;
//   }
// };
