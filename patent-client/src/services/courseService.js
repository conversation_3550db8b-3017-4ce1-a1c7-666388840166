import apiRequest, { formDataRequest } from "./api";
import { API_BASE_URL } from "../config/config";
// const API_BASE_URL = "http://localhost:5000/api/v1";
export const getCourses = async (page = 1, limit = 6) => {
  return await apiRequest(`/courses?page=${page}&limit=${limit}`);
};
export const getCoursesForDashboard = async () => {
  return await apiRequest("/courses/dashboard");
};
export const getMyCourses = async (page = 1, limit = 6) => {
  return await apiRequest(`/courses/my?page=${page}&limit=${limit}`);
};
export const getMyCoursesForDashboard = async () => {
  return await apiRequest("/courses/my/dashboard");
};
export const getCourseById = async (courseId) => {
  return await apiRequest(`/courses/${courseId}`);
};
export const createCourse = async (courseData) => {
  const hasFiles =
    courseData.image instanceof File ||
    (courseData.modules &&
      courseData.modules.some((module) => module.content instanceof File));
  if (hasFiles) {
    const formData = new FormData();
    formData.append("title", courseData.title);
    formData.append("description", courseData.description);
    formData.append("price", parseFloat(courseData.price).toString());
    if (courseData.image instanceof File) {
      formData.append("image", courseData.image);
    } else if (typeof courseData.image === "string") {
      formData.append("image", courseData.image);
    }
    const modulesForUpload = [];
    if (Array.isArray(courseData.modules)) {
      for (let i = 0; i < courseData.modules.length; i++) {
        const module = courseData.modules[i];
        const cleanModule = { ...module };
        delete cleanModule.expanded;
        if (module.content instanceof File) {
          formData.append(`moduleFiles-${i}`, module.content);
          cleanModule.content = `moduleFiles-${i}`;
        } else if (
          typeof module.content === "string" ||
          module.content === null
        ) {
          cleanModule.content = module.content || "";
        }
        modulesForUpload.push(cleanModule);
      }
    }
    formData.append("modules", JSON.stringify(modulesForUpload));
    try {
      return await formDataRequest("/courses", "POST", formData);
    } catch (error) {
      console.error("Error creating course:", error);
      throw error;
    }
  } else {
    const prepared = {
      ...courseData,
      price: parseFloat(courseData.price),
      modules: Array.isArray(courseData.modules) ? courseData.modules : [],
    };
    return await apiRequest("/courses", "POST", prepared);
  }
};
export const updateCourse = async (courseId, courseData) => {
  const hasFiles =
    courseData.image instanceof File ||
    (courseData.modules &&
      courseData.modules.some(
        (module) =>
          module.content instanceof File ||
          (Array.isArray(module.content) &&
            module.content.some((item) => item instanceof File)),
      ));
  let numericPrice;
  if (typeof courseData.price === "number") {
    numericPrice = courseData.price;
  } else if (typeof courseData.price === "string" && courseData.price.trim()) {
    const cleanPriceStr = courseData.price.replace(/\([^)]*\)/g, "").trim();
    numericPrice = parseFloat(cleanPriceStr);
  } else {
    try {
      const existingCourse = await getCourseById(courseId);
      numericPrice = existingCourse.price;
    } catch (error) {
      console.error("Error fetching existing course for price:", error);
      numericPrice = 0;
    }
  }
  if (isNaN(numericPrice) || numericPrice < 0) {
    throw new Error("Price must be a positive number");
  }
  if (hasFiles) {
    const formData = new FormData();
    if (courseData.title) formData.append("title", courseData.title);
    if (courseData.description)
      formData.append("description", courseData.description);
    formData.append("price", numericPrice.toString());
    if (courseData.image instanceof File) {
      formData.append("image", courseData.image);
    } else if (courseData.image && typeof courseData.image === "string") {
      formData.append("image", courseData.image);
    }
    let modulesArray = [];
    if (Array.isArray(courseData.modules)) {
      modulesArray = courseData.modules;
    } else if (courseData.modules && typeof courseData.modules === "object") {
      modulesArray = [courseData.modules];
    }
    const modulesForUpload = modulesArray.map((module, index) => {
      const { expanded, ...cleanModule } = module;
      if (module.content instanceof File) {
        formData.append(`moduleFiles-${index}`, module.content);
        const { content, ...moduleWithoutContent } = cleanModule;
        return {
          ...moduleWithoutContent,
          fileIndex: index,
        };
      } else if (
        Array.isArray(module.content) &&
        module.content.some((item) => item instanceof File)
      ) {
        module.content.forEach((file, fileIndex) => {
          if (file instanceof File) {
            formData.append(`moduleFiles-${index}-${fileIndex}`, file);
          }
        });
        const { content, ...moduleWithoutContent } = cleanModule;
        return {
          ...moduleWithoutContent,
          fileIndex: index,
        };
      }
      return cleanModule;
    });
    const modulesJson = JSON.stringify(modulesForUpload);
    formData.append("modules", modulesJson);
    for (const pair of formData.entries()) {
    }
    try {
      return await formDataRequest(`/courses/${courseId}`, "PUT", formData);
    } catch (error) {
      console.error("Error updating course:", error);
      console.error("Error details:", error.message);
      throw error;
    }
  } else {
    const dataToSend = {
      ...courseData,
      price: numericPrice,
      modules: Array.isArray(courseData.modules)
        ? courseData.modules.map((module) => {
            const { expanded, ...cleanModule } = module;
            return cleanModule;
          })
        : undefined,
    };
    return await apiRequest(`/courses/${courseId}`, "PUT", dataToSend);
  }
};
export const deleteCourse = async (courseId) => {
  return await apiRequest(`/courses/${courseId}`, "DELETE");
};
export const addModuleToCourse = async (courseId, moduleData) => {
  if (moduleData.content instanceof File) {
    const formData = new FormData();
    formData.append("title", moduleData.title);
    formData.append("type", moduleData.type);
    formData.append("order", moduleData.order || 0);
    formData.append("file", moduleData.content);
    return await formDataRequest(
      `/courses/${courseId}/modules`,
      "POST",
      formData,
    );
  } else {
    return await apiRequest(`/courses/${courseId}/modules`, "POST", moduleData);
  }
};
export const updateModuleInCourse = async (courseId, moduleId, moduleData) => {
  if (moduleData.content instanceof File) {
    const formData = new FormData();
    formData.append("title", moduleData.title);
    formData.append("type", moduleData.type);
    formData.append("order", moduleData.order || 0);
    formData.append("file", moduleData.content);
    return await formDataRequest(
      `/courses/${courseId}/modules/${moduleId}`,
      "PUT",
      formData,
    );
  } else {
    return await apiRequest(
      `/courses/${courseId}/modules/${moduleId}`,
      "PUT",
      moduleData,
    );
  }
};
export const deleteModuleFromCourse = async (courseId, moduleId) => {
  return await apiRequest(`/courses/${courseId}/modules/${moduleId}`, "DELETE");
};
export const createStripeCheckoutForCourse = async (courseId) => {
  try {
    const response = await apiRequest("/stripe/course-payment-intent", "POST", {
      courseId,
    });
    return response;
  } catch (error) {
    console.error(
      `Error creating Stripe checkout for course ${courseId}:`,
      error,
    );
    if (error.message && error.message.includes("already own")) {
      const ownedError = new Error(error.message);
      ownedError.code = "ALREADY_OWNED";
      throw ownedError;
    }
    throw error;
  }
};
const getAuthToken = () => {
  try {
    let userDataString = sessionStorage.getItem("patentpionner_user");
    if (!userDataString) {
      userDataString = localStorage.getItem("patentpionner_user");
    }
    let userData = null;
    if (userDataString) {
      userData = JSON.parse(userDataString);
    }
    if (userData) {
      return (
        userData.accessToken ||
        userData.token ||
        (userData.user && userData.user.accessToken) ||
        (userData.user && userData.user.token)
      );
    }
    return null;
  } catch (error) {
    console.error("Error parsing user data:", error);
    return null;
  }
};
export const downloadCourse = async (courseId, onProgress = () => {}) => {
  try {
    const token = getAuthToken();
    if (!token) {
      throw new Error("Authentication required. Please log in.");
    }
    const CHUNK_SIZE = 1024 * 1024;
    const headResponse = await fetch(
      `${API_BASE_URL}/courses/${courseId}/download`,
      {
        method: "HEAD",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        credentials: "include",
      },
    );
    if (!headResponse.ok) {
      throw new Error("Failed to get course information");
    }
    const contentLength = headResponse.headers.get("Content-Length");
    const totalSize = contentLength ? parseInt(contentLength, 10) : 0;
    if (!totalSize) {
      throw new Error("Could not determine file size for chunked download");
    }
    const contentDisposition = headResponse.headers.get("Content-Disposition");
    let filename = `course-${courseId}.zip`;
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1];
      }
    }
    const chunks = Math.ceil(totalSize / CHUNK_SIZE);
    const chunksData = new Array(chunks);
    let downloadedSize = 0;
    const concurrencyLimit = 3;
    let activeDownloads = 0;
    let nextChunkIndex = 0;
    return new Promise((resolve, reject) => {
      const downloadNextChunk = async () => {
        if (nextChunkIndex >= chunks) {
          return;
        }
        const chunkIndex = nextChunkIndex++;
        activeDownloads++;
        try {
          const start = chunkIndex * CHUNK_SIZE;
          const end = Math.min(start + CHUNK_SIZE - 1, totalSize - 1);
          const headers = {
            Authorization: `Bearer ${token}`,
            Range: `bytes=${start}-${end}`,
          };
          const response = await fetch(
            `${API_BASE_URL}/courses/${courseId}/download`,
            {
              method: "GET",
              headers,
              credentials: "include",
            },
          );
          if (!response.ok && response.status !== 206) {
            throw new Error(
              `Failed to download chunk ${chunkIndex + 1}. Status: ${response.status}`,
            );
          }
          const buffer = await response.arrayBuffer();
          const chunk = new Uint8Array(buffer);
          chunksData[chunkIndex] = chunk;
          downloadedSize += chunk.length;
          const progress = Math.round((downloadedSize / totalSize) * 100);
          onProgress(progress, downloadedSize, totalSize);
          if (downloadedSize >= totalSize) {
            finishDownload();
          }
        } catch (error) {
          console.error(`Error downloading chunk ${chunkIndex + 1}:`, error);
          reject(
            new Error(
              `Failed to download chunk ${chunkIndex + 1}: ${error.message}`,
            ),
          );
        } finally {
          activeDownloads--;
          if (nextChunkIndex < chunks) {
            downloadNextChunk();
          } else if (activeDownloads === 0) {
            if (downloadedSize < totalSize) {
              console.warn(
                `Download incomplete: ${downloadedSize}/${totalSize} bytes`,
              );
            }
          }
        }
      };
      const finishDownload = () => {
        try {
          let totalLength = 0;
          for (const chunk of chunksData) {
            if (chunk) {
              totalLength += chunk.length;
            }
          }
          const combinedData = new Uint8Array(totalLength);
          let position = 0;
          for (const chunk of chunksData) {
            if (chunk) {
              combinedData.set(chunk, position);
              position += chunk.length;
            }
          }
          const blob = new Blob([combinedData]);
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.style.display = "none";
          a.href = url;
          a.download = filename;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
          onProgress(100, totalLength, totalLength);
          setTimeout(() => {
            onProgress(100, totalLength, totalLength);
          }, 100);
          resolve({ success: true, filename });
        } catch (error) {
          console.error("Error finishing download:", error);
          reject(new Error(`Failed to complete download: ${error.message}`));
        }
      };
      for (let i = 0; i < Math.min(concurrencyLimit, chunks); i++) {
        downloadNextChunk();
      }
    });
  } catch (error) {
    console.error("Download error:", error);
    throw new Error(error.message || "Failed to download course");
  }
};
export const downloadModuleFile = async (
  courseId,
  moduleId,
  onProgress = () => {},
) => {
  try {
    const token = getAuthToken();
    if (!token) {
      throw new Error("Authentication required. Please log in.");
    }
    const CHUNK_SIZE = 1024 * 1024;
    const headResponse = await fetch(
      `${API_BASE_URL}/courses/${courseId}/modules/${moduleId}/download`,
      {
        method: "HEAD",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        credentials: "include",
      },
    );
    if (!headResponse.ok) {
      throw new Error("Failed to get module file information");
    }
    const contentLength = headResponse.headers.get("Content-Length");
    const totalSize = contentLength ? parseInt(contentLength, 10) : 0;
    const contentDisposition = headResponse.headers.get("Content-Disposition");
    let filename = `module-${moduleId}.file`;
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1];
      }
    }
    const chunksCount = Math.ceil(totalSize / CHUNK_SIZE);
    const chunksData = new Array(chunksCount);
    let downloadedSize = 0;
    const concurrencyLimit = 3;
    let activeDownloads = 0;
    let nextChunkIndex = 0;
    return new Promise((resolve, reject) => {
      const downloadNextChunk = async () => {
        if (nextChunkIndex >= chunksCount) {
          return;
        }
        const chunkIndex = nextChunkIndex++;
        activeDownloads++;
        try {
          const start = chunkIndex * CHUNK_SIZE;
          const end = Math.min(start + CHUNK_SIZE - 1, totalSize - 1);
          const headers = {
            Authorization: `Bearer ${token}`,
            Range: `bytes=${start}-${end}`,
          };
          const response = await fetch(
            `${API_BASE_URL}/courses/${courseId}/modules/${moduleId}/download`,
            {
              method: "GET",
              headers,
              credentials: "include",
            },
          );
          if (!response.ok && response.status !== 206) {
            throw new Error(
              `Failed to download module chunk ${chunkIndex + 1}. Status: ${response.status}`,
            );
          }
          const buffer = await response.arrayBuffer();
          const chunk = new Uint8Array(buffer);
          chunksData[chunkIndex] = chunk;
          downloadedSize += chunk.length;
          const progress = Math.round((downloadedSize / totalSize) * 100);
          onProgress(progress, downloadedSize, totalSize);
          if (downloadedSize >= totalSize) {
            finishDownload();
          }
        } catch (error) {
          console.error(
            `Error downloading module chunk ${chunkIndex + 1}:`,
            error,
          );
          reject(
            new Error(
              `Failed to download module chunk ${chunkIndex + 1}: ${error.message}`,
            ),
          );
        } finally {
          activeDownloads--;
          if (nextChunkIndex < chunksCount) {
            downloadNextChunk();
          } else if (activeDownloads === 0) {
            if (downloadedSize < totalSize) {
              console.warn(
                `Module download incomplete: ${downloadedSize}/${totalSize} bytes`,
              );
            }
          }
        }
      };
      const finishDownload = () => {
        try {
          let totalLength = 0;
          for (const chunk of chunksData) {
            if (chunk) {
              totalLength += chunk.length;
            }
          }
          const combinedData = new Uint8Array(totalLength);
          let position = 0;
          for (const chunk of chunksData) {
            if (chunk) {
              combinedData.set(chunk, position);
              position += chunk.length;
            }
          }
          const blob = new Blob([combinedData]);
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.style.display = "none";
          a.href = url;
          a.download = filename;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
          onProgress(100, totalLength, totalLength);
          setTimeout(() => {
            onProgress(100, totalLength, totalLength);
          }, 100);
          resolve({ success: true, filename });
        } catch (error) {
          console.error("Error finishing module download:", error);
          reject(
            new Error(`Failed to complete module download: ${error.message}`),
          );
        }
      };
      for (let i = 0; i < Math.min(concurrencyLimit, chunksCount); i++) {
        downloadNextChunk();
      }
    });
  } catch (error) {
    console.error("Module download error:", error);
    throw new Error(error.message || "Failed to download module file");
  }
};
