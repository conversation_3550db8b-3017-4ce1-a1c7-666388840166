import apiRequest from "./api";
export const createPurchase = async (purchaseData) => {
  return await apiRequest("/purchases", "POST", purchaseData);
};
export const getAllPurchases = async (page = 1, limit = 6) => {
  try {
    const userData =
      localStorage.getItem("patentpionner_user") ||
      sessionStorage.getItem("patentpionner_user");
    let isAdmin = false;
    if (userData) {
      const user = JSON.parse(userData);
      isAdmin =
        user.isAdmin === true || user.role === "Admin" || user.role === "admin";
    }
    if (!isAdmin) {
      console.warn("Non-admin user attempting to fetch all purchases");
      return { purchases: [], pagination: null };
    }
    const data = await apiRequest(`/purchases?page=${page}&limit=${limit}`);
    return data;
  } catch (error) {
    if (
      error.message &&
      error.message.includes("Not authorized: Admin access required")
    ) {
      console.error("Admin access required for this operation");
      return { purchases: [], pagination: null };
    }
    console.error("Error fetching all purchases:", error);
    return { purchases: [], pagination: null };
  }
};
export const getAllPurchasesForDashboard = async () => {
  try {
    const userData =
      localStorage.getItem("patentpionner_user") ||
      sessionStorage.getItem("patentpionner_user");
    let isAdmin = false;
    if (userData) {
      const user = JSON.parse(userData);
      isAdmin =
        user.isAdmin === true || user.role === "Admin" || user.role === "admin";
    }
    if (!isAdmin) {
      console.warn("Non-admin user attempting to fetch all purchases count");
      return { totalCount: 0 };
    }
    const data = await apiRequest("/purchases/dashboard");
    return data;
  } catch (error) {
    if (
      error.message &&
      error.message.includes("Not authorized: Admin access required")
    ) {
      console.error("Admin access required for this operation");
      return { totalCount: 0 };
    }
    console.error("Error fetching total purchase count:", error);
    return { totalCount: 0 };
  }
};
export const getUserPurchases = async (page = 1, limit = 6) => {
  try {
    const data = await apiRequest(`/purchases/my?page=${page}&limit=${limit}`);
    return data;
  } catch (error) {
    console.error("Error fetching user purchases:", error);
    return { purchases: [], pagination: null };
  }
};
export const getUserPurchasesForDashboard = async () => {
  try {
    const data = await apiRequest("/purchases/my/dashboard");
    return data;
  } catch (error) {
    console.error("Error fetching user purchase count:", error);
    return { totalCount: 0 };
  }
};
export const getPurchaseById = async (purchaseId) => {
  return await apiRequest(`/purchases/${purchaseId}`);
};
