import apiRequest from "./api";
export const getUsers = async (page = 1, limit = 6) => {
  return await apiRequest(`/users?page=${page}&limit=${limit}`);
};
export const getUsersForDashboard = async () => {
  return await apiRequest("/users/dashboard");
};
export const updateUserStatus = async (userId, statusData) => {
  return await apiRequest(`/users/${userId}/status`, "PATCH", statusData);
};
export const getUserById = async (userId) => {
  return await apiRequest(`/users/${userId}`);
};
export const updateUser = async (userId, userData) => {
  return await apiRequest(`/users/${userId}`, "PATCH", userData);
};
export const deleteUser = async (userId) => {
  return await apiRequest(`/users/${userId}`, "DELETE");
};
export const getUserProfile = async () => {
  return await apiRequest("/users/profile");
};
export const updateUserProfile = async (userData) => {
  return await apiRequest("/users/profile", "PATCH", userData);
};
export const updateUsername = async (usernameData) => {
  return await apiRequest("/users/update-username", "PATCH", usernameData);
};
export const updatePassword = async (passwordData) => {
  return await apiRequest("/users/change-password", "PATCH", passwordData);
};
