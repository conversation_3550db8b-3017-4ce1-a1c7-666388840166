import { createGlobalStyle } from "styled-components";
export const theme = {
  colors: {
    primary: "#009F9F", // Updated to teal color from landing page
    secondary: "#007A7A", // Darker teal for secondary
    success: "#4caf50",
    danger: "#f44336",
    warning: "#ff9800",
    info: "#00bcd4",
    light: "#f5f5f5",
    dark: "#212121",
    background: "#f9fbfd",
    text: "#333333",
    border: "#e0e0e0",
    teal: "#009F9F", // Adding explicit teal color
    darkTeal: "#007A7A", // Darker shade for hover states
  },
  spacing: {
    xs: "0.25rem",
    sm: "0.5rem",
    md: "1rem",
    lg: "1.5rem",
    xl: "2rem",
  },
  fontSizes: {
    xs: "0.75rem",
    sm: "0.875rem",
    md: "1rem",
    lg: "1.25rem",
    xl: "1.5rem",
    xxl: "2rem",
  },
  borderRadius: {
    sm: "0.25rem",
    md: "0.5rem",
    lg: "1rem",
  },
  shadows: {
    sm: "0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)",
    md: "0 4px 6px rgba(0,0,0,0.1)",
    lg: "0 10px 15px rgba(0,0,0,0.1)",
  },
  transition: "0.3s ease",
};
const GlobalStyles = createGlobalStyle`
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }
  html, body {
    width: 100%;
    height: 100%;
    overflow-x: hidden;
  }
  body {
    font-family: 'Roboto', 'Segoe UI', 'Arial', sans-serif;
    background-color: #ffffff; /* White background like landing page */
    color: ${({ theme }) => theme.colors.text};
    line-height: 1.5;
  }
  #root {
    width: 100%;
    height: 100%;
  }
  a {
    text-decoration: none;
    color: ${({ theme }) => theme.colors.primary};
  }
  button {
    cursor: pointer;
  }
  input, button, select, textarea {
    font-family: Figtree;
  }
  .full-width {
    width: 100%;
  }
`;
export default GlobalStyles;
