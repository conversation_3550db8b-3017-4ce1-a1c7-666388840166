import styled from "styled-components";
import { Link } from "react-router-dom";
export const Button = styled.button`
  background-color: ${({ theme, variant }) =>
    theme.colors[variant || "primary"]};
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  padding: ${({ theme, size }) =>
    size === "small"
      ? `${theme.spacing.xs} ${theme.spacing.md}`
      : size === "large"
        ? `${theme.spacing.md} ${theme.spacing.xl}`
        : `${theme.spacing.sm} ${theme.spacing.lg}`};
  font-size: ${({ size }) =>
    size === "small" ? "0.875rem" : size === "large" ? "1.125rem" : "1rem"};
  font-weight: 500;
  cursor: pointer;
  transition: background-color ${({ theme }) => theme.transition};
  &:hover {
    background-color: ${({ theme, variant }) => {
      const color = variant || "primary";
      return color === "primary"
        ? theme.colors.darkTeal
        : color === "secondary"
          ? "#005f5f"
          : color === "success"
            ? "#43a047"
            : color === "danger"
              ? "#d32f2f"
              : color === "warning"
                ? "#f57c00"
                : color === "info"
                  ? "#0097a7"
                  : theme.colors.darkTeal;
    }};
    color: white;
  }
  &:disabled {
    background-color: #bdbdbd;
    cursor: not-allowed;
  }
`;
export const Card = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: ${({ noPadding }) => (noPadding ? "0" : "1rem")};
  box-shadow: ${({ theme }) => theme.shadows.md};
`;
export const Input = styled.input`
  width: 100%;
  padding: ${({ theme }) => theme.spacing.md};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: 1rem;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  transition: border-color ${({ theme }) => theme.transition};
  background-color: white;
  color: ${({ theme }) => theme.colors.dark};
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;
export const Select = styled.select`
  width: 100%;
  padding: ${({ theme }) => theme.spacing.md};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: 1rem;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  transition: border-color ${({ theme }) => theme.transition};
  background-color: white;
  color: ${({ theme }) => theme.colors.dark};
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;
export const Textarea = styled.textarea`
  width: 100%;
  padding: ${({ theme }) => theme.spacing.md};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: 1rem;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  min-height: 100px;
  transition: border-color ${({ theme }) => theme.transition};
  background-color: white;
  color: ${({ theme }) => theme.colors.dark};
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;
export const FormGroup = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
export const Label = styled.label`
  display: block;
  margin-bottom: ${({ theme }) => theme.spacing.xs};
  font-weight: 500;
`;
export const ErrorText = styled.p`
  color: ${({ theme }) => theme.colors.danger};
  font-size: 0.875rem;
  margin-top: ${({ theme }) => theme.spacing.xs};
`;
export const Grid = styled.div`
  display: grid;
  grid-template-columns: repeat(${({ columns }) => columns || 4}, 1fr);
  grid-gap: ${({ theme, gap }) => theme.spacing[gap || "md"]};
  @media (max-width: 1200px) {
    grid-template-columns: repeat(3, 1fr);
  }
  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
`;
export const Column = styled.div`
  grid-column: span 1;
  @media (max-width: 768px) {
    grid-column: span 1;
  }
  @media (max-width: 480px) {
    grid-column: span 1;
  }
`;
export const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
export const Th = styled.th`
  text-align: left;
  padding: ${({ theme }) => theme.spacing.md};
  border-bottom: 2px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.light};
`;
export const Td = styled.td`
  padding: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;
export const NavLink = styled(Link)`
  color: ${({ theme, active }) =>
    active ? theme.colors.primary : theme.colors.text};
  text-decoration: none;
  font-weight: ${({ active }) => (active ? "600" : "400")};
  display: flex;
  align-items: center;
  padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.md}`};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: background-color ${({ theme }) => theme.transition};
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
  svg {
    margin-right: ${({ theme }) => theme.spacing.sm};
  }
`;
export const PageTitle = styled.h1`
  font-size: 1.75rem;
  color: #009F9F; /* Teal color for page titles */
  font-weight: 500;
  @media (max-width: 768px) {
    margin-top: 0px;
    padding-top: 0px;
  }
`;
export const SectionTitle = styled.h2`
  font-size: 1.375rem;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  color: #009F9F; /* Teal color for section titles */
  font-weight: 500;
`;
export const Badge = styled.span`
  background-color: ${({ theme, variant }) =>
    theme.colors[variant || "primary"]};
  color: white;
  padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.sm}`};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: 0.75rem;
  font-weight: 500;
`;
export const ContentWrapper = styled.div`
  padding: ${({ theme }) => theme.spacing.xl};
`;
export const SearchInput = styled.input`
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: 0.875rem;
  width: ${({ width }) => width || "250px"};
  background-color: white;
  color: #000000;
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
  &::placeholder {
    color: ${({ theme }) => theme.colors.textLight};
  }
`;
export const Form = styled.form`
  width: 100%;
  display: flex;
  flex-direction: column;
`;
