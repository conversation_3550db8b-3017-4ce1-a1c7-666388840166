# Server Configuration
PORT=5000
CLIENT_URL=http://localhost:3000/

# Database Configuration
DATABASE_URI=mongodb://localhost:27017/patent-pioneer

# JWT Configuration
JWT_ACCESS_SECRET=your_jwt_access_secret
JWT_EMAIL_SECRET=your_jwt_email_secret
JWT_SIGN_UP_EMAIL_SECRET=your_jwt_signup_email_secret
JWT_REFRESH_SECRET=your_jwt_refresh_secret
JWT_EXPIRY=7d

# SendGrid Configuration
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_ACCOUNT_EMAIL=<EMAIL>
SENDGRID_TEM_ID_FOR_VERIFY_EMAIL=d58a7e344ca741e984e8c224191061d5
SENDGRID_TEM_ID_FOR_FORGOT_EMAIL=your_forgot_password_template_id

# User Encryption
USER_ENCRYPT_KEY=your_user_encrypt_key

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret
