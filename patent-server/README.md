# NestJS Enterprise Boilerplate

This repository provides a scalable, production-ready boilerplate for building enterprise-grade applications with NestJS. It includes essential modules and integrations for robust authentication, user management, and infrastructure support, tailored for applications requiring security, performance, and modularity.

---

## Features

1. **Authentication Module**

   - Provides endpoints for **Sign-Up**, **Sign-In**, **Forgot Password**, and **Email/OTP Verification**.
   - Sends email verification on signup via a verification link or OTP.
   - Includes password reset functionality in the Forgot Password flow.
   - **User Schema**:
     - `First Name`: User’s first name.
     - `Last Name`: User’s last name.
     - `Picture`: Profile picture URL.
     - `Email`: Unique email address for login.
     - `Username`: User’s unique username.
     - `Role`: User’s role default is user.
     - `Password`: Password, securely hashed for protection.
     - `SSO Source`: User’s sso login type like email, google etc.
     - `SSO ID`: User’s sso login type like google id etc.
   - Uses **JWT** tokens for secure session management.

2. **User Module**

   - Endpoints for updating user profile, picture, and password.
   - Allows profile updates, supporting changes to `profile` and `picture`.
   - Secure password update feature with hashed storage.

3. **Protected Routes**

   - Secures routes with JWT-based authorization.
   - Utilizes **JWT Guards** to protect endpoints, ensuring only authenticated users can access them.

4. **Logger Middleware**

   - Middleware that logs incoming requests and responses for debugging and monitoring.
   - Logs critical information about request methods, URLs, and status codes.

5. **Throttling**

   - Rate limiting configured at **15 requests per minute** to prevent abuse.
   - Applies throttling to all routes, configurable for different endpoints.

6. **CORS (Cross-Origin Resource Sharing)**

   - Configured to control cross-origin requests based on security requirements.
   - Enables specific domains, methods, and headers as needed for API consumption.

7. **Email Service - SendGrid**

   - Integrates **SendGrid** for sending emails for signup verification and password recovery.
   - Configurable email templates for a branded and professional appearance.

8. **Testing**

   - Includes **unit** and **integration test** cases for core modules and controllers.
   - Uses Jest and Supertest for validating authentication flows, route protection, and other functionalities.

9. **S3 Integration**

   - Integrates Amazon S3 for storing and retrieving user profile pictures.
   - Configurable S3 bucket and access policies for managing user-generated content.

10. **MongoDB**

    - **Database**: MongoDB as the primary database, accessed through Mongoose ORM.
    - Provides flexible data modeling for complex data structures and user information.

11. **Docker**

    - **Dockerized Application**: Includes a Dockerfile for containerizing the application.
    - **Docker Compose**: A `docker-compose.yml` file for easy setup and management of application dependencies.
    - Ensures consistency in local development, testing, and production deployment.

12. **CI/CD Pipeline for Docker Builds**
    - Configured **CI/CD pipeline** using GitHub Actions for automated Docker builds and pushes.
    - Facilitates deployment to container registries or Kubernetes environments.

---

## Getting Started

### Prerequisites

- **Node.js** (v14 or later)
- **Nest CLI** installed globally: `yarn install -g @nestjs/cli`
- **Docker** (for containerization)

### Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/WaqasSL/nest-boilerplate.git
   cd nestjs-boilerplate
   ```

2. Install dependencies:

   ```bash
   yarn install
   ```

3. Create a `.env` file and configure the following variables:

   ```dotenv
   PORT=4800
   CLIENT_URL=http://client.url
   DATABASE_URI=mongodb://localhost:27017/yourdb
   JWT_SECRET=yourSecret
   JWT_EMAIL_SECRET=yourEmailSecret
   JWT_SIGN_UP_EMAIL_SECRET=yourSingupEmailSecret
   JWT_REFRESH_SECRET=yourRefreshSecret
   SENDGRID_API_KEY=yourSendGridApiKey
   SENDGRID_ACCOUNT_EMAIL=<EMAIL>
   SENDGRID_TEM_ID_FOR_VERIFY_EMAIL=templateId
   SENDGRID_TEM_ID_FOR_FORGOT_EMAIL=templateId
   USER_ENCRYPT_KEY=userEncryption
   AWS_S3_BUCKET=yourS3BucketName
   AWS_ACCESS_KEY_ID=yourAwsAccessKeyId
   AWS_SECRET_ACCESS_KEY=yourAwsSecretAccessKey
   ```

4. Run the application:

   ```bash
   yarn run start:dev
   ```

### Docker

To run the application using Docker:

1. Build the Docker image:

   ```bash
   docker build -t nestjs-enterprise-boilerplate .
   ```

2. Start the container with Docker Compose:

   ```bash
   docker-compose up -d
   ```

### Running Tests

Run unit and integration tests:

```bash
yarn run test
```

---

## Project Structure

```
src
├── app
      ├── auth                # Authentication Module
      ├── user                # User Management Module
├── role                # Role-Based Access Control
├── middleware          # Custom Middleware (e.g., Logger)
├── services            # Global Service Integration
├── config              # Configuration Files
├── main.ts             # Application Entry Point
```

---

## Contributing

Contributions are welcome! Please submit a pull request or open an issue for bug reports and feature requests.

---

## Collaborator

**[Muhammad Irfan Ali](https://github.com/MIrfanAliSolutions)** - Original creator, Full Stack Software Engineer, and architect of the boilerplate.

---

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for more details.

---

This NestJS Enterprise Boilerplate provides a solid foundation for developing enterprise-level applications, with security, scalability, and modularity in mind.
