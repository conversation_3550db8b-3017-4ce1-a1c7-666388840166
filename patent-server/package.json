{"name": "nest-boilerplate", "version": "0.0.1", "description": "", "author": "<PERSON>", "private": true, "license": "MIT License", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "prepare": "husky install", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "seed": "ts-node -r tsconfig-paths/register src/config/seeders/data-seeder.ts"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^10.1.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^8.0.7", "@sendgrid/mail": "^8.1.4", "@types/archiver": "^6.0.3", "@types/bcrypt": "^5.0.2", "adm-zip": "^0.5.16", "archiver": "^7.0.1", "aws-sdk": "^2.1692.0", "bcrypt": "^5.1.1", "body-parser": "^2.2.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cloudinary": "^2.6.0", "dotenv": "^16.4.5", "husky": "^9.1.6", "mongoose": "^8.8.2", "multer": "^1.4.5-lts.2", "pdfkit": "^0.17.0", "react-quill": "^2.0.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "stripe": "^18.0.0", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/pdfkit": "^0.13.9", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.14.0", "@typescript-eslint/parser": "^8.14.0", "eslint": "^9.15.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-unused-imports": "^4.1.4", "jest": "^29.5.0", "lint-staged": "^15.2.10", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.6.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^@app/(.*)$": "<rootDir>/$1", "^@common/(.*)$": "<rootDir>/../common/src/$1", "^@config/(.*)$": "<rootDir>/../config/src/$1", "^@database/(.*)$": "<rootDir>/../database/src/$1", "^@modules/(.*)$": "<rootDir>/../modules/$1"}}}