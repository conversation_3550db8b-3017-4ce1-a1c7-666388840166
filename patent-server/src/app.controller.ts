import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

import { AppService } from './app.service';

@ApiTags('App') // Grouping under "App"
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get('health') // GET /health
  @ApiOperation({ summary: 'Get Application Status' }) // API summary
  @ApiResponse({ status: 200, description: 'Application is running successfully.' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  appStatus(): string {
    return this.appService.getStatus();
  }
}
