import { RequestLoggerMiddleware } from '@app/common/middleware/request-logger.middleware';
import { MiddlewareConsumer, Module } from '@nestjs/common';
import { AuthModule } from 'app/auth/auth.module';
import { BookModule } from 'app/book/book.module';
import { ContactModule } from 'app/contact/contact.module';
import { CourseModule } from 'app/course/course.module';
import { FileUploadModule } from 'app/file-upload/file-upload.module';
import { InsightModule } from 'app/insight/insight.module';
import { PurchaseModule } from 'app/purchase/purchase.module';
import { StripeModule } from 'app/stripe/stripe.module';
import { RepositoryModule } from 'repositories/repository.module';

import { UserModule } from './app/user/user.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from './database/database.module';

@Module({
  imports: [
    RepositoryModule.forRoot(), 
    DatabaseModule, 
    AuthModule, 
    UserModule,
    CourseModule,
    PurchaseModule,
    FileUploadModule,
    StripeModule,
    BookModule,
    ContactModule,
    InsightModule
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestLoggerMiddleware).forRoutes('*'); // Apply globally
  }
}
