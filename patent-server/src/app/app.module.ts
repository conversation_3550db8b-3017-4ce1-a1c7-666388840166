import { envVariables } from '@app/config';
import { S3Module } from '@app/services/s3/s3.module';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

import { FileUploadModule } from './file-upload/file-upload.module';
// ...other imports...

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MongooseModule.forRoot(envVariables.DATABASE_URI),
    S3Module,
    FileUploadModule,
    // ...other modules...
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}