import {
  ACCOUNT_ALREADY_VERIFIED,
  ACCOUNT_EXISTS,
  ACCOUNT_NOT_EXIST_BY_EMAIL,
  CREDENTIALS_NOT_VALID,
  EMAIL_VERIFICATION_SENT,
  FORGOT_EMAIL_SENT_MESSAGE,
  ONE_TIME_TOKEN_EXPIRED,
  PASSWORD_CHANGED_MESSAGE,
} from '@app/constants/auth.constants';
import { ConflictException, BadRequestException, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { Test, TestingModule } from '@nestjs/testing';

import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import {
  CreateUserDto,
  LoginUserDto,
  ResendVerifyEmailDto,
  ForgotPasswordDto,
  ResetPasswordDto,
  VerifyAccountDto,
} from './dto';
import { JwtTokenService } from './jwt/jwt.token.service';
import { SendGridService } from './sendgrid/sendgrid.service';

describe('AuthController', () => {
  let authController: AuthController;
  let authService: AuthService;

  const mockAuthService = {
    registerUser: jest.fn(),
    resendVerifyRegisterUserEmail: jest.fn(),
    login: jest.fn(),
    verifyAccount: jest.fn(),
    forgotPasswordEmail: jest.fn(),
    resetPassword: jest.fn(),
  };

  const mockJwtTokenService = {
    // Mock methods if needed
    createAccessToken: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn().mockImplementation((key: string) => {
      if (key === 'SENDGRID_API_KEY') {
        return 'SG.mock-sendgrid-api-key'; // Mock the value of the SendGrid API key
      }
      return null;
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [JwtModule], // Add JwtModule to the imports array
      controllers: [AuthController],
      providers: [
        { provide: AuthService, useValue: mockAuthService },
        { provide: JwtTokenService, useValue: mockJwtTokenService },
        { provide: ConfigService, useValue: mockConfigService }, // Mock ConfigService
        SendGridService,
      ],
    }).compile();

    authController = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
  });

  describe('register', () => {
    it('should register a new user successfully', async () => {
      const createUserDto: CreateUserDto = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'Password123!',
      };
      mockAuthService.registerUser.mockResolvedValue({ message: EMAIL_VERIFICATION_SENT });

      const response = await authController.register(createUserDto);
      expect(response).toEqual({ message: EMAIL_VERIFICATION_SENT });
    });

    it('should throw an error if email already exists', async () => {
      const createUserDto: CreateUserDto = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'Password123!',
      };
      mockAuthService.registerUser.mockRejectedValue(new ConflictException(ACCOUNT_EXISTS));

      await expect(authController.register(createUserDto)).rejects.toThrowError(ConflictException);
    });
  });

  describe('resendVerifyRegisterUserEmail', () => {
    it('should resend verification email to existing user', async () => {
      const resendVerifyEmailDto: ResendVerifyEmailDto = { email: '<EMAIL>' };
      mockAuthService.resendVerifyRegisterUserEmail.mockResolvedValue({ message: EMAIL_VERIFICATION_SENT });

      const response = await authController.resendVerifyRegisterUserEmail(resendVerifyEmailDto);
      expect(response).toEqual({ message: EMAIL_VERIFICATION_SENT });
    });

    it('should throw an error if email does not exist', async () => {
      const resendVerifyEmailDto: ResendVerifyEmailDto = { email: '<EMAIL>' };
      mockAuthService.resendVerifyRegisterUserEmail.mockRejectedValue(
        new BadRequestException(ACCOUNT_NOT_EXIST_BY_EMAIL),
      );

      await expect(authController.resendVerifyRegisterUserEmail(resendVerifyEmailDto)).rejects.toThrowError(
        BadRequestException,
      );
    });
  });

  describe('login', () => {
    it('should log in successfully with valid credentials', async () => {
      const loginUserDto: LoginUserDto = {
        identifier: '<EMAIL>',
        password: 'Password123!',
      };
      mockAuthService.login.mockResolvedValue({
        user: { email: '<EMAIL>' },
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
      });

      const response = await authController.login(loginUserDto);
      expect(response).toHaveProperty('accessToken');
      expect(response).toHaveProperty('refreshToken');
    });

    it('should throw an error if credentials are invalid', async () => {
      const loginUserDto: LoginUserDto = {
        identifier: '<EMAIL>',
        password: 'WrongPassword123!',
      };
      mockAuthService.login.mockRejectedValue(new UnauthorizedException(CREDENTIALS_NOT_VALID));

      await expect(authController.login(loginUserDto)).rejects.toThrowError(UnauthorizedException);
    });
  });

  describe('verifyAccount', () => {
    it('should verify account successfully', async () => {
      const verifyAccountDto: VerifyAccountDto = { token: 'validToken123' };
      mockAuthService.verifyAccount.mockResolvedValue({
        user: { email: '<EMAIL>' },
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
      });

      const response = await authController.verifyAccount(verifyAccountDto);
      expect(response).toHaveProperty('accessToken');
      expect(response).toHaveProperty('refreshToken');
    });

    it('should throw an error if token is invalid', async () => {
      const verifyAccountDto: VerifyAccountDto = { token: 'invalidToken123' };
      mockAuthService.verifyAccount.mockRejectedValue(new BadRequestException(ACCOUNT_NOT_EXIST_BY_EMAIL));

      await expect(authController.verifyAccount(verifyAccountDto)).rejects.toThrowError(BadRequestException);
    });

    it('should throw an error if account is already verified', async () => {
      const verifyAccountDto: VerifyAccountDto = { token: 'alreadyVerifiedToken123' };
      mockAuthService.verifyAccount.mockRejectedValue(new BadRequestException(ACCOUNT_ALREADY_VERIFIED));

      await expect(authController.verifyAccount(verifyAccountDto)).rejects.toThrowError(BadRequestException);
    });
  });

  describe('forgotPasswordEmail', () => {
    it('should send reset token successfully', async () => {
      const forgotPasswordDto: ForgotPasswordDto = { email: '<EMAIL>' };
      mockAuthService.forgotPasswordEmail.mockResolvedValue({ message: FORGOT_EMAIL_SENT_MESSAGE });

      const response = await authController.forgotPasswordEmail(forgotPasswordDto);
      expect(response).toEqual({ message: FORGOT_EMAIL_SENT_MESSAGE });
    });

    it('should throw an error if email does not exist', async () => {
      const forgotPasswordDto: ForgotPasswordDto = { email: '<EMAIL>' };
      mockAuthService.forgotPasswordEmail.mockRejectedValue(new NotFoundException(ACCOUNT_NOT_EXIST_BY_EMAIL));

      await expect(authController.forgotPasswordEmail(forgotPasswordDto)).rejects.toThrowError(NotFoundException);
    });
  });

  describe('resetPassword', () => {
    it('should reset password successfully', async () => {
      const resetPasswordDto: ResetPasswordDto = { token: 'validResetToken123', password: 'NewPassword123!' };
      mockAuthService.resetPassword.mockResolvedValue({ message: PASSWORD_CHANGED_MESSAGE });

      const response = await authController.resetPassword(resetPasswordDto);
      expect(response).toEqual({ message: PASSWORD_CHANGED_MESSAGE });
    });

    it('should throw an error if token is invalid', async () => {
      const resetPasswordDto: ResetPasswordDto = { token: 'invalidResetToken123', password: 'NewPassword123!' };
      mockAuthService.resetPassword.mockRejectedValue(new BadRequestException(ONE_TIME_TOKEN_EXPIRED));

      await expect(authController.resetPassword(resetPasswordDto)).rejects.toThrowError(BadRequestException);
    });

    it('should throw an error if password is weak', async () => {
      const resetPasswordDto: ResetPasswordDto = { token: 'validResetToken123', password: '123' };
      mockAuthService.resetPassword.mockRejectedValue(new BadRequestException('Password is too weak'));

      await expect(authController.resetPassword(resetPasswordDto)).rejects.toThrowError(BadRequestException);
    });
  });
});
