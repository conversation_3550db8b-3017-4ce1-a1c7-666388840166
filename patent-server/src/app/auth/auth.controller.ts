import { Body, Controller, HttpCode, Post } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

import { AuthService } from './auth.service';
import {
  CreateUserDto,
  ForgotPasswordDto,
  LoginUserDto,
  ResendVerifyEmailDto,
  ResetPasswordDto,
  VerifyAccountDto,
} from './dto';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('register')
  @HttpCode(201)
  @ApiOperation({ summary: 'Register a new user' })
  @ApiResponse({ status: 201, description: 'User has been registered' })
  async register(@Body() createUserDto: CreateUserDto) 
  {
    return this.authService.registerUser(createUserDto);
  }

  @Post('resend-verify-email')
  @HttpCode(200)
  @ApiOperation({ summary: 'Resend verify email for register a new user' })
  @ApiResponse({ status: 200, description: 'User verify email has been sent' })
  async resendVerifyRegisterUserEmail(@Body() resendVerifyEmail: ResendVerifyEmailDto) {
    return this.authService.resendVerifyRegisterUserEmail(resendVerifyEmail);
  }

  @Post('login')
  @HttpCode(200)
  @ApiOperation({ summary: 'Login a user with email/username and password' })
  @ApiResponse({ status: 200, description: 'User has been logged in' })
  async login(@Body() loginDto: LoginUserDto) {
    return this.authService.login(loginDto);
  }

  // Admin login is now handled by the regular login endpoint

  @Post('verify-account')
  @HttpCode(200)
  @ApiOperation({ summary: 'Verify user account' })
  @ApiResponse({ status: 200, description: 'Account verified and User has been logged in' })
  async verifyAccount(@Body() verifyAccountDto: VerifyAccountDto) {
    return this.authService.verifyAccount(verifyAccountDto);
  }

  @Post('forgot-password')
  @HttpCode(200)
  @ApiOperation({ summary: 'Send password reset email' })
  @ApiResponse({ status: 200, description: 'Reset token sent to email' })
  async forgotPasswordEmail(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.authService.forgotPasswordEmail(forgotPasswordDto.email);
  }

  @Post('reset-password')
  @HttpCode(200)
  @ApiOperation({ summary: 'Reset user password' })
  @ApiResponse({ status: 200, description: 'Password has been reset' })
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(resetPasswordDto.token, resetPasswordDto.password);
  }
}
