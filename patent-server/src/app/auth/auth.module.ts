import { envVariables } from '@app/config';
import { DatabaseModule } from '@app/database/database.module';
import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtModule, JwtService } from '@nestjs/jwt';

import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtTokenService } from './jwt/jwt.token.service';
import { SendGridService } from './sendgrid/sendgrid.service';

@Module({
  imports: [
    DatabaseModule,
    JwtModule.register({
      global: true,
      secret: envVariables.JWT_ACCESS_SECRET,
      signOptions: { expiresIn: '1h' },
    }),
  ],
  providers: [JwtTokenService, JwtService, AuthService, SendGridService, ConfigService],
  controllers: [AuthController],
})
export class AuthModule {}
