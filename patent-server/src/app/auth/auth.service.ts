import {
  ACCOUNT_ALREADY_VERIFIED,
  ACCOUNT_EXISTS,
  ACCOUNT_EXISTS_UserName,
  ACCOUNT_NOT_EXIST_BY_EMAIL,
  CREDENTIALS_NOT_VALID,
  EMAIL_VERIFICATION_SENT,
  FORGOT_EMAIL_SENT_MESSAGE,
  ONE_TIME_TOKEN_EXPIRED,
  PASSWORD_CHANGED_MESSAGE,
} from "@app/constants/auth.constants";
import { UserStatuses } from "@app/utils";
import { decryptData } from "@app/utils/functionality.utils";
import {
  Injectable,
  ConflictException,
  UnauthorizedException,
  Inject,
  BadRequestException,
  NotFoundException,
} from "@nestjs/common";
import * as bcrypt from "bcrypt";

import {
  CreateUserDto,
  LoginUserDto,
  ResendVerifyEmailDto,
  VerifyAccountDto,
} from "./dto";
import { JwtTokenService } from "./jwt/jwt.token.service";
import { SendGridService } from "./sendgrid/sendgrid.service";

@Injectable()
export class AuthService {
  constructor(
    @Inject("UserRepository") private readonly userRepository,
    private readonly jwtTokenService: JwtTokenService,
    private readonly sendGridService: SendGridService
  ) {}

  async registerUser(createUserDto: CreateUserDto) {
    const { email, password, username } = createUserDto;
    const existingEmail = await this.userRepository.findOne({ email });
    if (existingEmail) {
      throw new ConflictException(ACCOUNT_EXISTS);
    }
    if (username) {
      const existingUsername = await this.userRepository.findOne({ username });
      if (existingUsername) {
        throw new ConflictException(ACCOUNT_EXISTS_UserName);
      }
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    const user = await this.userRepository.create({
      ...createUserDto,
      password: hashedPassword,
      status: UserStatuses.pending,
    });

    const token = await this.jwtTokenService.generateVerifyEmailToken(user);
    await this.sendGridService.verifyEmail(user, token);

    return {
      message: EMAIL_VERIFICATION_SENT,
      user: {
        _id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        status: user.status,
      },
    };
  }

  async resendVerifyRegisterUserEmail(resendVerifyEmail: ResendVerifyEmailDto) {
    const user = await this.userRepository.findOne({
      email: resendVerifyEmail?.email,
    });
    if (!user) {
      throw new BadRequestException(ACCOUNT_NOT_EXIST_BY_EMAIL);
    }

    const token = await this.jwtTokenService.generateVerifyEmailToken(user);
    await this.sendGridService.verifyEmail(user, token);

    return {
      message: EMAIL_VERIFICATION_SENT,
    };
  }

  async login(loginUserDto: LoginUserDto) {
    const { username, email, password } = loginUserDto;
    const passwordToUse = password || (loginUserDto as any).passsword;
    if (!username && !email) {
      throw new BadRequestException(
        "Either email or username must be provided"
      );
    }
    if (!passwordToUse) {
      throw new BadRequestException("Password is required");
    }
    const query: any = { $or: [] };

    if (username) {
      query.$or.push({ username });

      if (username.toLowerCase() === "admin") {
        query.isAdmin = true;
      }
    }

    if (email) {
      query.$or.push({ email });
    }

    const user = await this.userRepository.findOne(query);

    if (!user) {
      throw new BadRequestException(
        "Account not found. Please check your email or username."
      );
    }

    if (user?.status === UserStatuses.deactivated) {
      throw new UnauthorizedException(
        "Your account has been deactivated. Please contact an administrator."
      );
    }

    if (user?.status !== UserStatuses.active) {
      if (
        user?.status === UserStatuses.pending ||
        user?.status === UserStatuses.notVerified
      ) {
        const token = await this.jwtTokenService.generateVerifyEmailToken(user);
        await this.sendGridService.verifyEmail(user, token);
        return {
          message: EMAIL_VERIFICATION_SENT,
          needsVerification: true,
          email: user.email,
        };
      } else {
        throw new UnauthorizedException(
          `Your account status is ${user.status}. Please contact an administrator.`
        );
      }
    }

    const isMatch = await bcrypt.compare(passwordToUse, user.password);
    if (!isMatch) {
      throw new UnauthorizedException(CREDENTIALS_NOT_VALID);
    }

    const { accessToken, refreshToken } =
      await this.jwtTokenService.generateAccessToken(user);

    return {
      user,
      accessToken,
      refreshToken,
    };
  }

  async verifyAccount(verifyAccountDto: VerifyAccountDto) {
    try {
      const payloadToken = await this.jwtTokenService.decretVerifyEmailToken(
        verifyAccountDto?.token
      );

      const userExist = await this.userRepository.findOne({
        email: payloadToken?.user?.email,
      });
      if (!userExist) {
        throw new BadRequestException(ACCOUNT_NOT_EXIST_BY_EMAIL);
      }
      if (userExist?.status === UserStatuses.active) {
        const { accessToken, refreshToken } =
          await this.jwtTokenService.generateAccessToken(userExist);
        return {
          user: userExist,
          accessToken,
          refreshToken,
          message: ACCOUNT_ALREADY_VERIFIED,
        };
      }

      const user = await this.userRepository.findOneAndUpdate(
        { _id: userExist._id },
        { status: UserStatuses.active }
      );

      const { accessToken, refreshToken } =
        await this.jwtTokenService.generateAccessToken(user);

      return {
        user,
        accessToken,
        refreshToken,
      };
    } catch (error) {
      throw error;
    }
  }

  async forgotPasswordEmail(email: string) {
    const user = await this.userRepository.findOne({ email });

    if (!user) {
      throw new NotFoundException(ACCOUNT_NOT_EXIST_BY_EMAIL);
    }

    const token = await this.jwtTokenService.generateForgotPasswordToken(user);

    await this.sendGridService.forgotPasswordEmail(user, token);

    return {
      message: FORGOT_EMAIL_SENT_MESSAGE,
    };
  }

  async resetPassword(token: string, newPassword: string) {
    const payload = await this.jwtTokenService.decryptForgotToken(token);

    let userInfo = await decryptData(payload?.userInfo);
    userInfo = JSON.parse(userInfo);

    const user = await this.userRepository.findOne({ _id: userInfo?.userId });
    if (!user) {
      throw new BadRequestException(ACCOUNT_NOT_EXIST_BY_EMAIL);
    }

    if (userInfo?.encodeURI !== user?.password) {
      throw new BadRequestException(ONE_TIME_TOKEN_EXPIRED);
    }

    const salt = await bcrypt.genSalt(10);
    const hashPassword = await bcrypt.hash(newPassword, salt);

    await this.userRepository.findOneAndUpdate(
      { _id: userInfo?.userId },
      { password: hashPassword }
    );

    return {
      message: PASSWORD_CHANGED_MESSAGE,
    };
  }
}
