import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, MinLength } from 'class-validator';

class ResendVerifyEmailDto {
  @ApiProperty({ example: '<EMAIL>', description: 'User email' })
  @IsEmail()
  email: string;
}

class ForgotPasswordDto {
  @ApiProperty({ example: '<EMAIL>', description: 'User email' })
  @IsEmail()
  email: string;
}

class ResetPasswordDto {
  @ApiProperty({ example: 'randomResetToken123', description: 'Password reset token' })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({ example: 'NewPassword123!', description: 'New password (min 6 chars)' })
  @IsString()
  @MinLength(6)
  password: string;
}

export { ForgotPasswordDto, ResetPasswordDto, ResendVerifyEmailDto };
