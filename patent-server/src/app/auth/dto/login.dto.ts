import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

class LoginUserDto {
  @ApiProperty({
    example: 'johndoe',
    description: 'Username of the user (optional if email is provided)',
    required: false
  })
  @IsString()
  @IsOptional()
  username?: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email of the user (optional if username is provided)',
    required: false
  })
  @IsString()
  @IsOptional()
  email?: string;

  @ApiProperty({
    example: 'password123',
    description: 'Password (at least 6 characters)'
  })
  @IsString()
  @IsNotEmpty()
  password: string;
}

class VerifyAccountDto {
  @ApiProperty({ example: 'password123', description: 'Password (at least 6 characters)' })
  @IsString()
  token: string;
}

export { LoginUserDto, VerifyAccountDto };
