import { Roles, SsoSources, UserStatuses } from '@app/utils';
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsEnum, IsNotEmpty, IsOptional, IsString, MinLength } from 'class-validator';

class CreateUserDto {
  @ApiProperty({ example: 'John', description: 'First name of the user' })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  firstName: string;

  @ApiProperty({ example: 'Doe', description: 'Last name of the user' })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  lastName: string;

  @ApiProperty({ example: 'johndoe', description: 'Username (optional)', required: false })
  @IsString()
  @IsOptional()
  username?: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Email of the user' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ example: 'password123', description: 'Password (at least 6 characters)' })
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({
    example: Roles.user,
    description: 'User role (default: user)',
    enum: Roles,
    default: Roles.user,
    required: false,
  })
  @IsEnum(Roles)
  @IsOptional()
  role: string = Roles.user; // Set default value directly

  @ApiProperty({ example: false, description: 'Whether the user is an admin', required: false })
  @IsOptional()
  isAdmin?: boolean = false; // Default to false
}

class CreateUserSSODto {
  @ApiProperty({ example: 'John', description: 'First name of the user' })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({ example: 'Doe', description: 'Last name of the user' })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({ example: 'johndoe', description: 'Username (optional)', required: false })
  @IsString()
  @IsOptional()
  username?: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Email of the user' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ example: 'password123', description: 'Password (at least 6 characters)' })
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({
    example: Roles.user,
    description: 'User role (default: user)',
    enum: Roles,
    default: Roles.user,
    required: false,
  })
  @IsEnum(Roles)
  @IsOptional()
  role: string = Roles.user; // Set default value directly

  @ApiProperty({
    example: UserStatuses.pending,
    description: 'User status (default: pending)',
    enum: UserStatuses,
    required: false,
  })
  @IsEnum(UserStatuses)
  @IsOptional()
  status?: string;

  @ApiProperty({
    example: SsoSources.email,
    description: 'SSO Provider (if applicable)',
    enum: SsoSources,
    required: false,
  })
  @IsEnum(SsoSources)
  @IsOptional()
  ssoSource?: string;

  @ApiProperty({ example: '**********', description: 'SSO ID (if applicable)', required: false })
  @IsString()
  @IsOptional()
  ssoId?: string;
}

export { CreateUserDto, CreateUserSSODto };
