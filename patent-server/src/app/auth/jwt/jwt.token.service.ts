import { createCipheriv, randomBytes, createHash } from 'crypto';

import { envVariables } from '@app/config';
import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class JwtTokenService {
  constructor(private readonly jwtService: JwtService) {}

  async generateEncryptionPayload(data: any) {
    const userHashed = await this.encryptData(JSON.stringify(data));
    const payload = {
      sub: data?.userId,
      userInfo: userHashed,
    };
    return payload;
  }

  async generateSignUpToken(email: string) {
    const payload = {
      sub: email,
      user: {
        email,
      },
    };
    const token = await this.jwtService.signAsync(payload, {
      secret: envVariables.JWT_SIGN_UP_EMAIL_SECRET,
    });
    return token;
  }

  async generateForgotPasswordToken(user: any) {
    const payload = await this.generateEncryptionPayload({
      userId: user?._id,
      email: user?.email,
      encodeURI: user?.password,
    });
    const token = await this.jwtService.signAsync(payload, {
      secret: envVariables.JWT_EMAIL_SECRET,
    });
    return token;
  }

  async generateAccessToken(user: any) {
    const payload = await this.generateEncryptionPayload({
      userId: user?._id?.toString(),
      email: user?.email,
      role: user?.role || 'user',
      status: user?.status,
    });
    const accessToken = await this.jwtService.signAsync(payload, {
      secret: envVariables.JWT_ACCESS_SECRET,
      expiresIn: '1d',
      algorithm: 'HS512',
    });
    const refreshToken = await this.jwtService.signAsync(payload, {
      secret: envVariables.JWT_REFRESH_SECRET,
      expiresIn: '7d',
      algorithm: 'HS512',
    });
    return { payload, accessToken, refreshToken };
  }

  async generateVerifyEmailToken(user: any) {
    const payload = {
      sub: user?._id,
      user: {
        email: user?.email,
      },
    };
    const token = await this.jwtService.signAsync(payload, {
      secret: envVariables.JWT_EMAIL_SECRET,
      expiresIn: '1d',
      algorithm: 'HS512',
    });
    return token;
  }

  async decretVerifyEmailToken(token: string) {
    return await this.jwtService.verifyAsync(token, {
      secret: envVariables.JWT_EMAIL_SECRET,
    });
  }

  async decryptForgotToken(token: string): Promise<any> {
    return await this.jwtService.verifyAsync(token, {
      secret: envVariables.JWT_EMAIL_SECRET,
    });
  }

  async decryptSignUpToken(token: string): Promise<any> {
    return await this.jwtService.verifyAsync(token, {
      secret: envVariables.JWT_SIGN_UP_EMAIL_SECRET,
    });
  }

  async decryptRefreshToken(token: string): Promise<any> {
    return await this.jwtService.verifyAsync(token, {
      secret: envVariables.JWT_REFRESH_SECRET,
    });
  }

  async encryptData(data: string): Promise<any> {
    const key = createHash('sha256').update(envVariables.USER_ENCRYPT_KEY).digest('base64').substr(0, 32);
    const iv = randomBytes(16);
    const cipher = createCipheriv('aes-256-ctr', key, iv);
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return iv.toString('base64') + ':' + Buffer.from(encrypted, 'hex').toString('base64');
  }
}
