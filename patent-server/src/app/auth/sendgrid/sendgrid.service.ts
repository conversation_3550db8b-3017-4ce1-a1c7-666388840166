import { envVariables } from '@app/config';
import { User } from '@database';
import { Injectable, Logger } from '@nestjs/common';
import * as SendGrid from '@sendgrid/mail';

@Injectable()
export class SendGridService {
  private readonly logger = new Logger(SendGridService.name);

  constructor() {
    SendGrid.setApiKey(envVariables.SENDGRID_API_KEY);
  }

  async verifyEmail(user: User, token: string) {
    const verifyUrl = `${envVariables.CLIENT_URL.replace(/\/$/, '')}/verify-email?token=${encodeURIComponent(token)}`;

    const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <title>Verify Your Email - Patent Pioneer Institute</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          margin: 0;
          padding: 0;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          background-color: #009F9F;
          padding: 20px;
          text-align: center;
        }
        .header h1 {
          color: white;
          margin: 0;
        }
        .content {
          padding: 20px;
          background-color: #f9f9f9;
        }
        .button {
          display: inline-block;
          background-color: #009F9F;
          color: white;
          text-decoration: none;
          padding: 12px 24px;
          border-radius: 4px;
          margin: 20px 0;
          font-weight: bold;
        }
        .footer {
          text-align: center;
          padding: 20px;
          font-size: 12px;
          color: #666;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Verify Your Email Address</h1>
        </div>
        <div class="content">
          <p>Hello ${user?.firstName || ''} ${user?.lastName || ''},</p>
          <p>Thank you for registering with Patent Pioneer Institute. To complete your registration and access all features, please verify your email address by clicking the button below:</p>
          <p style="text-align: center;">
            <a href="${verifyUrl}" class="button">Verify Email Address</a>
          </p>
          <p>Or copy and paste this URL into your browser:</p>
          <p>${verifyUrl}</p>
          <p>This link will expire in 24 hours for security reasons.</p>
          <p>If you did not create an account with Patent Pioneer Institute, please ignore this email.</p>
          <p>Best regards,<br>The Patent Pioneer Team</p>
        </div>
        <div class="footer">
          <p>This email was sent to ${user?.email}. If you didn't request this verification, please ignore this email.</p>
          <p>&copy; ${new Date().getFullYear()} Patent Pioneer Institute. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
    `;

    // Create a plain text version for email clients that don't support HTML
    const textContent = `
    Verify Your Email Address - Patent Pioneer Institute

    Hello ${user?.firstName || ''} ${user?.lastName || ''},

    Thank you for registering with Patent Pioneer Institute. To complete your registration and access all features, please verify your email address by copying and pasting the URL below into your browser:

    ${verifyUrl}

    This link will expire in 24 hours for security reasons.

    If you did not create an account with Patent Pioneer Institute, please ignore this email.

    Best regards,
    The Patent Pioneer Team

    This email was sent to ${user?.email}. If you didn't request this verification, please ignore this email.
    © ${new Date().getFullYear()} Patent Pioneer Institute. All rights reserved.
    `;

    // Create the mail object
    const mail = {
      from: {
        email: envVariables.SENDGRID_ACCOUNT_EMAIL,
        name: 'Patent Pioneer Institute'
      },
      to: { email: user?.email },
      subject: 'Verify Your Email - Patent Pioneer Institute',
      text: textContent,
      html: htmlContent
    };

    try {
      const transport = await SendGrid.send(mail);
      return transport;
    } catch (error) {
      this.logger.error(`SendGrid error: ${error.message}`);
      if (error.response) {
        this.logger.error(`SendGrid error response: ${JSON.stringify(error.response.body)}`);
      }
      throw error;
    }
  }

  async forgotPasswordEmail(user: User, token: string) {
    // Create the reset password URL
    const resetUrl = `${envVariables.CLIENT_URL.replace(/\/$/, '')}/reset-new-password?token=${encodeURIComponent(token)}`;

    // Create a custom HTML email template
    const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <title>Reset Your Password</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          margin: 0;
          padding: 0;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          background-color: #009F9F;
          padding: 20px;
          text-align: center;
        }
        .header h1 {
          color: white;
          margin: 0;
        }
        .content {
          padding: 20px;
          background-color: #f9f9f9;
        }
        .button {
          display: inline-block;
          background-color: #009F9F;
          color: white;
          text-decoration: none;
          padding: 12px 24px;
          border-radius: 4px;
          margin: 20px 0;
          font-weight: bold;
        }
        .footer {
          text-align: center;
          padding: 20px;
          font-size: 12px;
          color: #666;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Password Reset Request</h1>
        </div>
        <div class="content">
          <p>Hello ${user?.firstName || ''} ${user?.lastName || ''},</p>
          <p>We received a request to reset your password for your Patent Pioneer Institute account. If you didn't make this request, you can safely ignore this email.</p>
          <p>To reset your password, click the button below:</p>
          <p style="text-align: center;">
            <a href="${resetUrl}" class="button">Reset Password</a>
          </p>
          <p>Or copy and paste this URL into your browser:</p>
          <p>${resetUrl}</p>
          <p>This link will expire in 24 hours for security reasons.</p>
          <p>If you have any questions, please contact our support team.</p>
          <p>Best regards,<br>The Patent Pioneer Team</p>
        </div>
        <div class="footer">
          <p>This email was sent to ${user?.email}. If you didn't request a password reset, please ignore this email.</p>
          <p>&copy; ${new Date().getFullYear()} Patent Pioneer Institute. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
    `;

    // Create a plain text version for email clients that don't support HTML
    const textContent = `
    Password Reset Request

    Hello ${user?.firstName || ''} ${user?.lastName || ''},

    We received a request to reset your password for your Patent Pioneer Institute account. If you didn't make this request, you can safely ignore this email.

    To reset your password, copy and paste this URL into your browser:
    ${resetUrl}

    This link will expire in 24 hours for security reasons.

    If you have any questions, please contact our support team.

    Best regards,
    The Patent Pioneer Team

    This email was sent to ${user?.email}. If you didn't request a password reset, please ignore this email.
    © ${new Date().getFullYear()} Patent Pioneer Institute. All rights reserved.
    `;

    // Create the mail object
    const mail = {
      from: {
        email: envVariables.SENDGRID_ACCOUNT_EMAIL,
        name: 'Patent Pioneer Institute'
      },
      to: { email: user?.email },
      subject: 'Reset Your Password - Patent Pioneer Institute',
      text: textContent,
      html: htmlContent
    };

    const transport = await SendGrid.send(mail);
    return transport;
  }

  async send(mail: any) {
    const transport = await SendGrid.send(mail);
    return transport;
  }
}
