import { AuthGuard } from "@app/guards/auth/auth.guard";
import { RequestWithUser } from "@app/interfaces";
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Put,
  HttpCode,
  UseInterceptors,
  UploadedFiles,
  Req,
  ForbiddenException,
  Query,
} from "@nestjs/common";
import { FileFieldsInterceptor } from "@nestjs/platform-express";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
} from "@nestjs/swagger";

import { FileUploadService } from "../file-upload/file-upload.service";

import { BookService } from "./book.service";
import { UpdateBookStatusDto } from "./dto";

@ApiTags("Books")
@ApiBearerAuth()
@Controller("books")
export class BookController {
  constructor(
    private readonly bookService: BookService,
    private readonly fileUploadService: FileUploadService
  ) {}

  @Get("dashboard")
  @HttpCode(200)
  @ApiOperation({ summary: "Get all books without pagination for dashboard" })
  @ApiResponse({ status: 200, description: "Return all books" })
  async getAllBooksForDashboard() {
    return this.bookService.getAllBooksWithoutPagination();
  }

  @Get()
  @HttpCode(200)
  @ApiOperation({ summary: "Get all books with pagination" })
  @ApiResponse({ status: 200, description: "Return paginated books" })
  async getAllBooks(
    @Query("page") page: string = "1",
    @Query("limit") limit: string = "6"
  ) {
    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 6;

    return this.bookService.getAllBooks(pageNum, limitNum);
  }

  @Get("my/dashboard")
  @UseGuards(AuthGuard)
  @HttpCode(200)
  @ApiOperation({
    summary: "Get authenticated user's books without pagination for dashboard",
  })
  @ApiResponse({ status: 200, description: "Return all user's books" })
  async getMyBooksForDashboard(@Req() request: RequestWithUser) {
    const userId = request?.user?.user?.sub;
    return this.bookService.getUserBooksWithoutPagination(userId);
  }

  @Get("my")
  @UseGuards(AuthGuard)
  @HttpCode(200)
  @ApiOperation({ summary: "Get authenticated user's books with pagination" })
  @ApiResponse({ status: 200, description: "Return paginated user's books" })
  async getMyBooks(
    @Req() request: RequestWithUser,
    @Query("page") page: string = "1",
    @Query("limit") limit: string = "6"
  ) {
    const userId = request?.user?.user?.sub;
    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 6;

    return this.bookService.getUserBooks(userId, pageNum, limitNum);
  }

  @Get(":id")
  @HttpCode(200)
  @ApiOperation({ summary: "Get a book by ID" })
  @ApiResponse({ status: 200, description: "Return a book" })
  async getBook(@Param("id") id: string) {
    return this.bookService.getBookById(id);
  }

  @UseGuards(AuthGuard)
  @Post()
  @HttpCode(201)
  @ApiOperation({ summary: "Create a new book (Admin only)" })
  @ApiResponse({ status: 201, description: "Book has been created" })
  @ApiConsumes("multipart/form-data")
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: "image", maxCount: 1 },
      { name: "pdfFile", maxCount: 1 },
    ])
  )
  async createBook(
    @Body() createBookDto: any,
    @UploadedFiles()
    files: { image?: Express.Multer.File[]; pdfFile?: Express.Multer.File[] },
    @Req() request: RequestWithUser
  ) {
    let imageUrl = createBookDto.image;
    let imagePublicId = "";
    let pdfUrl = "";
    let pdfPublicId = "";

    if (files?.image && files.image[0]) {
      const result = await this.fileUploadService.uploadFile(
        files.image[0],
        "books"
      );
      imageUrl = result.url;
      imagePublicId = result.public_id;
    }

    if (
      createBookDto.type === "softcopy" &&
      files?.pdfFile &&
      files.pdfFile[0]
    ) {
      const result = await this.fileUploadService.uploadFile(
        files.pdfFile[0],
        "books-pdf"
      );
      pdfUrl = result.url;
      pdfPublicId = result.public_id;
    }

    if (!imageUrl) {
      imageUrl =
        "https://images.unsplash.com/photo-1543002588-bfa74002ed7e?ixlib=rb-4.0.3";
    }

    const bookToCreate = {
      ...createBookDto,
      image: imageUrl,
      imagePublicId,
      pdfFile: pdfUrl,
      pdfPublicId,
    };

    return this.bookService.createBook(bookToCreate);
  }

  @UseGuards(AuthGuard)
  @Put(":id")
  @HttpCode(200)
  @ApiOperation({ summary: "Update a book (Admin only)" })
  @ApiResponse({ status: 200, description: "Book has been updated" })
  @ApiConsumes("multipart/form-data")
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: "image", maxCount: 1 },
      { name: "pdfFile", maxCount: 1 },
    ])
  )
  async updateBook(
    @Param("id") id: string,
    @Body() updateBookDto: any,
    @UploadedFiles()
    files: { image?: Express.Multer.File[]; pdfFile?: Express.Multer.File[] },
    @Req() request: RequestWithUser
  ) {
    const existingBook = await this.bookService.getBookById(id);

    let imageUrl = existingBook.image;
    let imagePublicId = existingBook.imagePublicId || "";
    let pdfUrl = existingBook.pdfFile || "";
    let pdfPublicId = existingBook.pdfPublicId || "";

    if (files?.image && files.image[0]) {
      if (existingBook.imagePublicId) {
        await this.fileUploadService.deleteFile(existingBook.imagePublicId);
      }

      const result = await this.fileUploadService.uploadFile(
        files.image[0],
        "books"
      );
      imageUrl = result.url;
      imagePublicId = result.public_id;
    }

    if (
      updateBookDto.type === "softcopy" &&
      files?.pdfFile &&
      files.pdfFile[0]
    ) {
      if (existingBook.pdfPublicId) {
        await this.fileUploadService.deleteFile(existingBook.pdfPublicId);
      }

      const result = await this.fileUploadService.uploadFile(
        files.pdfFile[0],
        "books-pdf"
      );
      pdfUrl = result.url;
      pdfPublicId = result.public_id;
    }

    const bookToUpdate = {
      ...updateBookDto,
      image: imageUrl,
      imagePublicId,
      pdfFile: pdfUrl,
      pdfPublicId,
    };

    return this.bookService.updateBook(id, bookToUpdate);
  }

  @UseGuards(AuthGuard)
  @Delete(":id")
  @HttpCode(200)
  @ApiOperation({ summary: "Delete a book (Admin only)" })
  @ApiResponse({ status: 200, description: "Book has been deleted" })
  async deleteBook(@Param("id") id: string, @Req() request: RequestWithUser) {
    const book = await this.bookService.getBookById(id);

    if (book.imagePublicId) {
      await this.fileUploadService.deleteFile(book.imagePublicId);
    }

    if (book.pdfPublicId) {
      await this.fileUploadService.deleteFile(book.pdfPublicId);
    }

    return this.bookService.deleteBook(id);
  }

  @UseGuards(AuthGuard)
  @Post(":id/purchase")
  @HttpCode(200)
  @ApiOperation({ summary: "Purchase a book" })
  @ApiResponse({ status: 200, description: "Book has been purchased" })
  async purchaseBook(@Param("id") id: string, @Req() request: RequestWithUser) {
    const userId = request?.user?.user?.sub;
    return this.bookService.addBookToUser(userId, id);
  }

  @UseGuards(AuthGuard)
  @Delete(":id/purchase")
  @HttpCode(200)
  @ApiOperation({
    summary: "Remove a book from user's collection (Admin only)",
  })
  @ApiResponse({
    status: 200,
    description: "Book has been removed from user's collection",
  })
  async removeBookFromUser(
    @Param("id") id: string,
    @Req() request: RequestWithUser
  ) {
    if (request?.user?.role !== "Admin") {
      throw new ForbiddenException("Only admins can remove books from users");
    }

    const userId = request?.user?.user?.sub;
    return this.bookService.removeBookFromUser(userId, id);
  }

  @UseGuards(AuthGuard)
  @Get("hardcopy/status/:status?")
  @HttpCode(200)
  @ApiOperation({ summary: "Get all hardcopy books by status (optional)" })
  @ApiResponse({ status: 200, description: "Return hardcopy books" })
  async getHardcopyBooksByStatus(
    @Param("status") status?: "in process" | "shipped" | "delivered"
  ) {
    return this.bookService.getHardcopyBooksByStatus(status);
  }

  @UseGuards(AuthGuard)
  @Get("my/hardcopy")
  @HttpCode(200)
  @ApiOperation({ summary: "Get authenticated user's hardcopy books" })
  @ApiResponse({ status: 200, description: "Return user's hardcopy books" })
  async getMyHardcopyBooks(@Req() request: RequestWithUser) {
    const userId = request?.user?.user?.sub;
    return this.bookService.getUserPurchasedHardcopyBooks(userId);
  }

  @UseGuards(AuthGuard)
  @Put(":id/status")
  @HttpCode(200)
  @ApiOperation({ summary: "Update a book's status (Admin only)" })
  @ApiResponse({ status: 200, description: "Book status has been updated" })
  async updateBookStatus(
    @Param("id") id: string,
    @Body() updateStatusDto: UpdateBookStatusDto,
    @Req() request: RequestWithUser
  ) {
    if (request?.user?.role !== "Admin") {
      throw new ForbiddenException("Only admins can update book status");
    }

    return this.bookService.updateBookStatus(id, updateStatusDto.status);
  }

  @UseGuards(AuthGuard)
  @Get(":id/pdf")
  @HttpCode(200)
  @ApiOperation({ summary: "Get PDF URL for a softcopy book" })
  @ApiResponse({ status: 200, description: "Return PDF URL" })
  async getBookPdfUrl(
    @Param("id") id: string,
    @Req() request: RequestWithUser
  ) {
    const userId = request?.user?.user?.sub;

    if (request?.user?.role === "Admin" || request?.user?.isAdmin === true) {
      const book = await this.bookService.getBookById(id);

      if (book.type !== "softcopy") {
        throw new ForbiddenException(
          "PDF is only available for softcopy books"
        );
      }

      if (!book.pdfFile || !book.pdfPublicId) {
        throw new ForbiddenException("PDF file not available for this book");
      }

      const s3Service = this.fileUploadService.s3;
      const signedUrl = s3Service.generateSignedUrl(book.pdfPublicId);

      if (!signedUrl) {
        throw new ForbiddenException("Failed to generate PDF URL");
      }

      return { pdfUrl: signedUrl };
    }

    return this.bookService.getBookPdfUrl(id, userId);
  }

  @UseGuards(AuthGuard)
  @Get(":id/view-pdf")
  @ApiOperation({ summary: "View PDF for a softcopy book" })
  @ApiResponse({ status: 200, description: "Return PDF content" })
  async viewBookPdf(@Param("id") id: string, @Req() request: RequestWithUser) {
    const userId = request?.user?.user?.sub;

    if (request?.user?.role === "Admin" || request?.user?.isAdmin === true) {
      const book = await this.bookService.getBookById(id);

      if (book.type !== "softcopy") {
        throw new ForbiddenException(
          "PDF is only available for softcopy books"
        );
      }

      if (!book.pdfFile || !book.pdfPublicId) {
        throw new ForbiddenException("PDF file not available for this book");
      }

      const s3Service = this.fileUploadService.s3;
      const signedUrl = s3Service.generateSignedUrl(book.pdfPublicId);

      if (!signedUrl) {
        throw new ForbiddenException("Failed to generate PDF URL");
      }

      return { pdfUrl: signedUrl };
    }

    const result = await this.bookService.getBookPdfUrl(id, userId);
    return result;
  }
}
