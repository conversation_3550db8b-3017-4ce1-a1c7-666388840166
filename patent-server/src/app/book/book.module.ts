import { S3Module } from '@app/services/s3/s3.module';
import { Module } from '@nestjs/common';

import { FileUploadModule } from '../file-upload/file-upload.module';

import { BookController } from './book.controller';
import { BookService } from './book.service';

@Module({
  imports: [
    FileUploadModule,
    S3Module
  ],
  controllers: [BookController],
  providers: [BookService],
  exports: [BookService],
})
export class BookModule {}