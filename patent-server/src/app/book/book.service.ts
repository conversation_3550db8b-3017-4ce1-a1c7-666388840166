import {
  Injectable,
  Inject,
  NotFoundException,
  BadRequestException,
  Logger,
} from "@nestjs/common";

import { FileUploadService } from "../file-upload/file-upload.service";

import { CreateBookDto, UpdateBookDto } from "./dto";

@Injectable()
export class BookService {
  constructor(
    @Inject("BookRepository") private readonly bookRepository,
    @Inject("UserRepository") private readonly userRepository,
    private readonly fileUploadService: FileUploadService
  ) {}

  async getAllBooksWithoutPagination() {
    const books = await this.bookRepository.getMany({});
    return books;
  }

  async getAllBooks(page = 1, limit = 6) {
    const skip = (page - 1) * limit;

    const totalCount = await this.bookRepository.count({});

    const books = await this.bookRepository.getModel
      .find({})
      .skip(skip)
      .limit(limit)
      .exec();

    return {
      books,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        pageSize: limit,
        hasNext: page < Math.ceil(totalCount / limit),
        hasPrevious: page > 1,
      },
    };
  }

  async getBookById(bookId: string) {
    const book = await this.bookRepository.findOne({ _id: bookId });
    if (!book) {
      throw new NotFoundException("Book not found");
    }
    return book;
  }

  async createBook(createBookDto: CreateBookDto) {
    const book = await this.bookRepository.create(createBookDto);
    return book;
  }

  async updateBook(bookId: string, updateBookDto: UpdateBookDto) {
    const book = await this.bookRepository.findOne({ _id: bookId });
    if (!book) {
      throw new NotFoundException("Book not found");
    }

    const updatedBook = await this.bookRepository.findOneAndUpdate(
      { _id: bookId },
      updateBookDto,
      { new: true }
    );
    return updatedBook;
  }

  async deleteBook(bookId: string) {
    const book = await this.bookRepository.findOne({ _id: bookId });
    if (!book) {
      throw new NotFoundException("Book not found");
    }

    await this.bookRepository.deleteOne({ _id: bookId });
    return { message: "Book deleted successfully" };
  }

  async getUserBooksWithoutPagination(userId: string) {
    const user = await this.userRepository.findOne({ _id: userId });
    if (!user) {
      throw new BadRequestException("User not found");
    }

    if (!user.books || user.books.length === 0) {
      return [];
    }

    const books = await this.bookRepository.getModel
      .find({ _id: { $in: user.books } })
      .exec();

    return books;
  }

  async getUserBooks(userId: string, page = 1, limit = 6) {
    const user = await this.userRepository.findOne({ _id: userId });
    if (!user) {
      throw new BadRequestException("User not found");
    }
    if (!user.books || user.books.length === 0) {
      return {
        books: [],
        pagination: {
          totalCount: 0,
          totalPages: 0,
          currentPage: page,
          pageSize: limit,
          hasNext: false,
          hasPrevious: false,
        },
      };
    }

    const skip = (page - 1) * limit;

    const totalCount = user.books.length;

    const books = await this.bookRepository.getModel
      .find({ _id: { $in: user.books } })
      .skip(skip)
      .limit(limit)
      .exec();

    return {
      books,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        pageSize: limit,
        hasNext: page < Math.ceil(totalCount / limit),
        hasPrevious: page > 1,
      },
    };
  }

  async addBookToUser(userId: string, bookId: string) {
    try {
      const user = await this.userRepository.findOne({ _id: userId });
      if (!user) {
        throw new NotFoundException("User not found");
      }

      const book = await this.bookRepository.findOne({ _id: bookId });
      if (!book) {
        throw new NotFoundException("Book not found");
      }

      // No need to check if the user already has the book
      const userObject = user.toObject ? user.toObject() : user;
      const books = Array.isArray(userObject.books) ? userObject.books : [];
      books.push(bookId);

      await this.userRepository.findOneAndUpdate(
        { _id: userId },
        { $set: { books, updatedAt: new Date() } },
        { new: true }
      );

      return { success: true, message: "Book added to user successfully" };
    } catch (error) {
      throw error;
    }
  }

  async removeBookFromUser(userId: string, bookId: string) {
    const user = await this.userRepository.findOne({ _id: userId });
    if (!user) {
      throw new BadRequestException("User not found");
    }
    const book = await this.bookRepository.findOne({ _id: bookId });
    if (!book) {
      throw new BadRequestException("Book not found");
    }

    if (!user.books || !user.books.some((id) => id.toString() === bookId)) {
      throw new BadRequestException("User does not have this book");
    }

    await this.userRepository.findOneAndUpdate(
      { _id: userId },
      { $pull: { books: bookId } },
      { new: true }
    );

    return { message: "Book removed from user successfully" };
  }

  async updateBookStatus(
    bookId: string,
    status: "in process" | "shipped" | "delivered"
  ) {
    const book = await this.bookRepository.findOne({ _id: bookId });
    if (!book) {
      throw new NotFoundException("Book not found");
    }

    if (book.type !== "hardcopy") {
      throw new BadRequestException(
        "Status can only be updated for hardcopy books"
      );
    }

    const updatedBook = await this.bookRepository.findOneAndUpdate(
      { _id: bookId },
      { status },
      { new: true }
    );

    return updatedBook;
  }

  async getHardcopyBooksByStatus(
    status?: "in process" | "shipped" | "delivered"
  ) {
    const query: any = { type: "hardcopy" };
  
    if (status) {
      query.status = status;
    }
  
    try {
      const books = await this.bookRepository.getModel
        .find(query)
        .exec();
  
      const booksWithUsername = await Promise.all(
        books.map(async (book) => {
          const user = await this.userRepository.getModel
            .findOne({ books: book._id })
            .select("username firstName lastName email")
            .exec();
  
          return {
            ...book.toObject(),
            user: user
              ? {
                  username: user.username,
                  firstName: user.firstName,
                  lastName: user.lastName,
                  email: user.email,
                }
              : null,
          };
        })
      );
  
      return booksWithUsername;
    } catch (error) {
      Logger.error("Error fetching hardcopy books:", error);
      throw error;
    }
  }

  async getUserPurchasedHardcopyBooks(userId: string) {
    const user = await this.userRepository.findOne({ _id: userId });
    if (!user) {
      throw new BadRequestException("User not found");
    }

    try {
      const books = await this.bookRepository.getModel
        .find({
          userId: userId,
          type: "hardcopy",
        })
        .exec();

      return books;
    } catch (error) {
      Logger.error(`Error fetching user's hardcopy books:`, error);
      throw error;
    }
  }

  async getBookPdfUrl(bookId: string, userId: string) {
    try {
      const book = await this.bookRepository.findOne({ _id: bookId });
      if (!book) {
        throw new NotFoundException("Book not found");
      }

      if (book.type !== "softcopy") {
        throw new BadRequestException("PDF is only available for softcopy books");
      }

      if (!book.pdfFile || !book.pdfPublicId) {
        throw new BadRequestException("PDF file not available for this book");
      }

      const user = await this.userRepository.findOne({ _id: userId });
      if (!user) {
        throw new BadRequestException("User not found");
      }

      const hasBook = user.books && user.books.some((id) => id.toString() === bookId.toString());
      if (!hasBook) {
        throw new BadRequestException("You must purchase this book to access the PDF");
      }

      // Generate a signed URL for the PDF using S3
      const s3Service = this.fileUploadService.s3;
      const pdfUrl = s3Service.generateSignedUrl(book.pdfPublicId);

      if (!pdfUrl) {
        throw new BadRequestException("Failed to generate PDF URL");
      }

      return { pdfUrl };
    } catch (error) {
      Logger.error(`Error getting book PDF URL:`, error);
      throw error;
    }
  }
}
