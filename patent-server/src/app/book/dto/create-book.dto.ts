import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, Min } from 'class-validator';

export class CreateBookDto {
  @ApiProperty({ description: 'Title of the book' })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({ description: 'Description of the book' })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({ description: 'Price of the book in USD' })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  price: number;

  @ApiProperty({ description: 'Type of book', enum: ['hardcopy', 'softcopy'] })
  @IsNotEmpty()
  @IsEnum(['hardcopy', 'softcopy'])
  type: 'hardcopy' | 'softcopy';

  @ApiProperty({ description: 'Book image URL' })
  @IsOptional()
  @IsString()
  image?: string;

  @ApiProperty({ description: 'Author of the book' })
  @IsOptional()
  @IsString()
  author?: string;

  @ApiProperty({ description: 'Year the book was published' })
  @IsOptional()
  @IsNumber()
  publishedYear?: number;

  @ApiProperty({ description: 'Publisher of the book' })
  @IsOptional()
  @IsString()
  publisher?: string;

  @ApiProperty({ description: 'Status of the book order (for hardcopy books)', enum: ['in process', 'shipped', 'delivered'], default: 'in process' })
  @IsOptional()
  @IsEnum(['in process', 'shipped', 'delivered'])
  status?: 'in process' | 'shipped' | 'delivered';
}