import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as sgMail from '@sendgrid/mail';

import { ContactFormDto } from './dto/contact.dto';

@Injectable()
export class ContactService {
  private readonly logger = new Logger(ContactService.name);

  constructor(private readonly configService: ConfigService) {
    // Initialize SendGrid with API key from config
    sgMail.setApiKey(this.configService.get('SENDGRID_API_KEY'));
  }

  async sendContactEmail(contactFormDto: ContactFormDto) {
    try {
      const { name, email, feedback, to } = contactFormDto;
      
      // Email to the recipient (admin/support)
      const msg = {
        to: to,
        from: this.configService.get('SENDGRID_ACCOUNT_EMAIL', '<EMAIL>'),
        subject: `New Contact Form Submission from ${name}`,
        text: `Name: ${name}\nEmail: ${email}\n\nMessage:\n${feedback}`,
        html: `
          <h2>New Contact Form Submission</h2>
          <p><strong>From:</strong> ${name}</p>
          <p><strong>Email:</strong> ${email}</p>
          <h3>Message:</h3>
          <p>${feedback}</p>
        `,
      };

      // Confirmation email to the user
      const confirmationMsg = {
        to: email,
        from: this.configService.get('SENDGRID_ACCOUNT_EMAIL', '<EMAIL>'),
        subject: 'Thank you for contacting Patent Pioneer',
        text: `Dear ${name},\n\nThank you for contacting us. We have received your message and will get back to you as soon as possible.\n\nBest regards,\nThe Patent Pioneer Team`,
        html: `
          <h2>Thank You for Contacting Us</h2>
          <p>Dear ${name},</p>
          <p>Thank you for reaching out to Patent Pioneer. We have received your message and will respond to you as soon as possible.</p>
          <p>Best regards,<br>The Patent Pioneer Team</p>
        `,
      };

      // Send both emails
      await Promise.all([
        sgMail.send(msg),
        sgMail.send(confirmationMsg),
      ]);

      this.logger.log(`Contact email sent successfully to ${to} from ${email}`);
      return { success: true, message: 'Email sent successfully' };
    } catch (error) {
      this.logger.error(`Failed to send contact email: ${error.message}`, error.stack);
      throw error;
    }
  }
}
