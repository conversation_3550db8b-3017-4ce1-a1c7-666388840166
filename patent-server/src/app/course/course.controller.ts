import { AuthGuard } from "@app/guards/auth/auth.guard";
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  UseGuards,
  HttpCode,
  UseInterceptors,
  UploadedFiles,
  UploadedFile,
  ConsoleLogger,
  Res,
  NotFoundException,
  Req,
  Query,
} from "@nestjs/common";
import { FileInterceptor, AnyFilesInterceptor } from "@nestjs/platform-express";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
} from "@nestjs/swagger";
import { Response, Request } from "express";
import * as PDFDocument from "pdfkit";

import { FileUploadService } from "../file-upload/file-upload.service";

import { CourseService } from "./course.service";
import { ModuleDto } from "./dto";

@ApiTags("Courses")
@ApiBearerAuth()
@Controller("courses")
export class CourseController {
  private readonly logger = new ConsoleLogger(CourseController.name);

  constructor(
    private readonly courseService: CourseService,
    private readonly fileUploadService: FileUploadService
  ) {}

  @Get("dashboard")
  @HttpCode(200)
  @ApiOperation({ summary: "Get all courses without pagination for dashboard" })
  @ApiResponse({ status: 200, description: "Return all courses" })
  async getAllCoursesForDashboard() {
    return this.courseService.getAllCoursesWithoutPagination();
  }

  @Get()
  @HttpCode(200)
  @ApiOperation({ summary: "Get all courses with pagination" })
  @ApiResponse({ status: 200, description: "Return paginated courses" })
  async getAllCourses(
    @Query("page") page: string = "1",
    @Query("limit") limit: string = "6"
  ) {
    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 6;

    return this.courseService.getAllCourses(pageNum, limitNum);
  }

  @UseGuards(AuthGuard)
  @Get("my/dashboard")
  @HttpCode(200)
  @ApiOperation({
    summary:
      "Get all courses owned by the current user without pagination for dashboard",
  })
  @ApiResponse({ status: 200, description: "Return all user's courses" })
  async getMyCoursesForDashboard(@Req() request: any) {
    const userId = request?.user?.user?.sub;
    if (!userId) {
      throw new NotFoundException("User not found");
    }

    return this.courseService.getMyCoursesWithoutPagination(userId);
  }

  @UseGuards(AuthGuard)
  @Get("my")
  @HttpCode(200)
  @ApiOperation({
    summary: "Get all courses owned by the current user with pagination",
  })
  @ApiResponse({ status: 200, description: "Return paginated user's courses" })
  async getMyCourses(
    @Req() request: any,
    @Query("page") page: string = "1",
    @Query("limit") limit: string = "6"
  ) {
    const userId = request?.user?.user?.sub;
    if (!userId) {
      throw new NotFoundException("User not found");
    }
    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 6;

    return this.courseService.getMyCourses(userId, pageNum, limitNum);
  }

  @Get(":id")
  @HttpCode(200)
  @ApiOperation({ summary: "Get a course by ID" })
  @ApiResponse({ status: 200, description: "Return a course" })
  async getCourse(@Param("id") id: string) {
    return this.courseService.getCourseById(id);
  }

  @UseGuards(AuthGuard)
  @Post()
  @HttpCode(201)
  @ApiOperation({ summary: "Create a new course" })
  @ApiResponse({ status: 201, description: "Course has been created" })
  @ApiConsumes("multipart/form-data")
  @UseInterceptors(AnyFilesInterceptor())
  async createCourse(
    @Body() createCourseDto: any,
    @UploadedFiles() files: Array<Express.Multer.File>
  ) {
    let imageUrl = "";
    let imagePublicId = "";
    let modulesContent = [];

    if (files && files.length > 0) {
      const results = await this.fileUploadService.uploadFiles(files, "courses");

      const imageFile = files.find((file) => file.fieldname === "image");
      if (imageFile) {
        const imageIndex = files.indexOf(imageFile);
        imageUrl = results[imageIndex].url;
        imagePublicId = results[imageIndex].public_id;
      }

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        if (file.fieldname.startsWith("moduleFiles-")) {
          const moduleIndex = parseInt(file.fieldname.split("-")[1] || "0");
          modulesContent[moduleIndex] = {
            url: results[i].url,
            publicId: results[i].public_id,
            resourceType: results[i].resource_type,
          };
        }
      }
    }

    let modules = [];
    try {
      if (!createCourseDto.modules) {
        modules = [];
      } else if (typeof createCourseDto.modules === "string") {
        try {
          const parsedModules = JSON.parse(createCourseDto.modules);
          if (Array.isArray(parsedModules)) {
            modules = parsedModules;
          } else if (typeof parsedModules === "object") {
            modules = [parsedModules];
          } else {
            modules = [];
          }
        } catch (err) {
          modules = [];
        }
      } else if (Array.isArray(createCourseDto.modules)) {
        modules = createCourseDto.modules;
      } else if (typeof createCourseDto.modules === "object") {
        modules = [createCourseDto.modules];
      } else {
        modules = [];
      }
    } catch (err) {
      modules = [];
    }

    if (!Array.isArray(modules)) {
      modules = [];
    }

    const processedModules = Array.isArray(modules)
      ? modules.map((module, index) => {
          if (
            ["image", "pdf", "video"].includes(module?.type) &&
            modulesContent[index]
          ) {
            return {
              ...module,
              content: modulesContent[index].url,
              publicId: modulesContent[index].public_id,
              resourceType: modulesContent[index].resourceType,
            };
          }
          return module;
        })
      : [];

    if (!imageUrl && !createCourseDto.image) {
      imageUrl = "https://via.placeholder.com/500";
    }

    const courseToCreate = {
      ...createCourseDto,
      image: imageUrl || createCourseDto.image,
      imagePublicId,
      modules: processedModules,
    };

    return this.courseService.createCourse(courseToCreate);
  }

  @UseGuards(AuthGuard)
  @Put(":id")
  @HttpCode(200)
  @ApiOperation({ summary: "Update a course" })
  @ApiResponse({ status: 200, description: "Course has been updated" })
  @ApiConsumes("multipart/form-data")
  @UseInterceptors(AnyFilesInterceptor())
  async updateCourse(
    @Param("id") id: string,
    @Body() updateCourseDto: any,
    @UploadedFiles() files: Array<Express.Multer.File>
  ) {
    try {
      const existingCourse = await this.courseService.getCourseById(id);

      let imageUrl = existingCourse.image;
      let imagePublicId = existingCourse.imagePublicId || "";
      let modulesContent = [];

      if (files && files.length > 0) {
        try {
          const results = await this.fileUploadService.uploadFiles(
            files,
            "courses"
          );

          const imageFile = files.find((file) => file.fieldname === "image");
          if (imageFile) {
            if (existingCourse.imagePublicId) {
              await this.fileUploadService.deleteFile(existingCourse.imagePublicId);
            }

            const imageIndex = files.indexOf(imageFile);
            imageUrl = results[imageIndex].url;
            imagePublicId = results[imageIndex].public_id;
          }

          for (let i = 0; i < files.length; i++) {
            const file = files[i];
            if (file.fieldname.startsWith("moduleFiles-")) {
              const moduleIndex = parseInt(file.fieldname.split("-")[1] || "0");
              modulesContent[moduleIndex] = {
                url: results[i].url,
                publicId: results[i].public_id,
                resourceType: results[i].resource_type,
              };
            }
          }
        } catch (fileError) {
          throw new Error(`File upload failed: ${fileError.message}`);
        }
      }

      let modules = [];
      try {
        if (updateCourseDto.modules) {
          if (typeof updateCourseDto.modules === "string") {
            modules = JSON.parse(updateCourseDto.modules);
          } else {
            modules = updateCourseDto.modules;
          }
        } else {
          modules = existingCourse.modules || [];
        }
      } catch (err) {
        modules = existingCourse.modules || [];
      }

      const processedModules = modules.map((module, index) => {
        try {
          const existingModule = existingCourse.modules?.find(
            (m) =>
              (m._id &&
                module._id &&
                m._id.toString() === module._id.toString()) ||
              (m.id && module.id && m.id === module.id)
          );

          if (
            ["image", "pdf", "video"].includes(module.type) &&
            modulesContent[index]
          ) {
            if (existingModule?.publicId) {
              this.fileUploadService.deleteFile(existingModule.publicId);
            }

            return {
              ...module,
              content: modulesContent[index].url,
              publicId: modulesContent[index].publicId,
              resourceType: modulesContent[index].resourceType,
            };
          }

          return {
            ...module,
            publicId: existingModule?.publicId || module.publicId || "",
            resourceType:
              existingModule?.resourceType || module.resourceType || "",
          };
        } catch (moduleError) {
          this.logger.error(
            `Error processing module at index ${index}: ${moduleError.message}`
          );
          return module;
        }
      });

      const courseToUpdate = {
        ...updateCourseDto,
        image: imageUrl,
        imagePublicId,
        modules: processedModules,
      };

      return this.courseService.updateCourse(id, courseToUpdate);
    } catch (error) {
      this.logger.error(`Course update error: ${error.message}`);
      throw error;
    }
  }

  @UseGuards(AuthGuard)
  @Delete(":id")
  @HttpCode(200)
  @ApiOperation({ summary: "Delete a course" })
  @ApiResponse({ status: 200, description: "Course has been deleted" })
  async deleteCourse(@Param("id") id: string) {
    const course = await this.courseService.getCourseById(id);

    if (course.imagePublicId) {
      await this.fileUploadService.deleteFile(course.imagePublicId);
    }

    for (const module of course.modules) {
      if (module.publicId && ["image", "pdf", "video"].includes(module.type)) {
        await this.fileUploadService.deleteFile(module.publicId);
      }
    }

    return this.courseService.deleteCourse(id);
  }

  @UseGuards(AuthGuard)
  @Post(":id/modules")
  @HttpCode(201)
  @ApiOperation({ summary: "Add a module to a course" })
  @ApiResponse({
    status: 201,
    description: "Module has been added to the course",
  })
  @ApiConsumes("multipart/form-data")
  @UseInterceptors(FileInterceptor("file"))
  async addModuleToCourse(
    @Param("id") courseId: string,
    @Body() moduleDto: ModuleDto,
    @UploadedFile() file: Express.Multer.File
  ) {
    let processedModule = { ...moduleDto };
    if (file && ["image", "pdf", "video"].includes(moduleDto.type)) {
      const result = await this.fileUploadService.uploadFile(file, "courses");
      processedModule = {
        ...moduleDto,
        content: result.url,
        publicId: result.public_id,
        resourceType: result.resource_type,
      };
    }
    return this.courseService.addModuleToCourse(courseId, processedModule);
  }

  @UseGuards(AuthGuard)
  @Put(":courseId/modules/:moduleId")
  @HttpCode(200)
  @ApiOperation({ summary: "Update a module in a course" })
  @ApiResponse({ status: 200, description: "Module has been updated" })
  @ApiConsumes("multipart/form-data")
  @UseInterceptors(FileInterceptor("file"))
  async updateModuleInCourse(
    @Param("courseId") courseId: string,
    @Param("moduleId") moduleId: string,
    @Body() moduleDto: ModuleDto,
    @UploadedFile() file: Express.Multer.File
  ) {
    const course = await this.courseService.getCourseById(courseId);
    const existingModule = course.modules.find(
      (m) => m._id.toString() === moduleId
    );

    let processedModule = { ...moduleDto };

    if (file && ["image", "pdf", "video"].includes(moduleDto.type)) {
      if (existingModule?.publicId) {
        await this.fileUploadService.deleteFile(existingModule.publicId);
      }

      const result = await this.fileUploadService.uploadFile(file, "courses");

      processedModule = {
        ...moduleDto,
        content: result.url,
        publicId: result.public_id,
        resourceType: result.resource_type,
      };
    } else if (!file && existingModule) {
      processedModule.publicId = existingModule.publicId;
      processedModule.resourceType = existingModule.resourceType;
      if (!processedModule.content && existingModule.content) {
        processedModule.content = existingModule.content;
      }
    }

    return this.courseService.updateModuleInCourse(
      courseId,
      moduleId,
      processedModule
    );
  }

  @UseGuards(AuthGuard)
  @Delete(":courseId/modules/:moduleId")
  @HttpCode(200)
  @ApiOperation({ summary: "Delete a module from a course" })
  @ApiResponse({ status: 200, description: "Module has been deleted" })
  async deleteModuleFromCourse(
    @Param("courseId") courseId: string,
    @Param("moduleId") moduleId: string
  ) {
    const course = await this.courseService.getCourseById(courseId);
    const moduleToDelete = course.modules.find(
      (m) => m._id.toString() === moduleId
    );

    if (
      moduleToDelete?.publicId &&
      ["image", "pdf", "video"].includes(moduleToDelete.type)
    ) {
      await this.fileUploadService.deleteFile(moduleToDelete.publicId);
    }

    return this.courseService.deleteModuleFromCourse(courseId, moduleId);
  }

  @UseGuards(AuthGuard)
  @Get(":id/download")
  @HttpCode(200)
  @ApiOperation({ summary: "Download a course as a zip file" })
  @ApiResponse({
    status: 200,
    description: "Returns a zip file containing all course content",
  })
  async downloadCourse(
    @Param("id") id: string,
    @Res() res: Response,
    @Req() req: Request
  ) {
    try {
      const course = await this.courseService.getCourseById(id);
      const zipBuffer = await this.courseService.generateCourseZip(id);
      const filename = `${course.title.replace(/[^a-z0-9]/gi, "_").toLowerCase()}.zip`;
      const rangeHeader = req.headers["range"];
      if (rangeHeader) {
        const parts = rangeHeader.replace(/bytes=/, "").split("-");
        const start = parseInt(parts[0], 10);
        const end = parts[1] ? parseInt(parts[1], 10) : zipBuffer.length - 1;
        const chunkSize = end - start + 1;

        const chunk = zipBuffer.slice(start, end + 1);

        res.status(206);
        res.set({
          "Content-Type": "application/zip",
          "Content-Disposition": `attachment; filename="${filename}"`,
          "Content-Length": chunkSize,
          "Content-Range": `bytes ${start}-${end}/${zipBuffer.length}`,
          "Accept-Ranges": "bytes",
        });

        return res.send(chunk);
      } else {
        res.set({
          "Content-Type": "application/zip",
          "Content-Disposition": `attachment; filename="${filename}"`,
          "Content-Length": zipBuffer.length,
          "Accept-Ranges": "bytes",
        });

        return res.send(zipBuffer);
      }
    } catch (error) {
      this.logger.error(`Error downloading course: ${error.message}`);
      throw error;
    }
  }

  @UseGuards(AuthGuard)
  @Get(":courseId/modules/:moduleId/download")
  @HttpCode(200)
  @ApiOperation({ summary: "Download a specific module file" })
  @ApiResponse({
    status: 200,
    description: "Returns the module file for download",
  })
  async downloadModuleFile(
    @Param("courseId") courseId: string,
    @Param("moduleId") moduleId: string,
    @Res() res: Response,
    @Req() req: Request
  ) {
    try {
      const course = await this.courseService.getCourseById(courseId);
      const module = course.modules.find((m) => m._id.toString() === moduleId);

      if (!module) {
        throw new NotFoundException("Module not found");
      }

      if (!module.content) {
        throw new NotFoundException("No content found for this module");
      }

      if (module.type === "text") {
        try {
          const doc = new PDFDocument({ margin: 50 });
          res.set({
            "Content-Type": "application/pdf",
            "Content-Disposition": `attachment; filename="${module.title.replace(/[^a-z0-9]/gi, "_").toLowerCase()}.pdf"`,
          });
          doc.pipe(res);
          doc.fontSize(20).text(module.title, { align: "center" });
          doc.moveDown();
          doc.fontSize(12).text(module.content || "");
          doc.end();
          return;
        } catch (error) {
          this.logger.error(`Error creating PDF: ${error.message}`);
          throw new Error("Failed to create PDF from text content");
        }
      }

      if (!module.publicId) {
        throw new NotFoundException("No downloadable content found for this module");
      }

      const s3Service = this.fileUploadService.s3;
      // Pass the resource type as the second parameter and a numeric expiration as the third
      const signedUrl = s3Service.generateSignedUrl(
        module.publicId,
        module.resourceType || module.type,
        3600 // Explicit numeric expiration in seconds (1 hour)
      );

      if (!signedUrl) {
        throw new Error("Failed to generate download URL");
      }

      this.logger.log(`Redirecting to signed URL for module download: ${module.title}`);

      return res.redirect(signedUrl);
    } catch (error) {
      this.logger.error(`Error downloading module: ${error.message}`);
      throw error;
    }
  }
}
