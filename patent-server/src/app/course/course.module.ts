import { S3Module } from '@app/services/s3/s3.module';
import { Module } from '@nestjs/common';

import { FileUploadModule } from '../file-upload/file-upload.module';

import { CourseController } from './course.controller';
import { CourseService } from './course.service';

@Module({
  imports: [FileUploadModule, S3Module],
  controllers: [CourseController],
  providers: [CourseService],
  exports: [CourseService],
})
export class CourseModule {}