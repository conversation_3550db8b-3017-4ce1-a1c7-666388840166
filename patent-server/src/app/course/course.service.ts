import { Injectable, Inject, NotFoundException, Logger } from '@nestjs/common';
import * as archiver from 'archiver';
import axios from 'axios';

import { FileUploadService } from '../file-upload/file-upload.service';

import { CreateCourseDto, UpdateCourseDto } from './dto';

@Injectable()
export class CourseService {
  private readonly logger = new Logger(CourseService.name);

  constructor(
    @Inject('CourseRepository') private readonly courseRepository,
    @Inject('UserRepository') private readonly userRepository,
    private readonly fileUploadService: FileUploadService,
  ) {}

  async getAllCoursesWithoutPagination() {
    // Get all courses without pagination for dashboard
    const courses = await this.courseRepository.getMany({});
    return courses;
  }

  async getAllCourses(page = 1, limit = 6) {
    const skip = (page - 1) * limit;

    // Get total count for pagination metadata
    const totalCount = await this.courseRepository.count({});

    // Get paginated courses - using the model directly for pagination
    const courses = await this.courseRepository.getModel
      .find({})
      .skip(skip)
      .limit(limit)
      .exec();

    return {
      courses,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        pageSize: limit,
        hasNext: page < Math.ceil(totalCount / limit),
        hasPrevious: page > 1
      }
    };
  }

  async getMyCoursesWithoutPagination(userId: string) {
    try {

      // Find the user and get their courses array
      const user = await this.userRepository.findOne({ _id: userId });

      if (!user) {
        this.logger.error(`User ${userId} not found`);
        throw new NotFoundException('User not found');
      }

      // Check if user has any courses
      if (!user.courses || user.courses.length === 0) {
        return [];
      }

      // Get all courses that match the IDs in the user's courses array
      const userCourses = await this.courseRepository.getModel
        .find({ _id: { $in: user.courses } })
        .exec();


      return userCourses;
    } catch (error) {
      this.logger.error(`Error getting user courses for dashboard: ${error.message}`);
      throw error;
    }
  }

  async getMyCourses(userId: string, page = 1, limit = 6) {
    try {
      const skip = (page - 1) * limit;

      // Find the user and get their courses array
      const user = await this.userRepository.findOne({ _id: userId });

      if (!user) {
        this.logger.error(`User ${userId} not found`);
        throw new NotFoundException('User not found');
      }

      // Check if user has any courses
      if (!user.courses || user.courses.length === 0) {
        return {
          courses: [],
          pagination: {
            totalCount: 0,
            totalPages: 0,
            currentPage: page,
            pageSize: limit,
            hasNext: false,
            hasPrevious: false
          }
        };
      }

      // Get total count for pagination metadata
      const totalCount = user.courses.length;

      // Get paginated courses that match the IDs in the user's courses array
      const userCourses = await this.courseRepository.getModel
        .find({ _id: { $in: user.courses } })
        .skip(skip)
        .limit(limit)
        .exec();


      return {
        courses: userCourses,
        pagination: {
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
          currentPage: page,
          pageSize: limit,
          hasNext: page < Math.ceil(totalCount / limit),
          hasPrevious: page > 1
        }
      };
    } catch (error) {
      this.logger.error(`Error getting user courses: ${error.message}`);
      throw error;
    }
  }

  async getCourseById(courseId: string) {
    const course = await this.courseRepository.findOne({ _id: courseId });
    if (!course) {
      throw new NotFoundException('Course not found');
    }
    return course;
  }

  async createCourse(createCourseDto: CreateCourseDto) {
    const course = await this.courseRepository.create(createCourseDto);
    return course;
  }

  async updateCourse(courseId: string, updateCourseDto: UpdateCourseDto) {
    // Check if course exists
    const course = await this.courseRepository.findOne({ _id: courseId });
    if (!course) {
      throw new NotFoundException('Course not found');
    }

    // Update course
    const updatedCourse = await this.courseRepository.findOneAndUpdate(
      { _id: courseId },
      updateCourseDto,
      { new: true },
    );
    return updatedCourse;
  }

  async deleteCourse(courseId: string) {
    // Check if course exists
    const course = await this.courseRepository.findOne({ _id: courseId });
    if (!course) {
      throw new NotFoundException('Course not found');
    }

    // Delete course
    await this.courseRepository.deleteOne({ _id: courseId });
    return { message: 'Course deleted successfully' };
  }

  async addModuleToCourse(courseId: string, moduleDto: any) {
    const course = await this.courseRepository.findOne({ _id: courseId });
    if (!course) {
      throw new NotFoundException('Course not found');
    }

    const updatedCourse = await this.courseRepository.findOneAndUpdate(
      { _id: courseId },
      { $push: { modules: moduleDto } },
      { new: true },
    );
    return updatedCourse;
  }

  async updateModuleInCourse(courseId: string, moduleId: string, moduleDto: any) {
    const course = await this.courseRepository.findOne({
      _id: courseId,
      'modules._id': moduleId,
    });

    if (!course) {
      throw new NotFoundException('Course or module not found');
    }

    const updatedCourse = await this.courseRepository.findOneAndUpdate(
      { _id: courseId, 'modules._id': moduleId },
      { $set: { 'modules.$': { ...moduleDto, _id: moduleId } } },
      { new: true },
    );

    return updatedCourse;
  }

  async deleteModuleFromCourse(courseId: string, moduleId: string) {
    const course = await this.courseRepository.findOne({
      _id: courseId,
      'modules._id': moduleId,
    });

    if (!course) {
      throw new NotFoundException('Course or module not found');
    }

    const updatedCourse = await this.courseRepository.findOneAndUpdate(
      { _id: courseId },
      { $pull: { modules: { _id: moduleId } } },
      { new: true },
    );

    return updatedCourse;
  }

  async addCourseToUser(userId: string, courseId: string) {
    try {
      const user = await this.userRepository.findOne({ _id: userId });
      if (!user) {
        throw new NotFoundException("User not found");
      }

      const course = await this.courseRepository.findOne({ _id: courseId });
      if (!course) {
        throw new NotFoundException("Course not found");
      }

      // No need to check if the user already has the course
      const userObject = user.toObject ? user.toObject() : user;
      const courses = Array.isArray(userObject.courses) ? userObject.courses : [];
      courses.push(courseId);

      await this.userRepository.findOneAndUpdate(
        { _id: userId },
        { $set: { courses, updatedAt: new Date() } },
        { new: true }
      );

      return { success: true, message: "Course added to user successfully" };
    } catch (error) {
      throw error;
    }
  }

  async generateCourseZip(courseId: string): Promise<Buffer> {
    const course = await this.getCourseById(courseId);
    const archive = archiver('zip', { zlib: { level: 9 } });
    const buffers: Buffer[] = [];

    this.logger.log(`Generating ZIP for course: ${course.title} (ID: ${courseId})`);
    this.logger.log(`Course has ${course.modules?.length || 0} modules`);

    archive.on('data', (data) => buffers.push(data));

    try {
      // Create a directory for course content
      archive.append('', { name: 'course/' });
      archive.append('', { name: 'course/modules/' });

      // Add course information as a text file
      const courseInfo = {
        title: course.title,
        description: course.description,
        price: course.price,
        level: course.level || 'Beginner',
        certificate: course.certificate || false,
        hours: course.hours || 0,
      };
      archive.append(JSON.stringify(courseInfo, null, 2), { name: 'course/info.json' });

      // Add modules
      let moduleCount = 0;
      let successfulModules = 0;

      if (!course.modules || course.modules.length === 0) {
        this.logger.warn(`Course ${courseId} has no modules to include in the ZIP file`);
      }

      for (const module of course.modules || []) {
        moduleCount++;
        try {
          this.logger.log(`Processing module ${moduleCount}: ${module.title} (type: ${module.type})`);

          if (module.type === 'text') {
            // For text modules, save as text files
            this.logger.log(`Adding text module: ${module.title}`);
            archive.append(module.content || '', {
              name: `course/modules/${module.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.txt`,
            });
            successfulModules++;
          } else if (['image', 'pdf', 'video'].includes(module.type)) {
            // If we have a content URL but no publicId, we can try to download directly from the content URL
            if (!module.publicId && !module.content) {
              this.logger.error(`Module ${module.title} has type ${module.type} but no publicId or content URL`);
              continue;
            }

            try {
              let downloadUrl = '';

              // If we have a publicId, use S3 to generate a signed URL
              if (module.publicId) {
                const s3Service = this.fileUploadService.s3;

                this.logger.log(`Checking if file exists in S3 for module: ${module.title}, publicId: ${module.publicId}`);

                // First check if the file exists in S3
                const fileExists = await s3Service.fileExists(module.publicId);

                if (!fileExists) {
                  this.logger.error(`File does not exist in S3 for module: ${module.title}, publicId: ${module.publicId}`);
                  continue;
                }

                this.logger.log(`Generating signed URL for module: ${module.title}, publicId: ${module.publicId}, resourceType: ${module.resourceType || module.type}`);

                // Pass the resource type as the second parameter and a numeric expiration as the third
                downloadUrl = s3Service.generateSignedUrl(
                  module.publicId,  // Key
                  module.resourceType || module.type,  // Resource type (for logging only)
                  3600  // Explicit numeric expiration in seconds (1 hour)
                );

                if (!downloadUrl) {
                  this.logger.error(`Failed to generate signed URL for module: ${module.title}`);
                  continue;
                }
              }
              // If we don't have a publicId but have a content URL, use that directly
              else if (module.content) {
                this.logger.log(`Using direct content URL for module: ${module.title}, URL: ${module.content}`);
                downloadUrl = module.content;

                // Log the file extension we would get from this URL
                const urlExtension = this.getExtensionFromUrl(module.content);
                this.logger.log(`Detected file extension from URL: ${urlExtension || 'none'}`);
              }

              this.logger.log(`Downloading module content from URL for: ${module.title}`);
              try {
                const response = await axios.get(downloadUrl, {
                  responseType: 'arraybuffer',
                  timeout: 60000, // 60 second timeout (increased from 30s)
                  maxContentLength: 100 * 1024 * 1024, // 100MB max size
                  headers: {
                    'Accept': '*/*',
                    'Accept-Encoding': 'gzip, deflate, br'
                  }
                });

                if (response.status !== 200) {
                  this.logger.error(`Failed to download module content: ${response.status} ${response.statusText}`);
                  continue;
                }

                if (!response.data || response.data.length === 0) {
                  this.logger.error(`Downloaded empty content for module: ${module.title}`);
                  continue;
                }

                // Log response headers for debugging
                this.logger.log(`Response headers for ${module.title}: ${JSON.stringify(response.headers)}`);
                this.logger.log(`Content-Type: ${response.headers['content-type']}`);

                const extension = this.getFileExtension(
                  module.type,
                  response.headers['content-type'],
                  module.content // Pass the content URL to help determine extension
                );
                const filename = `course/modules/${module.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}${extension}`;

                this.logger.log(`Adding file to archive: ${filename} (${response.data.length} bytes)`);
                archive.append(response.data, { name: filename });
                successfulModules++;
              } catch (axiosError) {
                this.logger.error(`Axios error downloading module content: ${axiosError.message}`);
                if (axiosError.response) {
                  this.logger.error(`Response status: ${axiosError.response.status}`);
                  this.logger.error(`Response headers: ${JSON.stringify(axiosError.response.headers)}`);
                }
                if (axiosError.request) {
                  this.logger.error(`Request failed: ${axiosError.request}`);
                }
              }
            } catch (downloadError) {
              this.logger.error(`Error downloading module content: ${downloadError.message}`);
              if (downloadError.stack) {
                this.logger.error(`Stack trace: ${downloadError.stack}`);
              }
            }
          } else {
            this.logger.warn(`Skipping module with unsupported type: ${module.type}`);
          }
        } catch (moduleError) {
          this.logger.error(`Error processing module ${module.title}: ${moduleError.message}`);
          if (moduleError.stack) {
            this.logger.error(`Stack trace: ${moduleError.stack}`);
          }
        }
      }

      this.logger.log(`Successfully processed ${successfulModules} out of ${moduleCount} modules`);

      archive.finalize();

      return new Promise((resolve, reject) => {
        archive.on('end', () => {
          const finalBuffer = Buffer.concat(buffers);
          this.logger.log(`ZIP file generated successfully. Size: ${finalBuffer.length} bytes`);
          resolve(finalBuffer);
        });
        archive.on('error', (err) => {
          this.logger.error(`Archive error: ${err.message}`);
          reject(err);
        });
      });
    } catch (error) {
      this.logger.error(`Error generating course zip: ${error.message}`);
      if (error.stack) {
        this.logger.error(`Stack trace: ${error.stack}`);
      }
      throw new Error('Failed to generate course zip file');
    }
  }

  /**
   * Extract file extension from a URL
   * @param url URL to extract extension from
   * @returns File extension with dot (e.g., '.mp4') or empty string if not found
   */
  private getExtensionFromUrl(url: string): string {
    if (!url) return '';

    try {
      // Remove query parameters and hash
      const cleanUrl = url.split('?')[0].split('#')[0];

      // Get the filename from the URL
      const filename = cleanUrl.split('/').pop();

      if (!filename) return '';

      // Extract extension
      const parts = filename.split('.');
      if (parts.length > 1) {
        return `.${parts.pop().toLowerCase()}`;
      }
    } catch (error) {
      this.logger.error(`Error extracting extension from URL: ${error.message}`);
    }

    return '';
  }

  /**
   * Get file extension based on type and content type
   * @param type Module type (pdf, video, image)
   * @param contentType HTTP content type
   * @param url Optional URL to extract extension from
   * @returns File extension with dot (e.g., '.mp4')
   */
  private getFileExtension(type: string, contentType?: string, url?: string): string {
    // First try to get extension from URL if provided
    if (url) {
      const urlExtension = this.getExtensionFromUrl(url);
      if (urlExtension) {
        return urlExtension;
      }
    }

    // Then check content type if available
    if (contentType) {
      if (contentType.includes('pdf')) return '.pdf';
      if (contentType.includes('video')) {
        if (contentType.includes('mp4')) return '.mp4';
        if (contentType.includes('webm')) return '.webm';
        if (contentType.includes('ogg')) return '.ogg';
        if (contentType.includes('quicktime')) return '.mov';
        return '.mp4'; // Default video extension
      }
      if (contentType.includes('image')) {
        if (contentType.includes('png')) return '.png';
        if (contentType.includes('gif')) return '.gif';
        if (contentType.includes('svg')) return '.svg';
        if (contentType.includes('webp')) return '.webp';
        return '.jpg'; // Default image extension
      }
    }

    // Fallback to type
    if (type === 'pdf') return '.pdf';
    if (type === 'video') return '.mp4';
    if (type === 'image') return '.jpg';

    return '';
  }
}