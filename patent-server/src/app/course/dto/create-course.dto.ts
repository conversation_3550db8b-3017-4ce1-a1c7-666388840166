import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, Min } from 'class-validator';

import { ModuleDto } from './module.dto';

export class CreateCourseDto {
  @ApiProperty({ example: 'Advanced NestJS Course', description: 'Title of the course' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ example: 'Learn NestJS from scratch to advanced concepts', description: 'Description of the course' })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ example: 99.99, description: 'Price of the course' })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsNotEmpty()
  price: number;

  @ApiProperty({ example: 'https://example.com/image.jpg', description: 'Image URL for the course' })
  @IsString()
  @IsOptional()
  image?: string;

  @ApiProperty({ example: 'image_public_id', description: 'Public ID of image in Cloudinary' })
  @IsString()
  @IsOptional()
  imagePublicId?: string;

  @ApiProperty({
    type: [ModuleDto],
    description: 'Array of course modules'
  })
  @IsArray()
  @IsOptional()
  modules?: ModuleDto[];

  @ApiProperty({ example: true, description: 'Whether a certificate is included with the course' })
  @IsBoolean()
  @IsOptional()
  certificate?: boolean;

  @ApiProperty({
    example: 'Beginner',
    description: 'Level of the course',
    enum: ['Beginner', 'Intermediate', 'Expert']
  })
  @IsEnum(['Beginner', 'Intermediate', 'Expert'])
  @IsOptional()
  level?: string;

  @ApiProperty({ example: 35, description: 'Duration of the course in hours' })
  @IsNumber()
  @Min(0)
  @IsOptional()
  hours?: number;

  // @ApiProperty({ example: '6073a5ae59a820e8e8c52123', description: 'ID of instructor user' })
  // @IsMongoId()
  // @IsNotEmpty()
  // instructor: string;
}