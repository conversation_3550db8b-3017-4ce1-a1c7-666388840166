import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class ModuleDto {
  @ApiProperty({ example: 'Introduction to NestJS', description: 'Title of the module' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ example: 'text', description: 'Type of content', enum: ['text', 'pdf', 'image', 'video'] })
  @IsEnum(['text', 'pdf', 'image', 'video'])
  @IsNotEmpty()
  type: string;

  @ApiProperty({ example: 'This is the content or URL to the content', description: 'Content or URL to content' })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({ example: 1, description: 'Order of the module in the course' })
  @IsNumber()
  @IsOptional()
  order?: number;

  @ApiProperty({ example: 'public_id_from_s3', description: 'Public ID from S3' })
  @IsString()
  @IsOptional()
  publicId?: string;

  @ApiProperty({ example: 'image', description: 'Type of resource', enum: ['image', 'video', 'raw', 'pdf', ''] })
  @IsEnum(['image', 'video', 'raw', 'pdf', ''])
  @IsOptional()
  resourceType?: string;
  
  @ApiProperty({ example: true, description: 'Field to track if this module is being updated with new content' })
  @IsOptional()
  fileIndex?: number;
}