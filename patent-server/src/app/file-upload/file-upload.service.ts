import { S3Service } from '@app/services/s3/s3.service';
import { Injectable } from '@nestjs/common';

// Define the File interface to match <PERSON>lter's structure
interface MulterFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  destination?: string;
  filename?: string;
  path?: string;
  buffer?: Buffer;
}

@Injectable()
export class FileUploadService {
  constructor(private readonly s3Service: S3Service) {}

  async uploadFile(file: MulterFile, folder = 'courses', options = {}) {
    // Set resource type based on file mimetype
    const resourceType = this.getResourceType(file.mimetype);
    const result = await this.s3Service.uploadFile(file, folder, { 
      ...options,
      resourceType 
    });
    return result;
  }

  async uploadFiles(files: MulterFile[], folder = 'courses', options = {}) {
    const uploadPromises = files.map(file => this.uploadFile(file, folder, options));
    return Promise.all(uploadPromises);
  }

  async deleteFile(key: string) {
    return this.s3Service.deleteFile(key);
  }

  private getResourceType(mimetype: string): string {
    if (mimetype.startsWith('image/')) return 'image';
    if (mimetype.startsWith('video/')) return 'video';
    if (mimetype === 'application/pdf') return 'pdf';
    return 'raw';
  }

  get s3() {
    return this.s3Service;
  }
}