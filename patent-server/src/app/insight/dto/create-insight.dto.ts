import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { 
  IsString, 
  IsNotEmpty, 
  IsArray, 
  IsMongoId,
  IsEnum,
  ValidateNested,
  ArrayMinSize
} from 'class-validator';

class ImageDto {
  @ApiProperty({ description: 'Image URL' })
  @IsString()
  @IsNotEmpty()
  url: string;

  @ApiProperty({ description: 'Image public ID (for cloud storage like Cloudinary)' })
  @IsString()
  @IsNotEmpty()
  publicId: string;
}

class RelatedEntityDto {
  @ApiProperty({ description: 'Type of related entity', enum: ['book', 'course'] })
  @IsEnum(['book', 'course'])
  @IsNotEmpty()
  type: 'book' | 'course';

  @ApiProperty({ description: 'ID of the related book or course' })
  @IsMongoId()
  @IsNotEmpty()
  id: string;
}

export class CreateInsightDto {
  @ApiProperty({ description: 'Title of the insight' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ description: 'Detailed content of the insight' })
  @IsString()
  @IsNotEmpty()
  details: string;

  @ApiProperty({ description: 'Images related to the insight', type: [ImageDto] })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => ImageDto)
  images: ImageDto[];

  @ApiProperty({ description: 'Related book or course', type: RelatedEntityDto })
  @ValidateNested()
  @Type(() => RelatedEntityDto)
  relatedTo: RelatedEntityDto;
}
