import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsMongoId, IsNotEmpty, IsString } from 'class-validator';

export class UploadInsightImagesDto {
  @ApiProperty({ description: 'Type of related entity', enum: ['book', 'course'] })
  @IsEnum(['book', 'course'])
  @IsNotEmpty()
  type: 'book' | 'course';

  @ApiProperty({ description: 'ID of the related book or course' })
  @IsMongoId()
  @IsNotEmpty()
  entityId: string;

  @ApiProperty({ description: 'Title for the insight' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ description: 'Details for the insight' })
  @IsString()
  @IsNotEmpty()
  details: string;
}
