import { AuthGuard } from '@app/guards/auth/auth.guard';
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
  HttpStatus,
  UsePipes,
  ValidationPipe,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiConsumes, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { FileUploadService } from '../file-upload/file-upload.service';

import { CreateInsightDto, UpdateInsightDto, UploadInsightImagesDto } from './dto';
import { InsightService } from './insight.service';

@ApiTags('Insights')
@Controller('insights')
export class InsightController {
  constructor(
    private readonly insightService: InsightService,
    private readonly fileUploadService: FileUploadService
  ) {}

  @Post()
  // @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new insight (Admin only)' })
  @ApiResponse({ status: HttpStatus.CREATED, description: 'Insight created successfully.' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized.' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Forbidden. Admin access required.' })
  @UsePipes(new ValidationPipe({ transform: true }))
  async create(@Body() createInsightDto: any, @Req() req) {
    return this.insightService.create(createInsightDto, req.user.user.sub);
  }

  @Get()
  @ApiOperation({ summary: 'Get all insights' })
  @ApiResponse({ status: HttpStatus.OK, description: 'List of insights returned.' })
  async findAll() {
    return this.insightService.findAll();
  }

  @Get('related')
  @ApiOperation({ summary: 'Get insights by related entity type and ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Related insights returned.' })
  async findByRelatedEntity(
    @Query('type') type: 'Book' | 'Course', // Changed to uppercase
    @Query('id') id: string,
  ) {
    return this.insightService.findByRelatedEntity(type, id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get insight by ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Insight returned.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Insight not found.' })
  async findOne(@Param('id') id: string) {
    return this.insightService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update an insight (Admin only)' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Insight updated successfully.' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized.' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Forbidden. Admin access required.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Insight not found.' })
  @UsePipes(new ValidationPipe({ transform: true }))
  async update(@Param('id') id: string, @Body() updateInsightDto: any) {
    return this.insightService.update(id, updateInsightDto);
  }

  @Delete(':id')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete an insight (Admin only)' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Insight deleted successfully.' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized.' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Forbidden. Admin access required.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Insight not found.' })
  async remove(@Param('id') id: string) {
    return this.insightService.remove(id);
  }
  
  @Post('upload')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload insight images and create insight (Admin only)' })
  @UseInterceptors(FileFieldsInterceptor([
    { name: 'images', maxCount: 5 },
  ]))
  async uploadImages(
    @UploadedFiles() files: { images?: Express.Multer.File[] },
    @Body() uploadDto: any,
    @Req() req
  ) {
    // Check if images are provided
    if (!files.images || files.images.length === 0) {
      return { 
        success: false, 
        message: 'No images uploaded' 
      };
    }
    
    try {
      // Upload images to storage
      const uploadedImages = await Promise.all(
        files.images.map(file => 
          this.fileUploadService.uploadFile(file, 'insights')
        )
      );
      
      // Create insight with uploaded images
      const insightData: CreateInsightDto = {
        title: uploadDto.title,
        details: uploadDto.details,
        images: uploadedImages.map(img => ({
          url: img.url,
          publicId: img.public_id || img.publicId // Handle both naming conventions
        })),
        relatedTo: {
          type: uploadDto.type,
          id: uploadDto.entityId
        }
      };
      
      const insight = await this.insightService.create(insightData, req.user.user.sub);
      
      return {
        success: true,
        message: 'Insight created with images',
        insight
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to create insight',
        error: error.message
      };
    }
  }
}
