import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { RepositoryModule } from '@app/repositories/repository.module';
import { InsightSchema } from '@app/database';
import { S3Module } from '@app/services/s3/s3.module';

import { FileUploadModule } from '../file-upload/file-upload.module';
import { InsightController } from './insight.controller';
import { InsightService } from './insight.service';
import { InsightRepository } from '@app/repositories/insight.repository';


@Module({
  imports: [
    RepositoryModule.forRoot(), // Explicitly import RepositoryModule to ensure dependencies are available
    MongooseModule.forFeature([{ name: 'Insight', schema: InsightSchema }]),
    FileUploadModule,
    S3Module
  ],
  controllers: [InsightController],
  providers: [
    InsightService,
    InsightRepository // Directly provide InsightRepository in this module
  ],
  exports: [InsightService],
})
export class InsightModule {}
