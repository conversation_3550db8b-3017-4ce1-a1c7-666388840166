import { InsightRepository, InsightEntity } from '@app/repositories/insight.repository';
import { Injectable } from '@nestjs/common';
import { Types } from 'mongoose';

import { CreateInsightDto, UpdateInsightDto } from './dto';

@Injectable()
export class InsightService {
  constructor(
    private readonly insightRepository: InsightRepository,
  ) {}

  async create(createInsightDto: any, userId: string): Promise<InsightEntity> {
    const insightData = {
      ...createInsightDto,
      createdBy: new Types.ObjectId(userId),
      relatedTo: {
        ...createInsightDto.relatedTo,
        id: new Types.ObjectId(createInsightDto.relatedTo.id)
      }
    };
    return this.insightRepository.create(insightData as any);
  }

  async findAll(filter: any = {}): Promise<InsightEntity[]> {
    return this.insightRepository.findAllWithPopulate(filter);
  }

  async findOne(id: string): Promise<InsightEntity | null> {
    return this.insightRepository.findOneByIdWithPopulate(id);
  }

  async update(id: string, updateInsightDto: any): Promise<InsightEntity | null> {
    let updateData: any = { ...updateInsightDto };
    if (updateInsightDto.relatedTo && updateInsightDto.relatedTo.id) {
      updateData.relatedTo = {
        ...updateInsightDto.relatedTo,
        id: new Types.ObjectId(updateInsightDto.relatedTo.id),
      };
    }
    return this.insightRepository.findOneAndUpdate({ _id: new Types.ObjectId(id) }, updateData, { new: true });
  }

  async remove(id: string): Promise<{ deleted: boolean }> {
    const result = await this.insightRepository.deleteOne({ _id: new Types.ObjectId(id) });
    return { deleted: result.deletedCount > 0 };
  }

  async findByRelatedEntity(type: 'Book' | 'Course', entityId: string): Promise<InsightEntity[]> {
    return this.insightRepository.findByRelatedEntityWithPopulate(type, entityId);
  }
}
