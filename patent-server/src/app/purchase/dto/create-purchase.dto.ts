import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString, ValidateIf, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

// Shipping address DTO
export class ShippingAddressDto {
  @ApiProperty({ example: '123 Main St', description: 'Street address' })
  @IsString()
  @IsNotEmpty()
  street: string;

  @ApiProperty({ example: 'New York', description: 'City' })
  @IsString()
  @IsNotEmpty()
  city: string;

  @ApiProperty({ example: 'NY', description: 'State or province' })
  @IsString()
  @IsNotEmpty()
  state: string;

  @ApiProperty({ example: '10001', description: 'Zip or postal code' })
  @IsString()
  @IsNotEmpty()
  zipCode: string;

  @ApiProperty({ example: 'US', description: 'Country code' })
  @IsString()
  @IsNotEmpty()
  country: string;

  @ApiProperty({ example: '+1234567890', description: 'Phone number' })
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;
}

export class CreatePurchaseDto {
  @ApiProperty({ example: '6073a5ae59a820e8e8c52123', description: 'User ID making the purchase' })
  @IsMongoId()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    example: '6073a5ae59a820e8e8c52456',
    description: 'Course ID being purchased (required if bookId is not provided)'
  })
  @IsMongoId()
  @IsOptional()
  courseId?: string;

  @ApiProperty({
    example: '6073a5ae59a820e8e8c52457',
    description: 'Book ID being purchased (required if courseId is not provided)'
  })
  @IsMongoId()
  @IsOptional()
  bookId?: string;

  @ApiProperty({ example: 99.99, description: 'Amount paid for the course/book' })
  @IsNumber()
  @IsNotEmpty()
  amount: number;

  @ApiProperty({
    example: 'Completed',
    description: 'Status of the purchase',
    enum: ['Pending', 'Completed', 'Failed', 'Refunded'],
    default: 'Completed'
  })
  @IsEnum(['Pending', 'Completed', 'Failed', 'Refunded'])
  @IsOptional()
  status?: string;

  @ApiProperty({
    example: 'CreditCard',
    description: 'Method of payment',
    enum: ['CreditCard', 'PayPal', 'BankTransfer', 'Crypto', 'Other']
  })
  @IsEnum(['CreditCard', 'PayPal', 'BankTransfer', 'Crypto', 'Other'])
  @IsNotEmpty()
  paymentMethod: string;

  @ApiProperty({ example: 'txn_12345abcde', description: 'Unique transaction ID from payment processor' })
  @IsString()
  @IsNotEmpty()
  transactionId: string;

  @ApiProperty({ example: true, description: 'Whether the purchase requires shipping (for hardcopy books)' })
  @IsBoolean()
  @IsOptional()
  requiresShipping?: boolean;

  @ApiProperty({
    description: 'Shipping address information (required if requiresShipping is true)',
    type: ShippingAddressDto
  })
  @ValidateIf(o => o.requiresShipping === true)
  @ValidateNested()
  @Type(() => ShippingAddressDto)
  @IsObject()
  shippingAddress?: ShippingAddressDto;
}