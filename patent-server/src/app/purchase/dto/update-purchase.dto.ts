import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsMongoId, IsNumber, IsObject, IsOptional, IsString, ValidateIf, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ShippingAddressDto } from './create-purchase.dto';

export class UpdatePurchaseDto {
  @ApiProperty({ example: '6073a5ae59a820e8e8c52123', description: 'User ID making the purchase' })
  @IsMongoId()
  @IsOptional()
  userId?: string;

  @ApiProperty({ example: '6073a5ae59a820e8e8c52456', description: 'Course ID being purchased' })
  @IsMongoId()
  @IsOptional()
  courseId?: string;

  @ApiProperty({ example: '6073a5ae59a820e8e8c52457', description: 'Book ID being purchased' })
  @IsMongoId()
  @IsOptional()
  bookId?: string;

  @ApiProperty({ example: 99.99, description: 'Amount paid for the course' })
  @IsNumber()
  @IsOptional()
  amount?: number;

  @ApiProperty({
    example: 'Completed',
    description: 'Status of the purchase',
    enum: ['Pending', 'Completed', 'Failed', 'Refunded']
  })
  @IsEnum(['Pending', 'Completed', 'Failed', 'Refunded'])
  @IsOptional()
  status?: string;

  @ApiProperty({
    example: 'CreditCard',
    description: 'Method of payment',
    enum: ['CreditCard', 'PayPal', 'BankTransfer', 'Crypto', 'Other']
  })
  @IsEnum(['CreditCard', 'PayPal', 'BankTransfer', 'Crypto', 'Other'])
  @IsOptional()
  paymentMethod?: string;

  @ApiProperty({ example: 'txn_12345abcde', description: 'Unique transaction ID from payment processor' })
  @IsString()
  @IsOptional()
  transactionId?: string;

  @ApiProperty({ example: true, description: 'Whether the purchase requires shipping (for hardcopy books)' })
  @IsBoolean()
  @IsOptional()
  requiresShipping?: boolean;

  @ApiProperty({
    description: 'Shipping address information (required if requiresShipping is true)',
    type: ShippingAddressDto
  })
  @ValidateIf(o => o.requiresShipping === true)
  @ValidateNested()
  @Type(() => ShippingAddressDto)
  @IsObject()
  @IsOptional()
  shippingAddress?: ShippingAddressDto;
}