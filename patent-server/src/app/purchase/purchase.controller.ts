import { AuthGuard } from "@app/guards/auth/auth.guard";
import { RequestWithUser } from "@app/interfaces";
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Req,
  HttpCode,
  NotFoundException,
  Delete,
  Put,
  Query,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";

import { CreatePurchaseDto, UpdatePurchaseDto } from "./dto";
import { PurchaseService } from "./purchase.service";

@ApiTags("Purchases")
@ApiBearerAuth()
@Controller("purchases")
export class PurchaseController {
  constructor(private readonly purchaseService: PurchaseService) {}

  @UseGuards(AuthGuard)
  @Get("dashboard")
  @HttpCode(200)
  @ApiOperation({
    summary: "Get total purchase count for dashboard (Admin only)",
  })
  @ApiResponse({ status: 200, description: "Return total purchase count" })
  async getAllPurchasesForDashboard(@Req() request: RequestWithUser) {
    const isAdmin =
      request.user?.role === "Admin" ||
      request.user?.role === "admin" ||
      request.user?.isAdmin === true ||
      (request.user?.user?.userInfo &&
        request.user?.user?.userInfo.includes('isAdmin":true'));

    if (!isAdmin) {
      throw new NotFoundException("Not authorized: Admin access required");
    }

    return this.purchaseService.getAllPurchasesForDashboard();
  }

  @UseGuards(AuthGuard)
  @Get()
  @HttpCode(200)
  @ApiOperation({ summary: "Get all purchases with pagination (Admin only)" })
  @ApiResponse({ status: 200, description: "Return paginated purchases" })
  async getAllPurchases(
    @Req() request: RequestWithUser,
    @Query("page") page: string = "1",
    @Query("limit") limit: string = "6"
  ) {
    const isAdmin =
      request.user?.role === "Admin" ||
      request.user?.role === "admin" ||
      request.user?.isAdmin === true ||
      (request.user?.user?.userInfo &&
        request.user?.user?.userInfo.includes('isAdmin":true'));

    if (!isAdmin) {
      throw new NotFoundException("Not authorized: Admin access required");
    }

    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 6;

    return this.purchaseService.getAllPurchases(pageNum, limitNum);
  }

  @UseGuards(AuthGuard)
  @Get("my/dashboard")
  @HttpCode(200)
  @ApiOperation({ summary: "Get user's total purchase count for dashboard" })
  @ApiResponse({
    status: 200,
    description: "Return user's total purchase count",
  })
  async getUserPurchasesForDashboard(@Req() request: RequestWithUser) {
    if (!request.user?.user?.sub) {
      throw new NotFoundException("User ID not found in token");
    }

    return this.purchaseService.getUserPurchasesForDashboard(
      request.user.user.sub
    );
  }

  @UseGuards(AuthGuard)
  @Get("my")
  @HttpCode(200)
  @ApiOperation({ summary: "Get user's purchases with pagination" })
  @ApiResponse({
    status: 200,
    description: "Return paginated user's purchases",
  })
  async getUserPurchases(
    @Req() request: RequestWithUser,
    @Query("page") page: string = "1",
    @Query("limit") limit: string = "6"
  ) {
    if (!request.user?.user?.sub) {
      throw new NotFoundException("User ID not found in token");
    }

    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 6;

    return this.purchaseService.getPurchasesByUser(
      request.user.user.sub,
      pageNum,
      limitNum
    );
  }

  @UseGuards(AuthGuard)
  @Get("my/courses")
  @HttpCode(200)
  @ApiOperation({ summary: "Get user's purchased courses without duplicates" })
  @ApiResponse({ status: 200, description: "Return user's purchased courses" })
  async getUserCourses(@Req() request: RequestWithUser) {
    return this.purchaseService.getUserPurchasedCourses(
      request.user?.user?.sub
    );
  }

  @UseGuards(AuthGuard)
  @Get("my/books")
  @HttpCode(200)
  @ApiOperation({ summary: "Get user's purchased books without duplicates" })
  @ApiResponse({ status: 200, description: "Return user's purchased books" })
  async getUserBooks(@Req() request: RequestWithUser) {
    return this.purchaseService.getUserPurchasedBooks(request.user?.user?.sub);
  }

  @UseGuards(AuthGuard)
  @Get("check/:itemType/:itemId")
  @HttpCode(200)
  @ApiOperation({ summary: "Check if user has purchased a specific item" })
  @ApiResponse({
    status: 200,
    description: "Returns true if user has purchased the item, false otherwise",
  })
  async checkUserOwnsItem(
    @Param("itemType") itemType: "course" | "book",
    @Param("itemId") itemId: string,
    @Req() request: RequestWithUser
  ) {
    if (itemType !== "course" && itemType !== "book") {
      return {
        owns: false,
        error: 'Invalid item type. Must be "course" or "book".',
      };
    }

    const owns = await this.purchaseService.checkUserOwnsItem(
      request.user?.user?.sub,
      itemId,
      itemType
    );

    return { owns };
  }

  @UseGuards(AuthGuard)
  @Get(":id")
  @HttpCode(200)
  @ApiOperation({ summary: "Get purchase by ID" })
  @ApiResponse({ status: 200, description: "Return purchase details" })
  async getPurchaseById(
    @Param("id") id: string,
    @Req() request: RequestWithUser
  ) {
    const purchase = await this.purchaseService.getPurchaseById(id);
    if (
      purchase.userId.toString() !== request.user?.user?.sub &&
      request.user?.role !== "Admin"
    ) {
      throw new NotFoundException("Not authorized to access this resource");
    }

    return purchase;
  }

  @UseGuards(AuthGuard)
  @Post()
  @HttpCode(201)
  @ApiOperation({ summary: "Create a new purchase" })
  @ApiResponse({ status: 201, description: "Purchase has been created" })
  async createPurchase(
    @Body() createPurchaseDto: CreatePurchaseDto,
    @Req() request: RequestWithUser
  ) {
    createPurchaseDto.userId = request.user?.user?.sub;
    return this.purchaseService.createPurchase(createPurchaseDto);
  }

  @UseGuards(AuthGuard)
  @Put(":id")
  @HttpCode(200)
  @ApiOperation({ summary: "Update a purchase (Admin only)" })
  @ApiResponse({ status: 200, description: "Purchase has been updated" })
  async updatePurchase(
    @Param("id") id: string,
    @Body() updatePurchaseDto: UpdatePurchaseDto,
    @Req() request: RequestWithUser
  ) {
    if (request.user?.role !== "Admin") {
      throw new NotFoundException("Not authorized to update purchases");
    }

    return this.purchaseService.updatePurchase(id, updatePurchaseDto);
  }

  @UseGuards(AuthGuard)
  @Delete(":id")
  @HttpCode(200)
  @ApiOperation({ summary: "Delete a purchase (Admin only)" })
  @ApiResponse({ status: 200, description: "Purchase has been deleted" })
  async deletePurchase(
    @Param("id") id: string,
    @Req() request: RequestWithUser
  ) {
    if (request.user?.role !== "Admin") {
      throw new NotFoundException("Not authorized to delete purchases");
    }

    return this.purchaseService.deletePurchase(id);
  }
}
