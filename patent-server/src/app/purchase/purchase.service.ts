import {
  Injectable,
  Inject,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";

import { CreatePurchaseDto, UpdatePurchaseDto } from "./dto";

@Injectable()
export class PurchaseService {
  constructor(
    @Inject("PurchaseRepository") private readonly purchaseRepository,
    @Inject("UserRepository") private readonly userRepository,
    @Inject("CourseRepository") private readonly courseRepository,
    @Inject("BookRepository") private readonly bookRepository
  ) {}

  async getAllPurchases(page = 1, limit = 6) {
    const skip = (page - 1) * limit;

    const totalCount = await this.purchaseRepository.count({});

    const purchases = await this.purchaseRepository.getModel
      .find({})
      .populate("userId", "firstName lastName username email")
      .populate("courseId")
      .populate("bookId")
      .skip(skip)
      .limit(limit)
      .exec();

    return {
      purchases,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        pageSize: limit,
        hasNext: page < Math.ceil(totalCount / limit),
        hasPrevious: page > 1,
      },
    };
  }

  async getAllPurchasesForDashboard() {
    const totalCount = await this.purchaseRepository.count({});

    return { totalCount };
  }

  async getPurchaseById(purchaseId: string) {
    const purchase = await this.purchaseRepository
      .findOne({ _id: purchaseId })
      .populate("userId", "firstName lastName username email")
      .populate("courseId")
      .populate("bookId");

    if (!purchase) {
      throw new NotFoundException("Purchase not found");
    }
    return purchase;
  }

  async getPurchasesByUser(userId: string, page = 1, limit = 6) {
    if (!userId) {
      throw new BadRequestException("User ID is required");
    }

    const user = await this.userRepository.findOne({ _id: userId });
    if (!user) {
      throw new BadRequestException("User not found");
    }

    const totalCount = await this.purchaseRepository.count({ userId: userId });

    if (totalCount === 0) {
      return {
        purchases: [],
        pagination: {
          totalCount: 0,
          totalPages: 0,
          currentPage: page,
          pageSize: limit,
          hasNext: false,
          hasPrevious: false,
        },
      };
    }

    const skip = (page - 1) * limit;

    const purchases = await this.purchaseRepository.getModel
      .find({ userId: userId })
      .populate("userId", "firstName lastName username email")
      .populate("courseId")
      .populate("bookId")
      .skip(skip)
      .limit(limit)
      .exec();

    return {
      purchases,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        pageSize: limit,
        hasNext: page < Math.ceil(totalCount / limit),
        hasPrevious: page > 1,
      },
    };
  }

  async getUserPurchasesForDashboard(userId: string) {
    if (!userId) {
      throw new BadRequestException("User ID is required");
    }

    const user = await this.userRepository.findOne({ _id: userId });
    if (!user) {
      throw new BadRequestException("User not found");
    }

    const totalCount = await this.purchaseRepository.count({ userId: userId });

    return { totalCount };
  }

  async createPurchase(createPurchaseDto: CreatePurchaseDto) {
    const user = await this.userRepository.findOne({
      _id: createPurchaseDto.userId,
    });
    if (!user) {
      throw new BadRequestException("User not found");
    }

    if (
      (createPurchaseDto.courseId && createPurchaseDto.bookId) ||
      (!createPurchaseDto.courseId && !createPurchaseDto.bookId)
    ) {
      throw new BadRequestException(
        "Please provide either courseId or bookId, but not both"
      );
    }

    if (createPurchaseDto.courseId) {
      const course = await this.courseRepository.findOne({
        _id: createPurchaseDto.courseId,
      });
      if (!course) {
        throw new BadRequestException("Course not found");
      }
    }

    if (createPurchaseDto.bookId) {
      const book = await this.bookRepository.findOne({
        _id: createPurchaseDto.bookId,
      });
      if (!book) {
        throw new BadRequestException("Book not found");
      }

      if (createPurchaseDto.status === "Completed") {
        await this.userRepository.findOneAndUpdate(
          { _id: createPurchaseDto.userId },
          { $addToSet: { books: createPurchaseDto.bookId } }
        );
      }
    }

    const purchase = await this.purchaseRepository.create(createPurchaseDto);
    return purchase;
  }

  async updatePurchase(
    purchaseId: string,
    updatePurchaseDto: UpdatePurchaseDto
  ) {
    const purchase = await this.purchaseRepository.findOne({ _id: purchaseId });
    if (!purchase) {
      throw new NotFoundException("Purchase not found");
    }

    if (updatePurchaseDto.userId) {
      const user = await this.userRepository.findOne({
        _id: updatePurchaseDto.userId,
      });
      if (!user) {
        throw new BadRequestException("User not found");
      }
    }

    if (updatePurchaseDto.courseId) {
      const course = await this.courseRepository.findOne({
        _id: updatePurchaseDto.courseId,
      });
      if (!course) {
        throw new BadRequestException("Course not found");
      }
    }

    if (updatePurchaseDto.bookId) {
      const book = await this.bookRepository.findOne({
        _id: updatePurchaseDto.bookId,
      });
      if (!book) {
        throw new BadRequestException("Book not found");
      }
    }

    if (
      purchase.bookId &&
      purchase.status !== "Completed" &&
      updatePurchaseDto.status === "Completed"
    ) {
      await this.userRepository.findOneAndUpdate(
        { _id: purchase.userId },
        { $addToSet: { books: purchase.bookId } }
      );
    }

    const updatedPurchase = await this.purchaseRepository.findOneAndUpdate(
      { _id: purchaseId },
      updatePurchaseDto,
      { new: true }
    );
    return updatedPurchase;
  }

  async deletePurchase(purchaseId: string) {
    const purchase = await this.purchaseRepository.findOne({ _id: purchaseId });
    if (!purchase) {
      throw new NotFoundException("Purchase not found");
    }

    if (purchase.bookId && purchase.status === "Completed") {
      await this.userRepository.findOneAndUpdate(
        { _id: purchase.userId },
        { $pull: { books: purchase.bookId } }
      );
    }

    await this.purchaseRepository.deleteOne({ _id: purchaseId });
    return { message: "Purchase deleted successfully" };
  }

  async getUserPurchasedCourses(userId: string) {
    const user = await this.userRepository.findOne({ _id: userId });
    if (!user) {
      throw new BadRequestException("User not found");
    }

    const query = await this.purchaseRepository.getMany({
      userId,
      courseId: { $exists: true }, 
      status: "Completed",
    });

    const purchases = await this.purchaseRepository.getModel
      .find(query._conditions)
      .populate("userId", "firstName lastName username email")
      .populate("courseId")
      .exec();

    const courses = purchases
      .map((purchase) => purchase.courseId)
      .filter(Boolean);

    return courses;
  }

  async getUserPurchasedBooks(userId: string) {
    const user = await this.userRepository
      .findOne({ _id: userId })
      .populate("books");
    if (!user) {
      throw new BadRequestException("User not found");
    }

    return user.books || [];
  }

  async checkUserOwnsItem(
    userId: string,
    itemId: string,
    itemType: "course" | "book"
  ) {
    const user = await this.userRepository.findOne({ _id: userId });
    if (!user) {
      throw new BadRequestException("User not found");
    }

    if (itemType === "course") {
      const purchase = await this.purchaseRepository.findOne({
        userId,
        courseId: itemId,
        status: "Completed",
      });

      return !!purchase;
    } else if (itemType === "book") {
      return user.books && user.books.includes(itemId);
    }

    return false;
  }
}
