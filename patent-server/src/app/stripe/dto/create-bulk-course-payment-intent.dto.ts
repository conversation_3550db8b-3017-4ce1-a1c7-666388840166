import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON>y, IsMongoId, IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateBulkCoursePaymentIntentDto {
  @ApiProperty({ 
    example: ['6073a5ae59a820e8e8c52456', '6073a5ae59a820e8e8c52457'], 
    description: 'Array of course IDs being purchased', 
    required: true 
  })
  @IsArray()
  @IsMongoId({ each: true })
  courseIds: string[];

  @ApiProperty({ example: 99.99, description: 'Total amount for all courses', required: true })
  @IsNumber()
  totalAmount: number;

  @ApiProperty({ example: 'usd', description: 'Currency code', default: 'usd' })
  @IsString()
  @IsOptional()
  currency?: string = 'usd';

  @ApiProperty({ example: 'Bulk course purchase', description: 'Description of the payment' })
  @IsString()
  @IsOptional()
  description?: string;
}
