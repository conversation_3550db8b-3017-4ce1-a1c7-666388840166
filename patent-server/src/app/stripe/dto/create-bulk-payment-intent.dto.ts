import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON>y, IsMongoId, IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateBulkPaymentIntentDto {
  @ApiProperty({ example: ['6073a5ae59a820e8e8c52456', '6073a5ae59a820e8e8c52457'], description: 'Array of Book IDs being purchased', required: true })
  @IsArray()
  @IsMongoId({ each: true })
  bookIds: string[];

  @ApiProperty({ example: 99.99, description: 'Total amount for all books', required: true })
  @IsNumber()
  totalAmount: number;

  @ApiProperty({ example: 'usd', description: 'Currency code', default: 'usd' })
  @IsString()
  @IsOptional()
  currency?: string = 'usd';

  @ApiProperty({ example: 'Bulk book purchase', description: 'Description of the payment' })
  @IsString()
  @IsOptional()
  description?: string;
}
