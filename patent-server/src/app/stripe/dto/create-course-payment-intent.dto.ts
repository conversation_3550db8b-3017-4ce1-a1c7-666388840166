import { ApiProperty } from '@nestjs/swagger';
import { IsMongoId, IsOptional, IsString } from 'class-validator';

export class CreateCoursePaymentIntentDto {
  @ApiProperty({ example: '6073a5ae59a820e8e8c52456', description: 'Course ID being purchased', required: true })
  @IsMongoId()
  courseId: string;

  @ApiProperty({ example: 'usd', description: 'Currency code', default: 'usd' })
  @IsString()
  @IsOptional()
  currency?: string = 'usd';

  @ApiProperty({ example: 'Course payment', description: 'Description of the payment' })
  @IsString()
  @IsOptional()
  description?: string;
}
