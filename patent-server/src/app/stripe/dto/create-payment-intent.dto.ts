import { ApiProperty } from '@nestjs/swagger';
import { IsMongoId, IsOptional, IsString } from 'class-validator';

export class CreatePaymentIntentDto {
  @ApiProperty({ example: '6073a5ae59a820e8e8c52456', description: 'Course ID being purchased', required: false })
  @IsMongoId()
  @IsOptional()
  courseId?: string;

  @ApiProperty({ example: '6073a5ae59a820e8e8c52457', description: 'Book ID being purchased', required: false })
  @IsMongoId()
  @IsOptional()
  bookId?: string;

  @ApiProperty({ example: 'usd', description: 'Currency code', default: 'usd' })
  @IsString()
  @IsOptional()
  currency?: string = 'usd';

  @ApiProperty({ example: 'Test course payment', description: 'Description of the payment' })
  @IsString()
  @IsOptional()
  description?: string;
}