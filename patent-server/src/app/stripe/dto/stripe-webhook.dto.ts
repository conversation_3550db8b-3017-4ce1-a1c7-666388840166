import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsObject, IsString } from 'class-validator';

export class StripeWebhookDto {
  @ApiProperty({ description: 'Stripe event ID' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ description: 'Type of webhook event' })
  @IsString()
  @IsNotEmpty()
  type: string;

  @ApiProperty({ description: 'Event data object containing all event details' })
  @IsObject()
  @IsNotEmpty()
  data: any;
}