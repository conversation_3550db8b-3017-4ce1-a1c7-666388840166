import { AuthGuard } from "@app/guards/auth/auth.guard";
import { RequestWithUser } from "@app/interfaces";
import {
  Controller,
  Get,
  Post,
  Body,
  UseGuards,
  Req,
  Headers,
  RawBodyRequest,
  HttpCode,
  Logger,
  NotFoundException,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";

import { CreatePaymentIntentDto } from "./dto";
import { CreateBulkCoursePaymentIntentDto } from "./dto/create-bulk-course-payment-intent.dto";
import { CreateBulkPaymentIntentDto } from "./dto/create-bulk-payment-intent.dto";
import { CreateCoursePaymentIntentDto } from "./dto/create-course-payment-intent.dto";
import { StripeService } from "./stripe.service";

@ApiTags("Payments")
@Controller()
export class StripeController {
  private readonly logger = new Logger(StripeController.name);

  constructor(private readonly stripeService: StripeService) {}

  @Post("stripe/payment-intent")
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @HttpCode(200)
  @ApiOperation({
    summary: "Create a payment intent for purchasing a course or book",
  })
  @ApiResponse({
    status: 200,
    description: "Payment intent created successfully",
  })
  async createPaymentIntent(
    @Body() createPaymentIntentDto: CreatePaymentIntentDto,
    @Req() request: RequestWithUser
  ) {
    const userId = request?.user?.user?.sub;
    return this.stripeService.createPaymentIntent(
      userId,
      createPaymentIntentDto
    );
  }

  @Post("stripe/course-payment-intent")
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @HttpCode(200)
  @ApiOperation({ summary: "Create a payment intent for purchasing a course" })
  @ApiResponse({
    status: 200,
    description: "Course payment intent created successfully",
  })
  async createCoursePaymentIntent(
    @Body() createCoursePaymentIntentDto: CreateCoursePaymentIntentDto,
    @Req() request: RequestWithUser
  ) {
    const userId = request?.user?.user?.sub;
    const paymentIntentDto: CreatePaymentIntentDto = {
      courseId: createCoursePaymentIntentDto.courseId,
      currency: createCoursePaymentIntentDto.currency,
      description: createCoursePaymentIntentDto.description,
    };

    return this.stripeService.createPaymentIntent(userId, paymentIntentDto);
  }

  @Post("stripe/bulk-payment-intent")
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @HttpCode(200)
  @ApiOperation({
    summary: "Create a payment intent for purchasing multiple books",
  })
  @ApiResponse({
    status: 200,
    description: "Bulk payment intent created successfully",
  })
  async createBulkPaymentIntent(
    @Body() createBulkPaymentIntentDto: CreateBulkPaymentIntentDto,
    @Req() request: RequestWithUser
  ) {
    const userId = request?.user?.user?.sub;
    return this.stripeService.createBulkPaymentIntent(
      userId,
      createBulkPaymentIntentDto
    );
  }

  @Post("stripe/bulk-course-payment-intent")
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @HttpCode(200)
  @ApiOperation({
    summary: "Create a payment intent for purchasing multiple courses",
  })
  @ApiResponse({
    status: 200,
    description: "Bulk course payment intent created successfully",
  })
  async createBulkCoursePaymentIntent(
    @Body() createBulkCoursePaymentIntentDto: CreateBulkCoursePaymentIntentDto,
    @Req() request: RequestWithUser
  ) {
    const userId = request?.user?.user?.sub;
    const bulkPaymentIntentDto: CreateBulkPaymentIntentDto = {
      bookIds: createBulkCoursePaymentIntentDto.courseIds,
      totalAmount: createBulkCoursePaymentIntentDto.totalAmount,
      currency: createBulkCoursePaymentIntentDto.currency,
      description:
        createBulkCoursePaymentIntentDto.description || "Bulk course purchase",
    };

    const result = await this.stripeService.createBulkPaymentIntent(
      userId,
      bulkPaymentIntentDto
    );

    return {
      ...result,
      courseIds: createBulkCoursePaymentIntentDto.courseIds,
      bookIds: undefined,
    };
  }

  @Post("stripe/process-payment")
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @HttpCode(200)
  @ApiOperation({
    summary: "Process a successful payment from the client (authenticated)",
  })
  @ApiResponse({ status: 200, description: "Payment processed successfully" })
  async processPayment(
    @Body() paymentData: any,
    @Req() request: RequestWithUser
  ) {
    try {
      const userId = request?.user?.user?.sub;

      const paymentIntent = await this.stripeService.retrievePaymentIntent(
        paymentData.paymentIntentId
      );

      if (paymentIntent.status === "succeeded") {
        await this.stripeService.handleSuccessfulPayment(paymentIntent);
        return { success: true, message: "Payment processed successfully" };
      } else {
        this.logger.warn(
          `Payment intent ${paymentData.paymentIntentId} has status ${paymentIntent.status}`
        );
        return {
          success: false,
          message: `Payment has status: ${paymentIntent.status}`,
        };
      }
    } catch (error) {
      this.logger.error(`Error processing payment: ${error.message}`);
      throw error;
    }
  }

  @Post("stripe/client-payment-confirmation")
  @HttpCode(200)
  @ApiOperation({
    summary: "Process a successful payment from the client (no auth required)",
  })
  @ApiResponse({ status: 200, description: "Payment processed successfully" })
  async processClientPayment(@Body() paymentData: any) {
    try {
      if (!paymentData.paymentIntentId) {
        return { success: false, message: "Payment intent ID is required" };
      }

      const paymentIntent = await this.stripeService.retrievePaymentIntent(
        paymentData.paymentIntentId
      );

      // If client sent additional metadata (like shipping info), merge it with the payment intent metadata
      if (paymentData.metadata) {
        this.logger.log(`Received metadata from client: ${JSON.stringify(paymentData.metadata)}`);

        // Create a new metadata object by merging the existing metadata with the client-provided metadata
        const updatedMetadata = {
          ...paymentIntent.metadata,
          ...paymentData.metadata
        };

        // Update the payment intent's metadata
        paymentIntent.metadata = updatedMetadata;

        this.logger.log(`Updated payment intent metadata: ${JSON.stringify(paymentIntent.metadata)}`);
      }

      if (paymentIntent.status === "succeeded") {
        await this.stripeService.handleSuccessfulPayment(paymentIntent);
        return { success: true, message: "Payment processed successfully" };
      } else {
        this.logger.warn(
          `Payment intent ${paymentData.paymentIntentId} has status ${paymentIntent.status}`
        );
        return {
          success: false,
          message: `Payment has status: ${paymentIntent.status}`,
        };
      }
    } catch (error) {
      this.logger.error(`Error processing client payment: ${error.message}`);
      return {
        success: false,
        message: `Error processing payment: ${error.message}`,
      };
    }
  }

  @Post("payments/webhook")
  @HttpCode(200)
  @ApiOperation({ summary: "Handle Stripe webhook events" })
  @ApiResponse({ status: 200, description: "Webhook processed successfully" })
  async handleWebhook(
    @Headers("stripe-signature") signature: string,
    @Req() request: RawBodyRequest<Request>,
    @Body() webhookData: any
  ) {
    return this.processWebhook(signature, request, webhookData);
  }

  @Post("stripe/webhook")
  @HttpCode(200)
  @ApiOperation({ summary: "Alternative endpoint for Stripe webhook events" })
  @ApiResponse({ status: 200, description: "Webhook processed successfully" })
  async handleStripeWebhook(
    @Headers("stripe-signature") signature: string,
    @Req() request: RawBodyRequest<Request>,
    @Body() webhookData: any
  ) {
    return this.processWebhook(signature, request, webhookData);
  }

  private async processWebhook(
    signature: string,
    request: RawBodyRequest<Request> & { originalUrl?: string },
    webhookData: any
  ) {
    try {
      if (webhookData && webhookData.id === "evt_test_webhook") {
        const paymentIntent = webhookData.data.object;
        if (webhookData.type === "payment_intent.succeeded") {
          await this.stripeService.handleSuccessfulPayment(paymentIntent);
          return { received: true, event: webhookData.type };
        } else if (webhookData.type === "payment_intent.payment_failed") {
          await this.stripeService.handleFailedPayment(paymentIntent);
          return { received: true, event: webhookData.type };
        }
        return { received: true, event: webhookData.type };
      }

      if (webhookData && webhookData.object === "payment_intent") {
        if (webhookData.status === "succeeded") {
          await this.stripeService.handleSuccessfulPayment(webhookData);
          return { received: true, event: "payment_intent.succeeded" };
        } else if (webhookData.status === "requires_payment_method") {
          await this.stripeService.handleFailedPayment(webhookData);
          return { received: true, event: "payment_intent.payment_failed" };
        }
        return {
          received: true,
          event: `payment_intent.${webhookData.status}`,
        };
      }

      if (!signature) {
        this.logger.error("Missing Stripe signature");
        return { error: "Missing Stripe signature" };
      }

      if (!request.rawBody) {
        this.logger.error("Missing request raw body");
        return { error: "Missing request raw body" };
      }

      const result = await this.stripeService.handleWebhookEvent(
        signature,
        request.rawBody
      );
      return result;
    } catch (error) {
      this.logger.error(`Error in webhook handler: ${error.message}`);
      throw error;
    }
  }


  @Post("stripe/mixed-payment-intent")
@UseGuards(AuthGuard)
@ApiBearerAuth()
@HttpCode(200)
@ApiOperation({
  summary: "Create a payment intent for purchasing both courses and books",
})
@ApiResponse({
  status: 200,
  description: "Mixed payment intent created successfully",
})
async createMixedPaymentIntent(
  @Body() createMixedPaymentIntentDto: any,
  @Req() request: RequestWithUser
) {
  const userId = request?.user?.user?.sub;
  return this.stripeService.createMixedPaymentIntent(
    userId,
    createMixedPaymentIntentDto
  );
}

@Get("stripe/saved-cards")
@UseGuards(AuthGuard)
@ApiBearerAuth()
@HttpCode(200)
@ApiOperation({
  summary: "Get saved payment methods for the authenticated user",
})
@ApiResponse({
  status: 200,
  description: "Saved payment methods retrieved successfully",
})
async getSavedPaymentMethods(@Req() request: RequestWithUser) {
  try {
    const userId = request?.user?.user?.sub;
    if (!userId) {
      return { data: [] };
    }
    return await this.stripeService.getSavedPaymentMethods(userId);
  } catch (error) {
    this.logger.error(`Error retrieving saved payment methods: ${error.message}`);
    return { data: [] };
  }
}

@Post("stripe/delete-card")
@UseGuards(AuthGuard)
@ApiBearerAuth()
@HttpCode(200)
@ApiOperation({
  summary: "Delete a saved payment method for the authenticated user",
})
@ApiResponse({
  status: 200,
  description: "Payment method deleted successfully",
})
async deletePaymentMethod(
  @Body() body: { paymentMethodId: string },
  @Req() request: RequestWithUser
) {
  try {
    const userId = request?.user?.user?.sub;
    if (!userId) {
      throw new NotFoundException("User not found");
    }
    return await this.stripeService.deletePaymentMethod(userId, body.paymentMethodId);
  } catch (error) {
    this.logger.error(`Error deleting payment method: ${error.message}`);
    throw error;
  }
}

@Post("stripe/setup-intent")
@UseGuards(AuthGuard)
@ApiBearerAuth()
@HttpCode(200)
@ApiOperation({
  summary: "Create a setup intent for saving a payment method without charging it",
})
@ApiResponse({
  status: 200,
  description: "Setup intent created successfully",
})
async createSetupIntent(@Req() request: RequestWithUser) {
  const userId = request?.user?.user?.sub;
  return this.stripeService.createSetupIntent(userId);
}
}
