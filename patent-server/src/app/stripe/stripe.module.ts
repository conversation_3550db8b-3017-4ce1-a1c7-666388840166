import { RepositoryModule } from '@app/repositories/repository.module';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';


import { StripeController } from './stripe.controller';
import { StripeService } from './stripe.service';

@Module({
  imports: [
    ConfigModule,
    RepositoryModule,
  ],
  controllers: [StripeController],
  providers: [StripeService],
  exports: [StripeService],
})
export class StripeModule {}