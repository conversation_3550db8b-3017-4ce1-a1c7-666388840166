  import {
    BadRequestException,
    Inject,
    Injectable,
    InternalServerErrorException,
    Logger,
    NotFoundException,
  } from "@nestjs/common";
  import { ConfigService } from "@nestjs/config";
  import Stripe from "stripe";

  import { CreatePaymentIntentDto } from "./dto";
  import { CreateBulkPaymentIntentDto } from "./dto/create-bulk-payment-intent.dto";

  @Injectable()
  export class StripeService {
    private readonly stripe: Stripe;
    private readonly logger = new Logger(StripeService.name);
    private readonly endpointSecret: string;

    constructor(
      private readonly configService: ConfigService,
      @Inject("CourseRepository") private readonly courseRepository,
      @Inject("BookRepository") private readonly bookRepository,
      @Inject("UserRepository") private readonly userRepository,
      @Inject("PurchaseRepository") private readonly purchaseRepository
    ) {
      this.stripe = new Stripe(
        this.configService.get<string>("STRIPE_SECRET_KEY"),
        {}
      );
      this.endpointSecret = this.configService.get<string>(
        "STRIPE_WEBHOOK_SECRET"
      );
    }

    async retrievePaymentIntent(paymentIntentId: string) {
      try {
        return await this.stripe.paymentIntents.retrieve(paymentIntentId);
      } catch (error) {
        this.logger.error(
          `Error retrieving payment intent ${paymentIntentId}: ${error.message}`
        );
        throw new InternalServerErrorException(
          `Failed to retrieve payment intent: ${error.message}`
        );
      }
    }

    async createSetupIntent(userId: string) {
      try {
        const customerId = await this.getOrCreateStripeCustomer(userId);

        const setupIntent = await this.stripe.setupIntents.create({
          customer: customerId,
          payment_method_types: ['card'],
          usage: 'off_session',
        });

        this.logger.log(`Created setup intent ${setupIntent.id} for user ${userId}`);

        return {
          clientSecret: setupIntent.client_secret,
          setupIntentId: setupIntent.id,
        };
      } catch (error) {
        this.logger.error(`Error creating setup intent: ${error.message}`);
        if (error instanceof NotFoundException) {
          throw error;
        }
        throw new InternalServerErrorException('Failed to create setup intent');
      }
    }

    private async getOrCreateStripeCustomer(userId: string): Promise<string> {
      try {
        const user = await this.userRepository.findOne({ _id: userId });
        if (!user) {
          throw new NotFoundException("User not found");
        }

        if (user.stripeCustomerId) {
          this.logger.log(
            `Using existing Stripe customer ID: ${user.stripeCustomerId} for user ${userId}`
          );
          return user.stripeCustomerId;
        }

        const customer = await this.stripe.customers.create({
          email: user.email,
          name: `${user.firstName} ${user.lastName}`,
          metadata: {
            userId: userId,
          },
        });

        await this.userRepository.findOneAndUpdate(
          { _id: userId },
          { stripeCustomerId: customer.id },
          { new: true }
        );

        this.logger.log(
          `Created new Stripe customer ID: ${customer.id} for user ${userId}`
        );
        return customer.id;
      } catch (error) {
        this.logger.error(
          `Error creating/retrieving Stripe customer: ${error.message}`
        );
        if (error instanceof NotFoundException) {
          throw error;
        }
        throw new InternalServerErrorException(
          "Failed to create/retrieve Stripe customer"
        );
      }
    }


    async getSavedPaymentMethods(userId: string) {
      try {
        const user = await this.userRepository.findOne({ _id: userId });
        if (!user) {
          this.logger.error(`User not found with ID: ${userId}`);
          throw new NotFoundException("User not found");
        }

        this.logger.log(`Found user: ${user._id}, stripeCustomerId: ${user.stripeCustomerId}`);

        if (!user.stripeCustomerId) {
          this.logger.log(`User ${userId} has no Stripe customer ID`);
          return { data: [] };
        }

        const paymentMethods = await this.stripe.paymentMethods.list({
          customer: user.stripeCustomerId,
          type: 'card',
        });

        this.logger.log(`Retrieved ${paymentMethods.data.length} payment methods for user ${userId}`);

        if (paymentMethods.data.length > 0) {
          this.logger.log(`First payment method: ${JSON.stringify({
            id: paymentMethods.data[0].id,
            type: paymentMethods.data[0].type,
            card: {
              brand: paymentMethods.data[0].card.brand,
              last4: paymentMethods.data[0].card.last4,
              exp_month: paymentMethods.data[0].card.exp_month,
              exp_year: paymentMethods.data[0].card.exp_year,
            }
          })}`);
        }

        return paymentMethods;
      } catch (error) {
        this.logger.error(`Error retrieving payment methods: ${error.message}`);
        if (error instanceof NotFoundException) {
          throw error;
        }
        throw new InternalServerErrorException("Failed to retrieve payment methods");
      }
    }

    async createBulkPaymentIntent(
      userId: string,
      createBulkPaymentIntentDto: CreateBulkPaymentIntentDto
    ) {
      try {
        const {
          bookIds,
          totalAmount,
          currency = "usd",
          description,
        } = createBulkPaymentIntentDto;

        if (!bookIds || bookIds.length === 0) {
          throw new BadRequestException("Please provide at least one book ID");
        }
        const user = await this.userRepository.findOne({ _id: userId });
        if (!user) {
          throw new NotFoundException("User not found");
        }
        const isCourseIds = description && description.includes("course");
        if (isCourseIds) {
          const courses = [];
          for (const courseId of bookIds) {
            const course = await this.courseRepository.findOne({ _id: courseId });
            if (!course) {
              throw new NotFoundException(`Course with ID ${courseId} not found`);
            }
            courses.push(course);
          }

          const customerId = await this.getOrCreateStripeCustomer(userId);

          const paymentIntent = await this.stripe.paymentIntents.create({
            amount: Math.round(totalAmount * 100),
            currency: currency,
            description:
              description || `Bulk purchase of ${courses.length} courses`,
            payment_method_types: ["card"],
            customer: customerId,
            setup_future_usage: 'off_session',
            metadata: {
              courseIds: JSON.stringify(bookIds),
              userId: userId,
              isBulkPurchase: "true",
              itemType: "course",
              stripeCustomerId: customerId,
            },
          });

          return {
            clientSecret: paymentIntent.client_secret,
            paymentIntentId: paymentIntent.id,
            amount: totalAmount,
            courseIds: bookIds,
          };
        } else {
          const books = [];
          for (const bookId of bookIds) {
            const book = await this.bookRepository.findOne({ _id: bookId });
            if (!book) {
              throw new NotFoundException(`Book with ID ${bookId} not found`);
            }
            books.push(book);
          }

          const customerId = await this.getOrCreateStripeCustomer(userId);

          // Check if any of the books are hardcopy and require shipping
          let hasHardcopyBooks = false;
          for (const book of books) {
            if (book.type === 'hardcopy') {
              hasHardcopyBooks = true;
              break;
            }
          }

          const paymentIntent = await this.stripe.paymentIntents.create({
            amount: Math.round(totalAmount * 100),
            currency: currency,
            description: description || `Bulk purchase of ${books.length} books`,
            payment_method_types: ["card"],
            customer: customerId,
            setup_future_usage: 'off_session',
            metadata: {
              bookIds: JSON.stringify(bookIds),
              userId: userId,
              isBulkPurchase: "true",
              itemType: "book",
              stripeCustomerId: customerId,
              requiresShipping: hasHardcopyBooks ? "true" : "false",
            },
          });

          return {
            clientSecret: paymentIntent.client_secret,
            paymentIntentId: paymentIntent.id,
            amount: totalAmount,
            bookIds: bookIds,
          };
        }
      } catch (error) {
        this.logger.error(`Error creating bulk payment intent: ${error.message}`);
        if (
          error instanceof NotFoundException ||
          error instanceof BadRequestException
        ) {
          throw error;
        }
        throw new InternalServerErrorException(
          "Failed to create bulk payment intent"
        );
      }
    }

    async createPaymentIntent(
      userId: string,
      createPaymentIntentDto: CreatePaymentIntentDto
    ) {
      try {
        const {
          courseId,
          bookId,
          currency = "usd",
          description,
        } = createPaymentIntentDto;

        if ((courseId && bookId) || (!courseId && !bookId)) {
          throw new BadRequestException(
            "Please provide either courseId or bookId, but not both"
          );
        }

        let item: any;
        let itemType: string;
        let price: number;
        let title: string;

        if (courseId) {
          item = await this.courseRepository.findOne({ _id: courseId });
          if (!item) {
            throw new NotFoundException("Course not found");
          }
          itemType = "course";
          price = item.price;
          title = item.title;
        } else {
          item = await this.bookRepository.findOne({ _id: bookId });
          if (!item) {
            throw new NotFoundException("Book not found");
          }
          itemType = "book";
          price = item.price;
          title = item.title;
        }

        const user = await this.userRepository.findOne({ _id: userId });
        if (!user) {
          throw new NotFoundException("User not found");
        }

        const customerId = await this.getOrCreateStripeCustomer(userId);

        // Check if this is a hardcopy book that requires shipping
        let requiresShipping = false;
        if (itemType === 'book') {
          try {
            const book = await this.bookRepository.findOne({ _id: bookId });
            requiresShipping = book && book.type === 'hardcopy';
          } catch (error) {
            this.logger.error(`Error checking book type: ${error.message}`);
          }
        }

        const paymentIntent = await this.stripe.paymentIntents.create({
          amount: Math.round(price * 100),
          currency: currency,
          description: description || `Payment for ${itemType}: ${title}`,
          payment_method_types: ["card"],
          customer: customerId,
          setup_future_usage: 'off_session',
          metadata: {
            itemId: courseId || bookId,
            itemType: itemType,
            userId: userId,
            title: title,
            stripeCustomerId: customerId,
            requiresShipping: requiresShipping ? "true" : "false",
          },
        });

        return {
          clientSecret: paymentIntent.client_secret,
          paymentIntentId: paymentIntent.id,
          amount: price,
          itemId: courseId || bookId,
          itemType: itemType,
          title: title,
        };
      } catch (error) {
        this.logger.error(`Error creating payment intent: ${error.message}`);
        if (
          error instanceof NotFoundException ||
          error instanceof BadRequestException
        ) {
          throw error;
        }
        throw new InternalServerErrorException("Failed to create payment intent");
      }
    }

    async handleWebhookEvent(signature: string, rawBody: Buffer) {
      try {
        const event = this.stripe.webhooks.constructEvent(
          rawBody,
          signature,
          this.endpointSecret
        );

        switch (event.type) {
          case "payment_intent.succeeded":
            await this.handleSuccessfulPayment(event.data.object);
            break;
          case "payment_intent.payment_failed":
            await this.handleFailedPayment(event.data.object);
            break;
          default:
        }

        return { received: true, event: event.type };
      } catch (error) {
        this.logger.error(`Webhook error: ${error.message}`);
        throw new BadRequestException(`Webhook error: ${error.message}`);
      }
    }

    public async handleSuccessfulPayment(paymentIntent: Stripe.PaymentIntent) {
      try {
        this.logger.log(`Processing successful payment: ${paymentIntent.id}`);
        this.logger.log(
          `Payment metadata: ${JSON.stringify(paymentIntent.metadata)}`
        );

        const {
          userId,
          courseIds,
          bookIds,
          itemType,
          itemId,
          isMixedPurchase,
          stripeCustomerId,
          requiresShipping,
          shippingAddress
        } = paymentIntent.metadata;

        // Parse shipping information if present
        let parsedShippingAddress = null;
        if (requiresShipping === "true" && shippingAddress) {
          try {
            parsedShippingAddress = JSON.parse(shippingAddress);
            this.logger.log(`Parsed shipping address: ${JSON.stringify(parsedShippingAddress)}`);
          } catch (error) {
            this.logger.error(`Error parsing shipping address: ${error.message}`);
          }
        }

        let customerId = stripeCustomerId;
        if (!customerId) {
          customerId = await this.getOrCreateStripeCustomer(userId);
        } else {
          const user = await this.userRepository.findOne({ _id: userId });
          if (user && !user.stripeCustomerId) {
            await this.userRepository.findOneAndUpdate(
              { _id: userId },
              { stripeCustomerId: customerId },
              { new: true }
            );
            this.logger.log(
              `Updated user ${userId} with Stripe customer ID: ${customerId}`
            );
          }
        }

        // If the payment has a payment method, attach it to the customer for future use
        if (paymentIntent.payment_method) {
          try {
            this.logger.log(`Payment has payment method: ${paymentIntent.payment_method}`);

            // Check if the payment method is already attached to the customer
            const paymentMethod = await this.stripe.paymentMethods.retrieve(
              paymentIntent.payment_method as string
            );

            if (!paymentMethod.customer) {
              // Attach the payment method to the customer
              await this.stripe.paymentMethods.attach(
                paymentIntent.payment_method as string,
                { customer: customerId }
              );

              this.logger.log(
                `Attached payment method ${paymentIntent.payment_method} to customer ${customerId}`
              );
            } else if (paymentMethod.customer !== customerId) {
              this.logger.log(
                `Payment method ${paymentIntent.payment_method} already attached to a different customer: ${paymentMethod.customer}`
              );
            } else {
              this.logger.log(
                `Payment method ${paymentIntent.payment_method} already attached to customer ${customerId}`
              );
            }
          } catch (error) {
            this.logger.error(
              `Error attaching payment method to customer: ${error.message}`
            );
          }
        } else {
          this.logger.log(`Payment does not have a payment method attached`);
        }

        let courseIdsArray = [];
        let bookIdsArray = [];

        try {
          if (courseIds) {
            courseIdsArray = JSON.parse(courseIds);
            this.logger.log(
              `Parsed courseIds: ${JSON.stringify(courseIdsArray)}`
            );
          }
        } catch (e) {
          this.logger.error(`Error parsing courseIds: ${e.message}`);
        }

        try {
          if (bookIds) {
            bookIdsArray = JSON.parse(bookIds);
            this.logger.log(`Parsed bookIds: ${JSON.stringify(bookIdsArray)}`);
          }
        } catch (e) {
          this.logger.error(`Error parsing bookIds: ${e.message}`);
        }

        if (
          isMixedPurchase === "true" &&
          (courseIdsArray.length > 0 || bookIdsArray.length > 0)
        ) {
          const user = await this.userRepository.findOne({ _id: userId });
          if (!user) {
            this.logger.error(`User ${userId} not found while updating arrays`);
            return;
          }

          const userObject = user.toObject ? user.toObject() : user;
          const totalItems = courseIdsArray.length + bookIdsArray.length;
          const amountPerItem = paymentIntent.amount / 100 / totalItems;

          if (courseIdsArray.length > 0) {
            const courses = Array.isArray(userObject.courses)
              ? userObject.courses
              : [];
            const newCourses = [...new Set([...courses, ...courseIdsArray])];
            await this.userRepository.findOneAndUpdate(
              { _id: userId },
              { $set: { courses: newCourses, updatedAt: new Date() } },
              { new: true }
            );

            for (const courseId of courseIdsArray) {
              const purchaseData = {
                userId,
                courseId,
                amount: amountPerItem,
                status: "Completed",
                paymentMethod: "CreditCard",
                transactionId: paymentIntent.id,
                date: new Date(),
              };
              try {
                await this.purchaseRepository.create(purchaseData, true);
              } catch (createError) {
                this.logger.error(
                  `Database error creating course purchase record: ${createError.message}`
                );
                try {
                  await this.purchaseRepository.save(purchaseData, true);
                } catch (saveError) {
                  this.logger.error(
                    `Save method also failed for course purchase: ${saveError.message}`
                  );
                }
              }
            }
          }

          if (bookIdsArray.length > 0) {
            const books = Array.isArray(userObject.books) ? userObject.books : [];
            const newBooks = [...new Set([...books, ...bookIdsArray])];
            await this.userRepository.findOneAndUpdate(
              { _id: userId },
              { $set: { books: newBooks, updatedAt: new Date() } },
              { new: true }
            );

            for (const bookId of bookIdsArray) {
              const purchaseData = {
                userId,
                bookId,
                amount: amountPerItem,
                status: "Completed",
                paymentMethod: "CreditCard",
                transactionId: paymentIntent.id,
                date: new Date(),
              };
              try {
                await this.purchaseRepository.create(purchaseData, true);
              } catch (createError) {
                this.logger.error(
                  `Database error creating book purchase record: ${createError.message}`
                );
                try {
                  await this.purchaseRepository.save(purchaseData, true);
                } catch (saveError) {
                  this.logger.error(
                    `Save method also failed for book purchase: ${saveError.message}`
                  );
                }
              }
            }
          }

          return;
        }

        if (courseIdsArray.length > 0) {
          try {
            this.logger.log(
              `Processing bulk course purchase for user ${userId}, courses: ${JSON.stringify(courseIdsArray)}`
            );
            const user = await this.userRepository.findOne({ _id: userId });
            if (!user) {
              this.logger.error(
                `User ${userId} not found while updating courses array`
              );
              return;
            }

            const userObject = user.toObject ? user.toObject() : user;
            const currentCourses = Array.isArray(userObject.courses)
              ? userObject.courses
              : [];

            const newCourses = [
              ...new Set([...currentCourses, ...courseIdsArray]),
            ];

            await this.userRepository.findOneAndUpdate(
              { _id: userId },
              { $set: { courses: newCourses, updatedAt: new Date() } },
              { new: true }
            );

            this.logger.log(
              `Updated user ${userId} courses array: ${JSON.stringify(newCourses)}`
            );

            for (const courseId of courseIdsArray) {
              try {
                const purchaseData = {
                  userId,
                  courseId,
                  amount: paymentIntent.amount / 100 / courseIdsArray.length,
                  status: "Completed",
                  paymentMethod: "CreditCard",
                  transactionId: paymentIntent.id,
                  date: new Date(),
                };

                Object.defineProperty(purchaseData, "bookId", {
                  enumerable: false,
                  value: undefined,
                });

                const purchase = await this.purchaseRepository.create(
                  purchaseData,
                  true
                );
                this.logger.log(
                  `Created purchase record for course ${courseId}: ${purchase?._id || "unknown ID"}`
                );
              } catch (createError) {
                this.logger.error(
                  `Database error creating course purchase record: ${createError.message}`
                );
                try {
                  const purchaseModel = this.purchaseRepository.getModel;
                  const newPurchase = new purchaseModel({
                    userId,
                    courseId,
                    amount: paymentIntent.amount / 100 / courseIdsArray.length,
                    status: "Completed",
                    paymentMethod: "CreditCard",
                    transactionId: paymentIntent.id,
                    date: new Date(),
                  });

                  const savedPurchase = await newPurchase.save();
                  this.logger.log(
                    `Saved purchase record for course ${courseId} using alternative method: ${savedPurchase?._id || "unknown ID"}`
                  );
                } catch (saveError) {
                  this.logger.error(
                    `Alternative save method also failed for course purchase: ${saveError.message}`
                  );
                }
              }
            }

            if (isMixedPurchase !== "true" || bookIdsArray.length === 0) {
              return;
            }
          } catch (error) {
            this.logger.error(
              `Error processing bulk course payment: ${error.message}`
            );
            this.logger.error(error.stack);
          }
        }

        if (bookIdsArray.length > 0) {
          try {
            this.logger.log(
              `Processing bulk book purchase for user ${userId}, books: ${JSON.stringify(bookIdsArray)}`
            );

            const user = await this.userRepository.findOne({ _id: userId });
            if (!user) {
              this.logger.error(
                `User ${userId} not found while updating books array`
              );
              return;
            }

            const userObject = user.toObject ? user.toObject() : user;
            const currentBooks = Array.isArray(userObject.books)
              ? userObject.books
              : [];

            const newBooks = [...new Set([...currentBooks, ...bookIdsArray])];

            await this.userRepository.findOneAndUpdate(
              { _id: userId },
              { $set: { books: newBooks, updatedAt: new Date() } },
              { new: true }
            );

            this.logger.log(
              `Updated user ${userId} books array: ${JSON.stringify(newBooks)}`
            );

            for (const bookId of bookIdsArray) {
              // Check if this book is a hardcopy and requires shipping
              let isHardcopy = false;
              try {
                const book = await this.bookRepository.findOne({ _id: bookId });
                isHardcopy = book && book.type === 'hardcopy';
              } catch (error) {
                this.logger.error(`Error checking book type: ${error.message}`);
              }

              const purchaseData = {
                userId,
                bookId,
                amount: paymentIntent.amount / 100 / bookIdsArray.length,
                status: "Completed",
                paymentMethod: "CreditCard",
                transactionId: paymentIntent.id,
                date: new Date(),
                requiresShipping: isHardcopy && requiresShipping === "true",
                ...(isHardcopy && requiresShipping === "true" && parsedShippingAddress ? { shippingAddress: parsedShippingAddress } : {})
              };

              try {
                const purchase = await this.purchaseRepository.create(
                  purchaseData,
                  true
                );
                this.logger.log(
                  `Created purchase record for book ${bookId}: ${purchase?._id || "unknown ID"}`
                );
              } catch (createError) {
                this.logger.error(
                  `Database error creating book purchase record: ${createError.message}`
                );
                try {
                  const savedPurchase = await this.purchaseRepository.save(
                    purchaseData,
                    true
                  );
                  this.logger.log(
                    `Saved purchase record for book ${bookId}: ${savedPurchase?._id || "unknown ID"}`
                  );
                } catch (saveError) {
                  this.logger.error(
                    `Save method also failed for book purchase: ${saveError.message}`
                  );
                }
              }
            }

            if (isMixedPurchase !== "true" || courseIdsArray.length === 0) {
              return; // Exit if this is only a book purchase
            }
          } catch (error) {
            this.logger.error(
              `Error processing bulk book payment: ${error.message}`
            );
            this.logger.error(error.stack);
          }
        }

        if (!isMixedPurchase && itemType === "course") {
          try {
            this.logger.log(
              `Processing individual course purchase for user ${userId}, course: ${itemId}`
            );

            const user = await this.userRepository.findOne({ _id: userId });
            if (!user) {
              this.logger.error(
                `User ${userId} not found while updating courses array`
              );
              return;
            }

            const userObject = user.toObject ? user.toObject() : user;
            const courses = Array.isArray(userObject.courses)
              ? userObject.courses
              : [];

            if (!courses.includes(itemId)) {
              courses.push(itemId);

              await this.userRepository.findOneAndUpdate(
                { _id: userId },
                { $set: { courses, updatedAt: new Date() } },
                { new: true }
              );

              this.logger.log(
                `Updated user ${userId} courses array with course ${itemId}`
              );
            }

            const purchaseData = {
              userId,
              courseId: itemId,
              amount: paymentIntent.amount / 100,
              status: "Completed",
              paymentMethod: "CreditCard",
              transactionId: paymentIntent.id,
              date: new Date(),
            };

            Object.defineProperty(purchaseData, "bookId", {
              enumerable: false,
              value: undefined,
            });

            try {
              const purchase = await this.purchaseRepository.create(
                purchaseData,
                true
              );
              this.logger.log(
                `Created purchase record for course ${itemId}: ${purchase?._id || "unknown ID"}`
              );
            } catch (createError) {
              this.logger.error(
                `Database error creating purchase record: ${createError.message}`
              );
              try {
                const purchaseModel = this.purchaseRepository.getModel;
                const newPurchase = new purchaseModel({
                  userId,
                  courseId: itemId,
                  amount: paymentIntent.amount / 100,
                  status: "Completed",
                  paymentMethod: "CreditCard",
                  transactionId: paymentIntent.id,
                  date: new Date(),
                });

                const savedPurchase = await newPurchase.save();
                this.logger.log(
                  `Saved purchase record for course ${itemId} using alternative method: ${savedPurchase?._id || "unknown ID"}`
                );
              } catch (saveError) {
                this.logger.error(
                  `Save method also failed: ${saveError.message}`
                );
              }
            }
          } catch (courseError) {
            this.logger.error(
              `Error processing course purchase: ${courseError.message}`
            );
            this.logger.error(courseError.stack);
          }
        } else if (!isMixedPurchase && itemType === "book") {
          try {
            this.logger.log(
              `Processing individual book purchase for user ${userId}, book: ${itemId}`
            );

            const user = await this.userRepository.findOne({ _id: userId });
            if (!user) {
              this.logger.error(
                `User ${userId} not found while updating books array`
              );
              return;
            }

            const userObject = user.toObject ? user.toObject() : user;
            const books = Array.isArray(userObject.books) ? userObject.books : [];

            if (!books.includes(itemId)) {
              books.push(itemId);

              await this.userRepository.findOneAndUpdate(
                { _id: userId },
                { $set: { books, updatedAt: new Date() } },
                { new: true }
              );

              this.logger.log(
                `Updated user ${userId} books array with book ${itemId}`
              );
            }

            // Check if this book is a hardcopy and requires shipping
            let isHardcopy = false;
            try {
              const book = await this.bookRepository.findOne({ _id: itemId });
              isHardcopy = book && book.type === 'hardcopy';
            } catch (error) {
              this.logger.error(`Error checking book type: ${error.message}`);
            }

            const purchaseData = {
              userId,
              bookId: itemId,
              amount: paymentIntent.amount / 100,
              status: "Completed",
              paymentMethod: "CreditCard",
              transactionId: paymentIntent.id,
              date: new Date(),
              requiresShipping: isHardcopy && requiresShipping === "true",
              ...(isHardcopy && requiresShipping === "true" && parsedShippingAddress ? { shippingAddress: parsedShippingAddress } : {})
            };

            Object.defineProperty(purchaseData, "courseId", {
              enumerable: false,
              value: undefined,
            });

            try {
              const purchase = await this.purchaseRepository.create(
                purchaseData,
                true
              );
              this.logger.log(
                `Created purchase record for book ${itemId}: ${purchase?._id || "unknown ID"}`
              );
            } catch (createError) {
              this.logger.error(
                `Database error creating purchase record: ${createError.message}`
              );
              try {
                const purchaseModel = this.purchaseRepository.getModel;
                const newPurchase = new purchaseModel({
                  userId,
                  bookId: itemId,
                  amount: paymentIntent.amount / 100,
                  status: "Completed",
                  paymentMethod: "CreditCard",
                  transactionId: paymentIntent.id,
                  date: new Date(),
                });

                const savedPurchase = await newPurchase.save();
                this.logger.log(
                  `Saved purchase record for book ${itemId} using alternative method: ${savedPurchase?._id || "unknown ID"}`
                );
              } catch (saveError) {
                this.logger.error(
                  `Save method also failed: ${saveError.message}`
                );
              }
            }
          } catch (bookError) {
            this.logger.error(
              `Error processing book purchase: ${bookError.message}`
            );
            this.logger.error(bookError.stack);
          }
        }
      } catch (error) {
        this.logger.error(`Error processing payment: ${error.message}`);
        this.logger.error(error.stack);
      }
    }

    async createMixedPaymentIntent(
      userId: string,
      createMixedPaymentIntentDto: any
    ) {
      try {
        const {
          courseIds,
          bookIds,
          totalAmount,
          currency = "usd",
          description,
        } = createMixedPaymentIntentDto;
        this.logger.log(
          `Creating mixed payment intent: ${JSON.stringify(createMixedPaymentIntentDto)}`
        );

        if (
          (!courseIds || courseIds.length === 0) &&
          (!bookIds || bookIds.length === 0)
        ) {
          throw new BadRequestException(
            "Please provide at least one course ID or book ID"
          );
        }

        const user = await this.userRepository.findOne({ _id: userId });
        if (!user) {
          throw new NotFoundException("User not found");
        }

        const items = [];
        if (courseIds && courseIds.length > 0) {
          for (const courseId of courseIds) {
            const course = await this.courseRepository.findOne({ _id: courseId });
            if (!course) {
              throw new NotFoundException(`Course with ID ${courseId} not found`);
            }
            items.push({ type: "course", id: courseId });
          }
        }

        if (bookIds && bookIds.length > 0) {
          for (const bookId of bookIds) {
            const book = await this.bookRepository.findOne({ _id: bookId });
            if (!book) {
              throw new NotFoundException(`Book with ID ${bookId} not found`);
            }
            items.push({ type: "book", id: bookId });
          }
        }

        const customerId = await this.getOrCreateStripeCustomer(userId);

        // Check if any of the books are hardcopy and require shipping
        let hasHardcopyBooks = false;
        if (bookIds && bookIds.length > 0) {
          for (const bookId of bookIds) {
            try {
              const book = await this.bookRepository.findOne({ _id: bookId });
              if (book && book.type === 'hardcopy') {
                hasHardcopyBooks = true;
                break;
              }
            } catch (error) {
              this.logger.error(`Error checking book type: ${error.message}`);
            }
          }
        }

        const paymentIntent = await this.stripe.paymentIntents.create({
          amount: Math.round(totalAmount * 100),
          currency: currency,
          description: description || `Mixed purchase of ${items.length} items`,
          payment_method_types: ["card"],
          customer: customerId,
          setup_future_usage: 'off_session',
          metadata: {
            courseIds: JSON.stringify(courseIds),
            bookIds: JSON.stringify(bookIds),
            userId: userId,
            isMixedPurchase: "true",
            stripeCustomerId: customerId,
            requiresShipping: hasHardcopyBooks ? "true" : "false",
          },
        });

        return {
          clientSecret: paymentIntent.client_secret,
          paymentIntentId: paymentIntent.id,
          amount: totalAmount,
          courseIds: courseIds,
          bookIds: bookIds,
        };
      } catch (error) {
        this.logger.error(
          `Error creating mixed payment intent: ${error.message}`
        );
        if (
          error instanceof NotFoundException ||
          error instanceof BadRequestException
        ) {
          throw error;
        }
        throw new InternalServerErrorException(
          "Failed to create mixed payment intent"
        );
      }
    }

    public async handleFailedPayment(paymentIntent: Stripe.PaymentIntent) {
      try {
        const { userId, itemId, itemType } = paymentIntent.metadata;

        const purchaseData = {
          userId,
          ...(itemType === "course" ? { courseId: itemId } : { bookId: itemId }),
          amount: paymentIntent.amount / 100,
          status: "Failed",
          paymentMethod: "CreditCard",
          transactionId: paymentIntent.id,
          date: new Date(),
        };

        try {
          await this.purchaseRepository.create(purchaseData, true);
        } catch (createError) {
          this.logger.error(
            `Database error creating failed payment record: ${createError.message}`
          );

          try {
            await this.purchaseRepository.save(purchaseData, true);
          } catch (saveError) {
            this.logger.error(
              `Save method also failed for failed payment: ${saveError.message}`
            );
            throw saveError;
          }
        }
      } catch (error) {
        this.logger.error(`Error processing failed payment: ${error.message}`);
        this.logger.error(error.stack);
      }
    }

    async deletePaymentMethod(userId: string, paymentMethodId: string) {
      try {
        const user = await this.userRepository.findOne({ _id: userId });
        if (!user) {
          throw new NotFoundException("User not found");
        }

        if (!user.stripeCustomerId) {
          throw new NotFoundException("User has no payment methods");
        }

        const paymentMethod = await this.stripe.paymentMethods.retrieve(paymentMethodId);

        if (!paymentMethod) {
          throw new NotFoundException("Payment method not found");
        }

        if (paymentMethod.customer !== user.stripeCustomerId) {
          throw new BadRequestException("Payment method does not belong to this user");
        }

        const detachedPaymentMethod = await this.stripe.paymentMethods.detach(paymentMethodId);

        this.logger.log(`Detached payment method ${paymentMethodId} from customer ${user.stripeCustomerId}`);

        return {
          success: true,
          message: "Payment method deleted successfully",
          paymentMethod: {
            id: detachedPaymentMethod.id,
            last4: detachedPaymentMethod.card.last4,
            brand: detachedPaymentMethod.card.brand
          }
        };
      } catch (error) {
        this.logger.error(`Error deleting payment method: ${error.message}`);
        if (error instanceof NotFoundException || error instanceof BadRequestException) {
          throw error;
        }
        throw new InternalServerErrorException(`Failed to delete payment method: ${error.message}`);
      }
    }
  }

