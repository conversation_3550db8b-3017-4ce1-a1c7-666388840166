import { UserStatuses } from '@app/utils';
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsEnum, IsNotEmpty, IsOptional, IsString, MinLength } from 'class-validator';

export class CreateUserByAdminDto {
  @ApiProperty({ example: 'John', description: 'First name of the user' })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({ example: 'Doe', description: 'Last name of the user' })
  @IsString()
  @IsNotEmpty()
  lastName: string;



  @ApiProperty({ example: '<EMAIL>', description: 'Email address of the user' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ example: 'johndoe', description: 'Username for the user' })
  @IsString()
  @IsOptional()
  username?: string;

  @ApiProperty({ example: 'password123', description: 'Password (at least 6 characters)' })
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({ example: 'Student', description: 'User role (Admin, Instructor, Student)', enum: ['Admin', 'Instructor', 'Student'] })
  @IsEnum(['Admin', 'Instructor', 'Student'])
  @IsNotEmpty()
  role: string;

  @ApiProperty({ example: false, description: 'Whether the user is an admin' })
  @IsOptional()
  isAdmin?: boolean;

  @ApiProperty({
    example: UserStatuses.pending,
    description: 'User status',
    enum: Object.values(UserStatuses),
    default: UserStatuses.pending
  })
  @IsEnum(UserStatuses)
  @IsOptional()
  status?: string;
}