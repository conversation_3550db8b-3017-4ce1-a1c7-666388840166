import { UserStatuses } from '@app/utils';
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEmail, IsNotEmpty, IsEnum } from 'class-validator';

class UpdateUserDto {
  @ApiProperty({ example: '<EMAIL>', description: 'Email address of the user', required: false })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({ example: 'John', description: 'First name of the user', required: false })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiProperty({ example: 'Doe', description: 'Last name of the user', required: false })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiProperty({ example: 'johndoe', description: 'Username', required: false })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiProperty({
    example: UserStatuses.active,
    description: 'User status',
    enum: Object.values(UserStatuses),
    required: false
  })
  @IsOptional()
  @IsEnum(UserStatuses)
  status?: string;
}

class UpdateAvatarDto {
  @ApiProperty({ example: 'https://example.com/avatar.jpg', description: 'Updated avatar URL' })
  avatar: string;
}

class UpdateUsernameDto {
  @ApiProperty({ example: 'johndoe', description: 'Username (optional)', required: false })
  @IsString()
  @IsNotEmpty()
  username: string;
}

class UpdatePasswordDto {
  @ApiProperty({ example: 'password123', description: 'New password (at least 6 characters)' })
  @IsString()
  @IsNotEmpty()
  password: string;

  @ApiProperty({ example: 'password123', description: 'Current password (at least 6 characters)' })
  @IsString()
  @IsNotEmpty()
  currentPassword: string;
}

export { UpdateUserDto, UpdateAvatarDto, UpdatePasswordDto, UpdateUsernameDto };
