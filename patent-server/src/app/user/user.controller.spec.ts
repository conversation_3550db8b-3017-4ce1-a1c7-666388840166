import { AuthGuard } from "@app/guards/auth/auth.guard";
import { RequestWithUser } from "@app/interfaces";
import { NotFoundException, BadRequestException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { JwtService } from "@nestjs/jwt";
import { Test, TestingModule } from "@nestjs/testing";

import { JwtTokenService } from "../auth/jwt/jwt.token.service";

import { UpdatePasswordDto, UpdateUserDto, UpdateUsernameDto } from "./dto";
import { UserController } from "./user.controller";
import { UserService } from "./user.service";

describe("UserController", () => {
  let userController: UserController;
  let userService: UserService;

  const mockUserService = {
    getUserById: jest.fn(),
    updateUser: jest.fn(),
    updateUsername: jest.fn(),
    updatePassword: jest.fn(),
  };

  const mockRequestWithUser = {
    user: { user: { userId: "test-user-id" } },
  };

  const mockJwtService = {
    verify: jest.fn().mockReturnValue({ userId: "test-user-id" }),
    sign: jest.fn(),
  };

  const mockJwtTokenService = {
    createAccessToken: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn().mockImplementation((key: string) => {
      if (key === "SENDGRID_API_KEY") {
        return "SG.mock-sendgrid-api-key";
      }
      return null;
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        { provide: JwtTokenService, useValue: mockJwtTokenService },
        { provide: ConfigService, useValue: mockConfigService }, 
        { provide: UserService, useValue: mockUserService },
        { provide: JwtService, useValue: mockJwtService }, 
        { provide: AuthGuard, useValue: {} }, 
      ],
    }).compile();

    userController = module.get<UserController>(UserController);
    userService = module.get<UserService>(UserService);
  });

  describe("getUser", () => {
    it("should return user details when user is found", async () => {
      const mockUser = { userId: "test-user-id", username: "testuser" };
      mockUserService.getUserById.mockResolvedValue(mockUser);

      const result = await userController.getUser(
        mockRequestWithUser as RequestWithUser
      );

      expect(result).toEqual(mockUser);
      expect(userService.getUserById).toHaveBeenCalledWith("test-user-id");
    });

    it("should throw NotFoundException when user is not found", async () => {
      mockUserService.getUserById.mockRejectedValue(
        new NotFoundException("User not found")
      );

      await expect(
        userController.getUser(mockRequestWithUser as RequestWithUser)
      ).rejects.toThrowError(NotFoundException);
    });
  });

  describe("updateUser", () => {
    it("should update user details successfully", async () => {
      const updateUserDto: UpdateUserDto = {
        firstName: "John",
        lastName: "Doe",
      };
      const mockUpdatedUser = {
        userId: "test-user-id",
        username: "newUsername",
      };
      mockUserService.updateUser.mockResolvedValue(mockUpdatedUser);

      const result = await userController.updateUser(
        mockRequestWithUser as RequestWithUser,
        updateUserDto
      );

      expect(result).toEqual(mockUpdatedUser);
      expect(userService.updateUser).toHaveBeenCalledWith(
        "test-user-id",
        updateUserDto
      );
    });

    it("should throw NotFoundException when user is not found", async () => {
      const updateUserDto: UpdateUserDto = {
        firstName: "John",
        lastName: "Doe",
      };
      mockUserService.updateUser.mockRejectedValue(
        new NotFoundException("User not found")
      );

      await expect(
        userController.updateUser(
          mockRequestWithUser as RequestWithUser,
          updateUserDto
        )
      ).rejects.toThrowError(NotFoundException);
    });
  });

  describe("updateUsername", () => {
    it("should update username successfully", async () => {
      const updateUsernameDto: UpdateUsernameDto = { username: "newUsername" };
      const mockUpdatedUser = {
        userId: "test-user-id",
        username: "newUsername",
      };
      mockUserService.updateUsername.mockResolvedValue(mockUpdatedUser);

      const result = await userController.updateUsername(
        mockRequestWithUser as RequestWithUser,
        updateUsernameDto
      );

      expect(result).toEqual(mockUpdatedUser);
      expect(userService.updateUsername).toHaveBeenCalledWith(
        "test-user-id",
        updateUsernameDto
      );
    });

    it("should throw NotFoundException when user is not found", async () => {
      const updateUsernameDto: UpdateUsernameDto = { username: "newUsername" };
      mockUserService.updateUsername.mockRejectedValue(
        new NotFoundException("User not found")
      );

      await expect(
        userController.updateUsername(
          mockRequestWithUser as RequestWithUser,
          updateUsernameDto
        )
      ).rejects.toThrowError(NotFoundException);
    });

    it("should throw NotFoundException if the username already exists", async () => {
      const updateUsernameDto: UpdateUsernameDto = {
        username: "existingUsername",
      };
      mockUserService.updateUsername.mockRejectedValue(
        new NotFoundException("Username already exists")
      );

      await expect(
        userController.updateUsername(
          mockRequestWithUser as RequestWithUser,
          updateUsernameDto
        )
      ).rejects.toThrowError(NotFoundException);
    });
  });

  describe("updatePassword", () => {
    it("should update password successfully", async () => {
      const updatePasswordDto: UpdatePasswordDto = {
        currentPassword: "currentPassword",
        password: "newPassword",
      };
      const mockUpdatedUser = {
        userId: "test-user-id",
        password: "newPassword",
      };
      mockUserService.updatePassword.mockResolvedValue(mockUpdatedUser);

      const result = await userController.updatePassword(
        mockRequestWithUser as RequestWithUser,
        updatePasswordDto
      );

      expect(result).toEqual(mockUpdatedUser);
      expect(userService.updatePassword).toHaveBeenCalledWith(
        "test-user-id",
        updatePasswordDto
      );
    });

    it("should throw NotFoundException when user is not found", async () => {
      const updatePasswordDto: UpdatePasswordDto = {
        currentPassword: "currentPassword",
        password: "newPassword",
      };
      mockUserService.updatePassword.mockRejectedValue(
        new NotFoundException("User not found")
      );

      await expect(
        userController.updatePassword(
          mockRequestWithUser as RequestWithUser,
          updatePasswordDto
        )
      ).rejects.toThrowError(NotFoundException);
    });

    it("should throw BadRequestException if current password is incorrect", async () => {
      const updatePasswordDto: UpdatePasswordDto = {
        currentPassword: "wrongPassword",
        password: "newPassword",
      };
      mockUserService.updatePassword.mockRejectedValue(
        new BadRequestException("Current password is incorrect")
      );

      await expect(
        userController.updatePassword(
          mockRequestWithUser as RequestWithUser,
          updatePasswordDto
        )
      ).rejects.toThrowError(BadRequestException);
    });
  });
});
