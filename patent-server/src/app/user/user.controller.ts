import { AuthGuard } from '@app/guards/auth/auth.guard';
import { RequestWithUser } from '@app/interfaces';
import { Controller, Get, Post, Delete, Param, Patch, Body, UseGuards, Req, HttpCode, BadRequestException, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiResponse } from '@nestjs/swagger';

import { CreateUserByAdminDto, UpdatePasswordDto, UpdateStatusDto, UpdateUserDto, UpdateUsernameDto } from './dto';
import { UserService } from './user.service';

@ApiTags('Users')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get('/profile')
  @HttpCode(200)
  @ApiOperation({ summary: 'Get user by access token' })
  @ApiResponse({ status: 200, description: 'Get User details' })
  async getUser(@Req() request: RequestWithUser) {
    const userId = request?.user?.user?.sub;
    if (!userId) {
      throw new BadRequestException('User ID not found in token');
    }
    return this.userService.getUserById(userId);
  }

  @Patch('profile')
@HttpCode(200)
@ApiOperation({ summary: 'Update user profile (name, email, username)' })
@ApiResponse({ status: 200, description: 'User profile updated' })
async updateUser(
  @Req() request: RequestWithUser,
  @Body() updateUserDto: UpdateUserDto
) {
  const userId = request?.user?.user?.sub;
  if (!userId) throw new BadRequestException('User ID not found in token');
  return await this.userService.updateUser(userId, updateUserDto);
}

  @Patch('update-username')
  @HttpCode(200)
  @ApiOperation({ summary: 'Update user name' })
  @ApiResponse({ status: 200, description: 'Username updated' })
  async updateUsername(@Req() request: RequestWithUser, @Body() updateUsernameDto: UpdateUsernameDto) {
    const userId = request?.user?.user?.sub;
    if (!userId) {
      throw new BadRequestException('User ID not found in token');
    }

    try {
      const result = await this.userService.updateUsername(userId, updateUsernameDto);
      return result;
    } catch (error) {
      throw error;
    }
  }

  @Patch('change-password')
  @HttpCode(200)
  @ApiOperation({ summary: 'Update user password' })
  @ApiResponse({ status: 200, description: 'Password has been updated' })
  async updatePassword(@Req() request: RequestWithUser, @Body() updatePasswordDto: UpdatePasswordDto) {
    const userId = request?.user?.user?.sub;
    if (!userId) {
      throw new BadRequestException('User ID not found in token');
    }

    try {
      const result = await this.userService.updatePassword(userId, updatePasswordDto);
      return result;
    } catch (error) {
      throw error;
    }
  }

  @Get('dashboard')
  @HttpCode(200)
  @ApiOperation({ summary: 'Get all users without pagination for dashboard (Admin only)' })
  @ApiResponse({ status: 200, description: 'Return all users' })
  async getAllUsersForDashboard(@Req() request: RequestWithUser) {
    // Check if user is admin before returning all users
    const userId = request?.user?.user?.sub;
    if (!userId) {
      throw new BadRequestException('User ID not found in token');
    }

    return this.userService.getAllUsersWithoutPagination();
  }

  @Get()
  @HttpCode(200)
  @ApiOperation({ summary: 'Get all users with pagination (Admin only)' })
  @ApiResponse({ status: 200, description: 'Return paginated users' })
  async getAllUsers(
    @Req() request: RequestWithUser,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '6'
  ) {
    // Check if user is admin before returning all users
    const userId = request?.user?.user?.sub;
    if (!userId) {
      throw new BadRequestException('User ID not found in token');
    }

    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 6;

    return this.userService.getAllUsers(pageNum, limitNum);
  }

  @Post()
  @HttpCode(201)
  @ApiOperation({ summary: 'Create a new user (Admin only)' })
  @ApiResponse({ status: 201, description: 'User has been created' })
  async createUser(@Body() createUserDto: CreateUserByAdminDto) {
    return this.userService.createUser(createUserDto);
  }

  @Get(':id')
  @HttpCode(200)
  @ApiOperation({ summary: 'Get user by ID (Admin only)' })
  @ApiResponse({ status: 200, description: 'Return user details' })
  async getUserById(@Param('id') userId: string) {
    return this.userService.getUserById(userId);
  }

  @Patch(':id')
  @HttpCode(200)
  @ApiOperation({ summary: 'Update user by ID (Admin only)' })
  @ApiResponse({ status: 200, description: 'User has been updated' })
  async updateUserById(@Param('id') userId: string, @Body() updateUserDto: UpdateUserDto) {
    return this.userService.updateUser(userId, updateUserDto);
  }

  @Delete(':id')
  @HttpCode(200)
  @ApiOperation({ summary: 'Delete user by ID (Admin only)' })
  @ApiResponse({ status: 200, description: 'User has been deleted' })
  async deleteUser(@Param('id') userId: string) {
    return this.userService.deleteUser(userId);
  }

  @Patch(':id/status')
  @HttpCode(200)
  @ApiOperation({ summary: 'Update user status by ID (Admin only)' })
  @ApiResponse({ status: 200, description: 'User status has been updated' })
  async updateUserStatus(@Param('id') userId: string, @Body() updateStatusDto: UpdateStatusDto) {
    return this.userService.updateUserStatus(userId, updateStatusDto.status);
  }
}
