import {
  CURRENT_PASSWORD_INCORRECT,
  USER_NOT_FOUND,
  USERNAME_ALREADY_EXIST,
} from "@app/constants/user.constants";
import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
  ConflictException,
  Logger,
} from "@nestjs/common";
import * as bcrypt from "bcrypt";

import {
  CreateUserByAdminDto,
  UpdateUserDto,
  UpdateAvatarDto,
  UpdatePasswordDto,
  UpdateUsernameDto,
} from "./dto";

@Injectable()
export class UserService {
  constructor(@Inject("UserRepository") private readonly userRepository) {}

  async getAllUsersWithoutPagination() {
    const users = await this.userRepository.getMany({});
    return users.map((user) => {
      const userObj = user.toObject ? user.toObject() : user;
      const { password, ...userWithoutPassword } = userObj;
      return userWithoutPassword;
    });
  }

  async getAllUsers(page = 1, limit = 6) {
    const skip = (page - 1) * limit;

    const totalCount = await this.userRepository.count({});

    const users = await this.userRepository.getModel
      .find({})
      .skip(skip)
      .limit(limit)
      .exec();

    const usersWithoutPassword = users.map((user) => {
      const userObj = user.toObject ? user.toObject() : user;
      const { password, ...userWithoutPassword } = userObj;
      return userWithoutPassword;
    });

    return {
      users: usersWithoutPassword,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        pageSize: limit,
        hasNext: page < Math.ceil(totalCount / limit),
        hasPrevious: page > 1,
      },
    };
  }

  async getUserById(userId: string) {
    const user = await this.userRepository.findOne({ _id: userId });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    return user;
  }

  async createUser(createUserDto: CreateUserByAdminDto) {
    const existingEmail = await this.userRepository.findOne({
      email: createUserDto.email,
    });
    if (existingEmail) {
      throw new ConflictException("User with this email already exists");
    }

    if (createUserDto.username) {
      const existingUsername = await this.userRepository.findOne({
        username: createUserDto.username,
      });
      if (existingUsername) {
        throw new ConflictException("Username already taken");
      }
    }

    const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

    const user = await this.userRepository.create({
      ...createUserDto,
      password: hashedPassword,
    });

    const { password, ...userWithoutPassword } = user.toObject();
    return userWithoutPassword;
  }

  async deleteUser(userId: string) {
    const user = await this.userRepository.findOne({ _id: userId });
    if (!user) {
      throw new NotFoundException(USER_NOT_FOUND);
    }

    await this.userRepository.deleteOne({ _id: userId });
    return { message: "User deleted successfully" };
  }

  async updateUser(userId: string, updateUserDto: UpdateUserDto) {
    Logger.log(`Updating user ${userId} with data:`, updateUserDto);
    const user = await this.userRepository.findOne({ _id: userId });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);

    const updateData = {};

    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const existingEmail = await this.userRepository.findOne({
        email: updateUserDto.email,
        _id: { $ne: userId },
      });
      if (existingEmail) throw new ConflictException("Email is already in use");
      updateData["email"] = updateUserDto.email;
    }

    if (updateUserDto.username && updateUserDto.username !== user.username) {
      const existingUsername = await this.userRepository.findOne({
        username: updateUserDto.username,
        _id: { $ne: userId },
      });
      if (existingUsername) throw new ConflictException(USERNAME_ALREADY_EXIST);
      updateData["username"] = updateUserDto.username;
    }
    if (updateUserDto.status) {
      updateData["status"] = updateUserDto.status;
    }

    if (Object.keys(updateData).length === 0) {
      Logger.log("No update data provided, returning current user");
      return user;
    }

    Logger.log("Final update data:", updateData);

    try {
      const updatedUser = await this.userRepository.findOneAndUpdate(
        { _id: userId },
        updateData,
        { new: true, upsert: false },
        true
      );

      if (!updatedUser) {
        throw new NotFoundException(`Failed to update user with ID: ${userId}`);
      }
      const verifiedUser = await this.userRepository.findOne(
        { _id: userId },
        true
      );
      Logger.log("Verified user after update:", verifiedUser);

      return verifiedUser;
    } catch (error) {
      Logger.error("Error updating user:", error);
      throw error;
    }
  }

  async updateAvatar(userId: string, updateAvatarDto: UpdateAvatarDto) {
    const user = await this.userRepository.findOne({ _id: userId });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);

    const updatedUser = await this.userRepository.findOneAndUpdate(
      { _id: userId },
      { $set: { avatar: updateAvatarDto.avatar } },
      { new: true }
    );
    return updatedUser;
  }

  async updateUsername(userId: string, updateUsernameDto: UpdateUsernameDto) {
    const user = await this.userRepository.findOne({ _id: userId });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);

    const userExist = await this.userRepository.findOne({
      username: updateUsernameDto.username,
      _id: { $ne: userId },
    });
    if (userExist) throw new ConflictException(USERNAME_ALREADY_EXIST);

    try {
      const updatedUser = await this.userRepository.findOneAndUpdate(
        { _id: userId },
        { $set: { username: updateUsernameDto.username } },
        { new: true }
      );
      return updatedUser;
    } catch (error) {
      throw error;
    }
  }

  async updatePassword(userId: string, updatePasswordDto: UpdatePasswordDto) {
    const user = await this.userRepository.findOne({ _id: userId });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);

    const isPasswordValid = await bcrypt.compare(
      updatePasswordDto.currentPassword,
      user.password
    );
    if (!isPasswordValid) {
      throw new BadRequestException(CURRENT_PASSWORD_INCORRECT);
    }

    try {
      const hashedPassword = await bcrypt.hash(updatePasswordDto.password, 10);
      const updatedUser = await this.userRepository.findOneAndUpdate(
        { _id: userId },
        { $set: { password: hashedPassword } },
        { new: true }
      );
      return updatedUser;
    } catch (error) {
      throw error;
    }
  }

  async updateUserStatus(userId: string, status: string) {
    const user = await this.userRepository.findOne({ _id: userId });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    try {
      const updatedUser = await this.userRepository.findOneAndUpdate(
        { _id: userId },
        { $set: { status } },
        { new: true }
      );
      return updatedUser;
    } catch (error) {
      Logger.error("Error updating user status:", error);
      throw error;
    }
  }
}
