import { AppLogger } from '@app/services/logger/AppLogger';
import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';
import { JsonWebTokenError, TokenExpiredError } from 'jsonwebtoken';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const logger = new AppLogger();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';

    // ✅ Handle NestJS-specific HTTP errors
    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();
      message =
        typeof exceptionResponse === 'string' ? exceptionResponse : (exceptionResponse as any).message || message;
    }
    // ✅ Handle JWT Errors
    else if (exception instanceof TokenExpiredError) {
      status = HttpStatus.UNAUTHORIZED;
      message = 'JWT Token has expired';
    } else if (exception instanceof JsonWebTokenError) {
      status = HttpStatus.UNAUTHORIZED;
      message = 'Invalid JWT Token';
    }
    // ✅ Handle SendGrid Errors (or any other external API errors)
    else if ((exception as any)?.response?.body?.errors) {
      status = HttpStatus.BAD_REQUEST;
      message = (exception as any).response.body.errors.map((err: any) => err.message).join(', ');
    }
    // ✅ Handle generic errors (e.g., database connection failures)
    else if (exception instanceof Error) {
      message = exception.message;
    }

    // 🚀 Log the error
    logger.error(`🚨 Error on ${request.method} ${request.url}: ${message}`);

    response.status(status).json({
      success: false,
      statusCode: status,
      message,
      timestamp: new Date().toISOString(),
      path: request.url,
    });
  }
}
