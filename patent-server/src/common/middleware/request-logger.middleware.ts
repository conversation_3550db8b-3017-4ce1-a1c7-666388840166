import { AppLogger } from '@app/services/logger/AppLogger'; // Adjust path if needed
import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class RequestLoggerMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const startTime = Date.now();
    const logger = new AppLogger();

    logger.log(`📥 ${req.method} ${req.originalUrl} from ${req.ip}`, 'RequestLogger');

    res.on('finish', () => {
      const duration = Date.now() - startTime;
      const isSuccess = res.statusCode >= 200 && res.statusCode < 300;
      const logMessage = `${isSuccess ? '✅' : '⚠️'} ${req.method} ${req.originalUrl} [${res.statusCode}] - ${duration}ms`;

      if (isSuccess) {
        logger.log(logMessage, 'RequestLogger');
      } else {
        logger.warn(logMessage, 'RequestLogger'); // Highlight failed requests
      }
    });

    next();
  }
}
