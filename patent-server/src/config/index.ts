import 'dotenv/config';
import { AppLogger } from '@app/services/logger/AppLogger';
import { hasEmptyValues } from '@app/utils/functionality.utils';

const logger = new AppLogger();

type RootConfig = {
  PORT: string;
  CLIENT_URL: string;
  DATABASE_URI: string;
  JWT_ACCESS_SECRET: string;
  JWT_EMAIL_SECRET: string;
  JWT_SIGN_UP_EMAIL_SECRET: string;
  JWT_REFRESH_SECRET: string;
  JWT_EXPIRY: string;
  SENDGRID_API_KEY: string;
  SENDGRID_ACCOUNT_EMAIL: string;
  SENDGRID_TEM_ID_FOR_VERIFY_EMAIL: string;
  SENDGRID_TEM_ID_FOR_FORGOT_EMAIL: string;
  USER_ENCRYPT_KEY: string;
  CLOUDINARY_CLOUD_NAME: string;
  CLOUDINARY_API_KEY: string;
  CLOUDINARY_API_SECRET: string;
  AWS_S3_ACCESS_KEY_ID: string;
  AWS_S3_SECRET_ACCESS_KEY: string;
  AWS_S3_BUCKET_NAME: string;
  AWS_S3_REGION: string;
};

const envVariables: RootConfig = {
  PORT: process.env.PORT,
  CLIENT_URL: process.env.CLIENT_URL,
  DATABASE_URI: process.env.DATABASE_URI,
  JWT_ACCESS_SECRET: process.env.JWT_ACCESS_SECRET,
  JWT_EMAIL_SECRET: process.env.JWT_EMAIL_SECRET,
  JWT_SIGN_UP_EMAIL_SECRET: process.env.JWT_SIGN_UP_EMAIL_SECRET,
  JWT_REFRESH_SECRET: process.env.JWT_REFRESH_SECRET,
  JWT_EXPIRY: process.env.JWT_EXPIRY || '7d',
  SENDGRID_API_KEY: process.env.SENDGRID_API_KEY,
  SENDGRID_ACCOUNT_EMAIL: process.env.SENDGRID_ACCOUNT_EMAIL,
  SENDGRID_TEM_ID_FOR_VERIFY_EMAIL: process.env.SENDGRID_TEM_ID_FOR_VERIFY_EMAIL,
  SENDGRID_TEM_ID_FOR_FORGOT_EMAIL: process.env.SENDGRID_TEM_ID_FOR_FORGOT_EMAIL,
  USER_ENCRYPT_KEY: process.env.USER_ENCRYPT_KEY,
  CLOUDINARY_CLOUD_NAME: process.env.CLOUDINARY_CLOUD_NAME,
  CLOUDINARY_API_KEY: process.env.CLOUDINARY_API_KEY,
  CLOUDINARY_API_SECRET: process.env.CLOUDINARY_API_SECRET,
  AWS_S3_ACCESS_KEY_ID: process.env.AWS_S3_ACCESS_KEY_ID,
  AWS_S3_SECRET_ACCESS_KEY: process.env.AWS_S3_SECRET_ACCESS_KEY,
  AWS_S3_BUCKET_NAME: process.env.AWS_S3_BUCKET_NAME,
  AWS_S3_REGION: process.env.AWS_S3_REGION,
};

const emptyFields = hasEmptyValues(envVariables, '', logger);

if (emptyFields.length > 0) {
  logger.error('\n\nPLEASE ENSURE ALL ENVIRONMENT VARIABLES ARE SET\n\n');
  process.exit(1);
}

export { envVariables };
