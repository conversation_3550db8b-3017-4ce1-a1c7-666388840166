// Use CommonJS require instead of ES module import
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// MongoDB connection URL
const MONGODB_URI = "mongodb+srv://ghayas:<EMAIL>/ghayas?retryWrites=true&w=majority";

// Sample data
const users = [
  {
    name: 'Admin User',
    email: '<EMAIL>',
    username: 'admin',
    password: 'admin123',
    role: 'Admin',
    isAdmin: true,
    status: 'active'
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    username: 'johnsmith',
    password: 'password123',
    role: 'Student',
    isAdmin: false,
    status: 'active'
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    username: 'sarah<PERSON>',
    password: 'password123',
    role: 'Student',
    isAdmin: false,
    status: 'active'
  },
  {
    name: '<PERSON>',
    email: 'micha<PERSON>@example.com',
    username: 'mi<PERSON><PERSON><PERSON>',
    password: 'password123',
    role: 'Instructor',
    isAdmin: false,
    status: 'active'
  },
  {
    name: 'Emily Davis',
    email: '<EMAIL>',
    username: 'emilyd',
    password: 'password123',
    role: 'Student',
    isAdmin: false,
    status: 'active'
  }
];

const courses = [
  {
    title: 'Patent Basics',
    description: 'Learn the fundamentals of patents and intellectual property protection.',
    price: 199.99,
    image: 'https://images.unsplash.com/photo-1589829085413-56de8ae18c73?ixlib=rb-4.0.3',
    modules: [
      {
        title: 'Introduction to Patents',
        type: 'text',
        content: 'A patent is an intellectual property right granted by the government to an inventor...',
        order: 0
      },
      {
        title: 'Patent Application Process',
        type: 'pdf',
        content: 'https://example.com/patents/application-process.pdf',
        order: 1
      },
      {
        title: 'Patent Types Overview',
        type: 'video',
        content: 'https://example.com/videos/patent-types.mp4',
        order: 2
      }
    ]
  },
  {
    title: 'Trademark Law Essentials',
    description: 'Comprehensive guide to trademark registration and protection strategies.',
    price: 249.99,
    image: 'https://images.unsplash.com/photo-1450101499163-c8848c66ca85?ixlib=rb-4.0.3',
    modules: [
      {
        title: 'What is a Trademark?',
        type: 'text',
        content: 'A trademark is a symbol, word, or phrase legally registered or established by use...',
        order: 0
      },
      {
        title: 'Trademark Registration Process',
        type: 'image',
        content: 'https://example.com/images/trademark-process.jpg',
        order: 1
      },
      {
        title: 'International Trademark Protection',
        type: 'text',
        content: 'Protecting your trademark internationally involves several systems...',
        order: 2
      }
    ]
  },
  {
    title: 'Copyright Protection',
    description: 'Everything you need to know about copyright law and content protection.',
    price: 179.99,
    image: 'https://images.unsplash.com/photo-1532619675605-1ede6c2ed2b0?ixlib=rb-4.0.3',
    modules: [
      {
        title: 'Copyright Fundamentals',
        type: 'text',
        content: 'Copyright is a form of intellectual property that protects original works of authorship...',
        order: 0
      },
      {
        title: 'Copyright Registration Demo',
        type: 'video',
        content: 'https://example.com/videos/copyright-demo.mp4',
        order: 1
      },
      {
        title: 'Fair Use Doctrine',
        type: 'text',
        content: 'The fair use doctrine is a limitation on copyright that allows limited use of copyrighted material...',
        order: 2
      }
    ]
  },
  {
    title: 'IP Strategy for Startups',
    description: 'Strategic approach to intellectual property for new businesses and entrepreneurs.',
    price: 299.99,
    image: 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3',
    modules: [
      {
        title: 'IP Portfolio Management',
        type: 'text',
        content: 'Effective management of intellectual property assets is crucial for startups...',
        order: 0
      },
      {
        title: 'Funding and IP Valuation',
        type: 'pdf',
        content: 'https://example.com/documents/ip-valuation.pdf',
        order: 1
      },
      {
        title: 'IP Strategy Workshop',
        type: 'video',
        content: 'https://example.com/videos/ip-strategy.mp4',
        order: 2
      }
    ]
  },
  {
    title: 'International Patent Law',
    description: 'Navigate the complexities of international patent systems and treaties.',
    price: 349.99,
    image: 'https://images.unsplash.com/photo-1569017388730-28d93d5bdf1b?ixlib=rb-4.0.3',
    modules: [
      {
        title: 'PCT System Overview',
        type: 'text',
        content: 'The Patent Cooperation Treaty (PCT) is an international patent law treaty...',
        order: 0
      },
      {
        title: 'European Patent Office Procedures',
        type: 'image',
        content: 'https://example.com/images/epo-flowchart.jpg',
        order: 1
      },
      {
        title: 'Asian Patent Systems Comparison',
        type: 'pdf',
        content: 'https://example.com/documents/asia-patents.pdf',
        order: 2
      }
    ]
  }
];

// Sample books data
const books = [
  {
    title: 'The Value of Your Ideas',
    description: 'This insightful book offers a comprehensive guide on intellectual property.',
    price: 14.99,
    type: 'softcopy',
    image: 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?ixlib=rb-4.0.3',
    author: 'Dr. Robert Johnson',
    publishedYear: 2023,
    publisher: 'Legal Press',
    status: 'pending'
  },
  {
    title: 'Unlocking the Power of Your Imagination',
    description: 'A deep dive into unlocking the creative potential of the mind for success.',
    price: 112.99,
    type: 'hardcopy',
    image: 'https://images.unsplash.com/photo-1544967082-d9d25d867d66?ixlib=rb-4.0.3',
    author: 'Amanda Williams',
    publishedYear: 2022,
    publisher: 'Digital Publishing House'
    // No status needed for softcopy
  },
  {
    title: 'Transforming Thoughts into Treasures',
    description: 'Learn how to turn your thoughts into valuable assets with this comprehensive guide.',
    price: 14.99,
    type: 'softcopy',
    image: 'https://images.unsplash.com/photo-1589829085413-56de8ae18c73?ixlib=rb-4.0.3',
    author: 'Thomas Chen',
    publishedYear: 2021,
    publisher: 'Business Publishing',
    status: 'processing'
  },
  {
    title: 'Harnessing the Genius Within',
    description: 'Tap into your inner genius and unlock your potential with this inspiring guide.',
    price: 14.99,
    type: 'softcopy',
    image: 'https://images.unsplash.com/photo-1532619675605-1ede6c2ed2b0?ixlib=rb-4.0.3',
    author: 'Sarah Adams',
    publishedYear: 2022,
    publisher: 'Creator\'s Press'
    // No status needed for softcopy
  },
  {
    title: 'The Future of Innovation',
    description: 'A forward-looking perspective on the future of innovation in various industries.',
    price: 19.99,
    type: 'hardcopy',
    image: 'https://images.unsplash.com/photo-1569017388730-28d93d5bdf1b?ixlib=rb-4.0.3',
    author: 'Michael Brown',
    publishedYear: 2023,
<<<<<<< HEAD
    publisher: 'Global Legal Press',
    status: 'shipped'
=======
    publisher: 'Innovation Press'
>>>>>>> a5eb61bcb06a789ebb16d7bc03142e972533c9aa
  },
  {
    title: 'Creative Problem Solving',
    description: 'Master the art of creative problem-solving with proven strategies and techniques.',
    price: 12.99,
    type: 'softcopy',
    image: 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?ixlib=rb-4.0.3',
    author: 'Emily Davis',
    publishedYear: 2022,
    publisher: 'Creative Solutions'
  },
  {
    title: 'The Art of Negotiation',
    description: 'Learn the fundamental skills needed for effective negotiation in any environment.',
    price: 16.99,
    type: 'hardcopy',
    image: 'https://images.unsplash.com/photo-1544967082-d9d25d867d66?ixlib=rb-4.0.3',
    author: 'David Wilson',
    publishedYear: 2021,
    publisher: 'Business Experts'
  },
  {
    title: 'Mastering Time Management',
    description: 'Become a time management expert and optimize your daily productivity.',
    price: 14.99,
    type: 'softcopy',
    image: 'https://images.unsplash.com/photo-1589829085413-56de8ae18c73?ixlib=rb-4.0.3',
    author: 'Jennifer Lee',
    publishedYear: 2023,
    publisher: 'Productivity Press'
  }
];


// Connect to MongoDB
mongoose.connect(MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('Connected to MongoDB'))
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

// Define schemas
const userSchema = new mongoose.Schema({
  name: String,
  email: String,
  username: String,
  password: String,
  role: String,
  isAdmin: Boolean,
  status: String,
  joinDate: { type: Date, default: Date.now },
  courses: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Course' }],
  books: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Book' }]
}, { timestamps: true });

const moduleSchema = new mongoose.Schema({
  title: String,
  type: { type: String, enum: ['text', 'pdf', 'image', 'video'] },
  content: String,
  order: Number,
  publicId: { type: String, default: '' },
  resourceType: { type: String, enum: ['image', 'video', 'raw', ''], default: '' }
}, { timestamps: true });

const courseSchema = new mongoose.Schema({
  title: String,
  description: String,
  price: Number,
  image: String,
  imagePublicId: { type: String, default: '' },
  modules: [moduleSchema],
  instructor: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
}, { timestamps: true });

const purchaseSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  courseId: { type: mongoose.Schema.Types.ObjectId, ref: 'Course' },
  amount: Number,
  date: { type: Date, default: Date.now },
  status: { type: String, enum: ['Pending', 'Completed', 'Failed', 'Refunded'], default: 'Completed' }
}, { timestamps: true });

const bookSchema = new mongoose.Schema({
  title: String,
  description: String,
  price: Number,
  type: { type: String, enum: ['hardcopy', 'softcopy'] },
  image: String,
  imagePublicId: { type: String, default: '' },
  author: String,
  publishedYear: Number,
  publisher: String,
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  status: {
    type: String,
    enum: ['pending', 'processing', 'shipped', 'delivered'],
    default: 'pending'
  }
}, { timestamps: true });

const User = mongoose.model('User', userSchema);
const Course = mongoose.model('Course', courseSchema);
const Purchase = mongoose.model('Purchase', purchaseSchema);
const Book = mongoose.model('Book', bookSchema);

// Seed data
async function seedData() {
  try {
    // Clear existing data
    await User.deleteMany({});
    await Course.deleteMany({});
    await Purchase.deleteMany({});
    await Book.deleteMany({});


    // Create users with hashed passwords
    const createdUsers = await Promise.all(users.map(async (userData) => {
      // Hash the password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(userData.password, salt);

      const newUser = new User({
        ...userData,
        password: hashedPassword
      });

      return newUser.save();
    }));


    // Find instructor and admin users
    const instructor = createdUsers.find(user => user.role === 'Instructor');
    const admin = createdUsers.find(user => user.isAdmin);
    const regularUsers = createdUsers.filter(user => !user.isAdmin);

    // Create courses assigned to the instructor or admin if no instructor
    const createdCourses = await Promise.all(courses.map(courseData => {
      const newCourse = new Course({
        ...courseData,
        instructor: instructor ? instructor._id : admin._id
      });

      return newCourse.save();
    }));


    // Create books with assigned users for hardcopy books
    const createdBooks = await Promise.all(books.map(bookData => {
      const newBook = new Book(bookData);

      // If it's a hardcopy book, assign a random user
      if (bookData.type === 'hardcopy') {
        // Get a random regular user
        const randomUser = regularUsers[Math.floor(Math.random() * regularUsers.length)];
        newBook.userId = randomUser._id;
      }

      return newBook.save();
    }));


    // Create purchases - each non-admin user purchases 1-3 random courses
    const purchasesToCreate = [];

    for (const user of regularUsers) {
      // User purchases 1-3 random courses
      const numCourses = Math.floor(Math.random() * 3) + 1;
      const shuffledCourses = [...createdCourses].sort(() => 0.5 - Math.random());
      const selectedCourses = shuffledCourses.slice(0, numCourses);

      // Create purchase records for the selected courses
      for (const course of selectedCourses) {
        purchasesToCreate.push({
          userId: user._id,
          courseId: course._id,
          amount: course.price,
          date: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000), // Random date in last 30 days
          status: 'Completed'
        });

        // Add course to user's courses array
        user.courses.push(course._id);
      }

      // Find books already assigned to this user
      const userBooks = createdBooks.filter(book =>
        book.type === 'hardcopy' &&
        book.userId &&
        book.userId.toString() === user._id.toString()
      );

      // Add these books to user's books array
      for (const book of userBooks) {
        user.books.push(book._id);
      }

      // If user doesn't have any hardcopy books yet, assign 1-2 random ones
      if (userBooks.length === 0) {
        const availableSoftcopyBooks = createdBooks.filter(book => book.type === 'softcopy');
        const numBooks = Math.floor(Math.random() * 2) + 1;
        const shuffledBooks = [...availableSoftcopyBooks].sort(() => 0.5 - Math.random());
        const selectedBooks = shuffledBooks.slice(0, numBooks);

        for (const book of selectedBooks) {
          // Add book to user's books array
          user.books.push(book._id);
        }
      }

      // Save updated user with course and book references
      await user.save();
    }

    // Save all purchases
    if (purchasesToCreate.length > 0) {
      await Purchase.insertMany(purchasesToCreate);
    }


  } catch (error) {
    console.error('Error seeding data:', error);
  } finally {
    // Close the connection when done
    mongoose.connection.close();
  }
}

// Run the seeding function
seedData();