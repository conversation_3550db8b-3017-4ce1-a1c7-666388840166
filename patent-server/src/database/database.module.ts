import { envVariables } from '@app/config';
import { User, UserSchema } from '@database';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ModelDefinition } from '@nestjs/mongoose';

const models: Array<ModelDefinition> = [{ name: User.name, schema: UserSchema }];
const repositories = [];

@Module({
  imports: [
    MongooseModule.forRootAsync({
      useFactory: () => ({
        uri: envVariables.DATABASE_URI,
      }),
    }),
    MongooseModule.forFeature([...models]),
  ],
  providers: [...repositories],
  exports: [MongooseModule.forFeature([...models]), ...repositories],
})
export class DatabaseModule {}
