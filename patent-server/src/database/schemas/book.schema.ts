import mongoose from 'mongoose';

export const BookSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Book title is required'],
    trim: true
  },
  description: {
    type: String,
    required: [true, 'Book description is required']
  },
  price: {
    type: Number,
    required: [true, 'Book price is required'],
    min: 0
  },
  type: {
    type: String,
    enum: ['hardcopy', 'softcopy'],
    required: [true, 'Book type is required']
  },
  image: {
    type: String,
    required: [true, 'Book image is required'],
    default: 'https://images.unsplash.com/photo-1543002588-bfa74002ed7e?ixlib=rb-4.0.3'
  },
  imagePublicId: {
    type: String,
    default: ''
  },
  pdfFile: {
    type: String,
    default: ''
  },
  pdfPublicId: {
    type: String,
    default: ''
  },
  author: {
    type: String,
    default: ''
  },
  publishedYear: {
    type: Number,
    default: null
  },
  publisher: {
    type: String,
    default: ''
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  status: {
    type: String,
    enum: ['in process', 'shipped', 'delivered'],
    default: 'in process'
  }
}, {
  timestamps: true
});