import mongoose from 'mongoose';

// Module Schema (for course modules)
export const ModuleSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Module title is required']
  },
  type: {
    type: String,
    enum: ['text', 'pdf', 'image', 'video'],
    required: [true, 'Module type is required']
  },
  content: {
    type: String,
    required: [true, 'Module content is required']
  },
  publicId: {
    type: String,
    default: ''
  },
  resourceType: {
    type: String,
    enum: ['image', 'video', 'raw', 'pdf', ''],
    default: ''
  }
}, { timestamps: true });

// Course Schema
export const CourseSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Course title is required'],
    trim: true
  },
  description: {
    type: String,
    required: [true, 'Course description is required']
  },
  price: {
    type: Number,
    required: [true, 'Course price is required'],
    min: 0
  },
  image: {
    type: String,
    required: [true, 'Course image is required'],
    default: 'https://images.unsplash.com/photo-1589829085413-56de8ae18c73?ixlib=rb-4.0.3'
  },
  imagePublicId: {
    type: String,
    default: ''
  },
  // New fields
  certificate: {
    type: Boolean,
    default: false
  },
  level: {
    type: String,
    enum: ['Beginner', 'Intermediate', 'Expert'],
    default: 'Beginner'
  },
  hours: {
    type: Number,
    default: 0
  },
  modules: [ModuleSchema],
  instructor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});