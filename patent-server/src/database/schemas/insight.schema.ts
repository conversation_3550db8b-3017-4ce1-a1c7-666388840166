import mongoose from 'mongoose';

export const InsightSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, 'Insight title is required'],
      trim: true,
    },
    details: {
      type: String,
      required: [true, 'Insight details are required'],
      trim: true,
    },
    images: [
      {
        url: {
          type: String,
          required: true,
        },
        publicId: {
          type: String,
          required: true,
        },
      },
    ],
    relatedTo: {
      type: {
        type: String,
        enum: ['Book', 'Course'], // Changed to uppercase to match model names
        required: [true, 'Related entity type is required'],
      },
      id: {
        type: mongoose.Schema.Types.ObjectId,
        refPath: 'relatedTo.type',
        required: [true, 'Related entity ID is required'],
      },
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
  },
  { timestamps: true }
);

export interface Insight extends mongoose.Document {
  title: string;
  details: string;
  images: Array<{
    url: string;
    publicId: string;
  }>;
  relatedTo: {
    type: 'Book' | 'Course'; // Changed to uppercase
    id: mongoose.Types.ObjectId;
  };
  createdBy: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
