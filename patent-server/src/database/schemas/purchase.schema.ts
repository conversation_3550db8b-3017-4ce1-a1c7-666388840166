import mongoose from 'mongoose';

// Define shipping address schema
const ShippingAddressSchema = new mongoose.Schema({
  street: {
    type: String,
    required: true
  },
  city: {
    type: String,
    required: true
  },
  state: {
    type: String,
    required: true
  },
  zipCode: {
    type: String,
    required: true
  },
  country: {
    type: String,
    required: true
  },
  phoneNumber: {
    type: String,
    required: true
  }
}, { _id: false });

export const PurchaseSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  courseId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    // Making courseId optional to support book purchases
    required: function() {
      return !this.bookId; // courseId is required if bookId is not present
    }
  },
  bookId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Book',
    // Making bookId optional to support course purchases
    required: function() {
      return !this.courseId; // bookId is required if courseId is not present
    }
  },
  amount: {
    type: Number,
    required: true
  },
  date: {
    type: Date,
    default: Date.now
  },
  status: {
    type: String,
    enum: ['Pending', 'Completed', 'Failed', 'Refunded'],
    default: 'Completed'
  },
  paymentMethod: {
    type: String,
    enum: ['CreditCard', 'PayPal', 'BankTransfer', 'Crypto', 'Other'],
    required: true
  },
  transactionId: {
    type: String,
    required: true
  },
  // New fields for shipping information
  requiresShipping: {
    type: Boolean,
    default: false
  },
  shippingAddress: {
    type: ShippingAddressSchema,
    required: function() {
      return this.requiresShipping === true;
    }
  }
}, {
  timestamps: true
});