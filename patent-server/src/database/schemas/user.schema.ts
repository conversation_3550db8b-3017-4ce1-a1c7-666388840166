import { Roles, SsoSources, UserStatuses } from '@app/utils';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Schema as MongooseSchema } from 'mongoose';
import * as mongoose from 'mongoose';

@Schema({
  _id: true,
  id: false,
  timestamps: true,
  versionKey: false,
  collection: 'users',
})
export class User {
  @Prop({
    type: String,
    required: true,
  })
  firstName: string;

  @Prop({
    type: String,
    required: true,
  })
  lastName: string;

  @Prop({
    type: String,
    trim: true,
    required: true,
    unique: true,
  })
  username: string;

  @Prop({
    type: String,
    required: true,
    trim: true,
    unique: true,
    immutable: true,
  })
  email: string;

  @Prop({
    type: String,
    trim: true,
  })
  password: string;

  @Prop({
    type: String,
    default: null,
    trim: true,
  })
  avatar: string;

  @Prop({
    type: String,
    default: Roles.user,
    trim: true,
    enum: Array.from(Object.values(Roles)),
  })
  role: string;

  @Prop({
    type: String,
    default: UserStatuses.pending,
    enum: Array.from(Object.values(UserStatuses)),
  })
  status: string;

  @Prop({
    type: Boolean,
    default: false
  })
  isAdmin: boolean;  // Added isAdmin field

  @Prop({ type: String, default: SsoSources.email, trim: true, enum: Array.from(Object.values(SsoSources)) })
  ssoSource?: string;

  @Prop({ type: String, default: null, trim: true })
  ssoId?: string;

  @Prop({ type: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Book' }], default: [] })
  books?: mongoose.Types.ObjectId[];

  @Prop({ type: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Course' }], default: [] })
  courses?: mongoose.Types.ObjectId[];

  @Prop({
    type: String,
    default: null,
    trim: true,
  })
  stripeCustomerId?: string;
}

export interface UserDocument extends Partial<Document> {
  _id?: string;
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  password: string;
  avatar: string;
  role: string;
  status: string;
  isAdmin: boolean;
  ssoId: string;
  books?: mongoose.Types.ObjectId[];
  courses?: mongoose.Types.ObjectId[];
  stripeCustomerId?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export const UserSchema: MongooseSchema<UserDocument> = SchemaFactory.createForClass(User) as any;
