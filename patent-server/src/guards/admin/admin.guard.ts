import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { Observable } from 'rxjs';

@Injectable()
export class AdminGuard implements CanActivate {
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    
    if (!user) {
      throw new UnauthorizedException('User not authenticated');
    }
    
    const isAdmin = user.isAdmin === true || 
                   (typeof user.role === 'string' && user.role.toLowerCase() === 'admin');
                   
    if (!isAdmin) {
      throw new UnauthorizedException('Admin access required');
    }
    
    return true;
  }
}
