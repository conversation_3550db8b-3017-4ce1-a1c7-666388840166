import { envVariables } from '@app/config';
import { decryptData } from '@app/utils/functionality.utils';
import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(private readonly jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);
    if (!token) {
      throw new UnauthorizedException('No token provided');
    }

    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: envVariables.JWT_ACCESS_SECRET,
      });

      // Decrypt the userInfo field to get the user data including role
      let userInfo = null;
      let role = null;
      let isAdmin = false;

      if (payload.userInfo) {
        try {
          const decryptedData = await decryptData(payload.userInfo);
          userInfo = JSON.parse(decryptedData);

          // Check for admin status in multiple ways
          role = userInfo.role;

          // Check for admin status in multiple ways (case-insensitive)
          isAdmin =
            (typeof userInfo.role === 'string' && userInfo.role.toLowerCase() === 'admin') ||
            userInfo.isAdmin === true;

        } catch (decryptError) {
          console.error('Failed to decrypt user info:', decryptError);
        }
      }



      // Set user object with admin status
      request.user = {
        user: payload,
        role: role || 'user', // Default to 'user' if role is not found
        isAdmin: isAdmin
      };
      return true;
    } catch (error) {
      console.error('Auth error:', error);
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  private extractTokenFromHeader(request): string | undefined {
    const authHeader = request.headers.authorization;
    if (!authHeader) {
      return undefined;
    }
    const [type, token] = authHeader.split(' ');
    return type === 'Bearer' ? token : undefined;
  }
}
