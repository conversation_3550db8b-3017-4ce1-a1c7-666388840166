import { GlobalExceptionFilter } from '@app/common/exceptionFilters/http-exception.filter';
import { envVariables } from '@app/config';
import { AppLogger } from '@app/services/logger/AppLogger';
import { RequestMethod, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as bodyParser from 'body-parser';
import * as YAML from 'yaml';

import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new AppLogger();
  const app = await NestFactory.create(AppModule, {
    logger: new AppLogger(),
    bodyParser: false, // Disable built-in body parser
  });

  // Use separate middleware for webhook endpoints and regular JSON endpoints
  app.use((req: any, res: any, next: any) => {
    const isWebhookEndpoint = req.originalUrl && (
      req.originalUrl.includes('/payments/webhook') ||
      req.originalUrl.includes('/api/v1/payments/webhook') ||
      req.originalUrl.includes('/api/v1/stripe/webhook')
    );

    if (isWebhookEndpoint) {
      // For webhook endpoints, use raw body parser
      bodyParser.raw({ type: 'application/json' })(req, res, (err: any) => {
        if (err) {
          console.error('Error in raw body parser:', err);
          return next(err);
        }

        // Preserve raw body for Stripe signature verification
        req.rawBody = req.body;

        // Parse the raw body for route handlers
        if (req.body && req.body.length) {
          try {
            req.body = JSON.parse(req.body.toString());
          } catch (error) {
            console.error('Error parsing webhook body:', error);
          }
        }

        next();
      });
    } else {
      // For all other endpoints, use standard JSON parser
      bodyParser.json()(req, res, (err: any) => {
        if (err) {
          console.error('Error in JSON body parser:', err);
          return next(err);
        }
        next();
      });
    }
  });

  app.enableCors({
    origin: 'http://localhost:5173',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    credentials: true,
  });

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  );

  app.setGlobalPrefix('api/v1', {
    exclude: [{ path: 'health', method: RequestMethod.GET }],
  });

  // Enable Global Exception Handling
  app.useGlobalFilters(new GlobalExceptionFilter());

  // 🔹 Configure Swagger with Export Options
  const jsonExportUrl = `http://localhost:${envVariables.PORT}/docs-json`;
  const postmanExportUrl = `http://localhost:${envVariables.PORT}/docs-postman`;

  const config = new DocumentBuilder()
    .setTitle('Boilerplate API')
    .setDescription(
      `API documentation for Boilerplate. \n\n🔹 **Export Options:**\n` +
        `📄 [Export as JSON](${jsonExportUrl})\n\n` +
        `📬 [Export as Postman Collection](${postmanExportUrl})`,
    )
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);

  SwaggerModule.setup('docs', app, document, {
    swaggerOptions: {
      docExpansion: 'list',
      theme: 'dark',
      showExtensions: true,
      showCommonExtensions: true,
      supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
      defaultModelsExpandDepth: -1, // Hides models by default
    },
    customSiteTitle: 'Boilerplate API Docs',
    customfavIcon: 'https://example.com/favicon.ico',
  });

  // ✅ Add API routes for exporting documentation
  app.use('/docs-json', (_, res) => res.json(document));
  app.use('/docs-yaml', (_, res) => res.type('text/yaml').send(YAML.stringify(document)));

  // ✅ NEW: Add Postman Export Endpoint
  app.use('/docs-postman', (_, res) => {
    const postmanCollection = {
      info: {
        name: 'Boilerplate API',
        schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json',
      },
      item: Object.entries(document.paths).map(([path, methods]) => ({
        name: path,
        item: Object.entries(methods).map(([method, details]: any) => ({
          name: details.summary || `${method.toUpperCase()} ${path}`,
          request: {
            method: method.toUpperCase(),
            header: [
              {
                key: 'Authorization',
                value: 'Bearer {{token}}',
                type: 'text',
              },
            ],
            url: {
              raw: `http://localhost:${envVariables.PORT}/${path}`,
              host: [`http://localhost:${envVariables.PORT}`],
              path: path.split('/').filter(Boolean),
            },
            body: details.requestBody
              ? {
                  mode: 'raw',
                  raw: JSON.stringify(details.requestBody.content?.['application/json']?.example || {}),
                  options: { raw: { language: 'json' } },
                }
              : undefined,
          },
        })),
      })),
    };

    res.json(postmanCollection);
  });

  await app.listen(envVariables.PORT, () =>
    logger.log(`Server is running on http://localhost:${envVariables.PORT}/docs`),
  );
}
bootstrap();
