import { Logger } from '@nestjs/common';
import { FilterQuery, Model, QueryOptions, UpdateQuery, UpdateResult } from 'mongoose';

export class BaseRepository<Entity, Document> {
  constructor(protected readonly model: Model<Entity>) {}

  // Getter to expose the model for advanced operations
  get getModel(): Model<Entity> {
    return this.model;
  }

  async create(data: Partial<Document>, debug: boolean = false): Promise<Entity> {
    try {
      if (debug) {
        Logger.debug(`Creating document with data: ${JSON.stringify(data)}`);
        Logger.debug(`Model name: ${this.model.modelName}`);
      }

      // Validate the data against the schema
      const newDoc = new this.model(data);
      const validationError = newDoc.validateSync();

      if (validationError) {
        Logger.error(`Validation error: ${JSON.stringify(validationError)}`);
        throw validationError;
      }

      // Create the document
      const result = await this.model.create(data);

      if (debug) {
        Logger.debug(`Created document result: ${JSON.stringify(result)}`);
      }

      return result;
    } catch (error) {
      Logger.error(`Error creating document: ${error.message}`);
      Logger.error(`Stack trace: ${error.stack}`);

      // Instead of returning null, throw the error to allow proper handling
      throw error;
    }
  }


  async findOne(filter: FilterQuery<Entity>, debug: boolean = false): Promise<Entity> {
    try {
      if (debug) Logger.debug({ filter });
      const result = await this.model.findOne({ ...filter });
      if (debug) Logger.debug({ result });
      return result;
    } catch (error) {
      Logger.error(error);
    }
  }

  async getMany(filter: FilterQuery<Entity>, debug: boolean = false): Promise<any> {
    try {
      if (debug) Logger.debug({ filter });
      // Return the query without awaiting to enable method chaining
      return this.model.find({ ...filter });
    } catch (error) {
      Logger.error(error);
      throw error; // Re-throw to allow proper error handling upstream
    }
  }

  async findOneAndUpdate(
    filter: FilterQuery<Entity>,
    data: UpdateQuery<Entity>,
    options?: QueryOptions<Entity>,
    debug: boolean = false,
  ): Promise<Entity> {
    try {
      if (debug) Logger.debug({ filter, data });

      const updatedOptions = {
        new: true, // This is critical to return the updated document
        ...options,
      };

      // Log details for debugging
      Logger.log(`Performing update with filter: ${JSON.stringify(filter)}`);
      Logger.log(`Update data: ${JSON.stringify(data)}`);


      const result = await this.model.findOneAndUpdate(
        filter,
        data,
        updatedOptions
      ).exec(); // Execute the query explicitly

      if (!result) {
        Logger.warn(`No document found to update with filter: ${JSON.stringify(filter)}`);
        return null;
      }

      Logger.log(`Document updated successfully: ${result}`);

      const verifiedDoc = await this.model.findById(result._id).exec();

      return verifiedDoc;
    } catch (error) {
      Logger.error(`Error in findOneAndUpdate: ${error.message}`);
      Logger.error(error.stack);
      throw error;
    }
  }

  async updateOne(
    filter: FilterQuery<Entity>,
    data: UpdateQuery<Entity>,
    debug: boolean = false,
  ): Promise<UpdateResult> {
    try {
      if (debug) Logger.debug({ filter, data });
      const result = await this.model.updateOne(filter, data, { new: true });
      if (debug) Logger.debug({ result });
      return result;
    } catch (error) {
      Logger.error(error);
    }
  }

  async updateMany(filter: FilterQuery<Entity>, data: UpdateQuery<Entity>, debug: boolean = false) {
    try {
      if (debug) Logger.debug({ filter, data });
      const result = await this.model.updateMany(filter, data, { new: true });
      if (debug) Logger.debug({ result });
      return result;
    } catch (error) {
      Logger.error(error);
    }
  }

  async deleteOne(filter: FilterQuery<Entity>, session?: any, debug: boolean = false) {
    try {
      if (debug) Logger.debug({ filter });
      const result = await this.model.deleteOne({ ...filter }, { session });
      if (debug) Logger.debug({ result });
      return result;
    } catch (error) {
      Logger.error(error);
    }
  }

  async deleteMany(filter: FilterQuery<Entity>, session?: any, debug: boolean = false) {
    try {
      if (debug) Logger.debug({ filter });
      const result = await this.model.deleteMany({ ...filter }, { session });
      if (debug) Logger.debug({ result });
      return result;
    } catch (error) {
      Logger.error(error);
    }
  }

  // TODO: implement this
  async search(filter: FilterQuery<Entity>, debug: boolean = false) {
    try {
      if (debug) Logger.debug({ filter });
      const result = await this.model.find({ ...filter });
      if (debug) Logger.debug({ result });
      return result;
    } catch (error) {
      Logger.error(error);
    }
  }

  async exists(filter: FilterQuery<Entity>, debug: boolean = false) {
    try {
      if (debug) Logger.debug({ filter });
      const result = await this.model.exists({ ...filter });
      if (debug) Logger.debug({ result });
      return result;
    } catch (error) {
      Logger.error(error);
    }
  }

  async count(filter: FilterQuery<Entity>, debug: boolean = false) {
    try {
      if (debug) Logger.debug({ filter });
      const result = await this.model.countDocuments({ ...filter });
      if (debug) Logger.debug({ result });
      return result;
    } catch (error) {
      Logger.error(error);
    }
  }

  // Add this new method after the existing methods
  async save(entity: Entity, debug: boolean = false): Promise<Entity> {
    try {
      if (debug) Logger.debug({ entity });

      // If the entity has an _id, update it, otherwise create it
      if (entity && entity['_id']) {
        const { _id, ...updateData } = entity as any;
        const result = await this.model.findByIdAndUpdate(_id, updateData, { new: true }).exec();
        if (debug) Logger.debug({ result });
        return result;
      } else {
        // If no _id, create a new document
        const result = await this.model.create(entity);
        if (debug) Logger.debug({ result });
        return result;
      }
    } catch (error) {
      Logger.error(`Error in save method: ${error.message}`);
      Logger.error(error.stack);
      throw error;
    }
  }
}
