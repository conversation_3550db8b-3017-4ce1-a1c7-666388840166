import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { BaseRepository } from './base.repository';

// Define Book interfaces for type safety
interface BookDocument {
  title: string;
  description: string;
  price: number;
  type: 'hardcopy' | 'softcopy';
  image: string;
  imagePublicId: string;
  author: string;
  publishedYear: number;
  publisher: string;
  userId?: any; // Reference to User
  status?: 'pending' | 'processing' | 'shipped' | 'delivered';
  createdAt?: Date;
  updatedAt?: Date;
}

interface BookEntity extends BookDocument {
  _id: string;
}

@Injectable()
export class BookRepository extends BaseRepository<BookEntity, BookDocument> {
  constructor(@InjectModel('Book') private bookModel: Model<BookEntity>) {
    super(bookModel);
  }
}