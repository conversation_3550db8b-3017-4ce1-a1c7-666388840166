import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { BaseRepository } from './base.repository';

// Define Course interfaces for type safety
interface CourseDocument {
  title: string;
  description: string;
  price: number;
  image: string;
  imagePublicId: string;
  modules: any[];
  instructor: any;
  createdAt?: Date;
  updatedAt?: Date;
}

interface CourseEntity extends CourseDocument {
  _id: string;
}

@Injectable()
export class CourseRepository extends BaseRepository<CourseEntity, CourseDocument> {
  constructor(@InjectModel('Course') private courseModel: Model<CourseEntity>) {
    super(courseModel);
  }
}