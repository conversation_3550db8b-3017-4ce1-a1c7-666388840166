import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';

import { BaseRepository } from './base.repository';

// Define Insight interfaces for type safety
// InsightDocument represents the shape of the data for creating/updating insights
export interface InsightDocument {
  title: string;
  details: string;
  images: Array<{
    url: string;
    publicId: string;
  }>;
  relatedTo: {
    type: 'Book' | 'Course';
    id: Types.ObjectId;
  };
  createdBy: Types.ObjectId;
  createdAt?: Date;
  updatedAt?: Date;
}

// InsightEntity represents the insight document as stored in MongoDB, including _id
export interface InsightEntity extends InsightDocument {
  _id: Types.ObjectId; // or string, depending on how you handle IDs
}

@Injectable()
export class InsightRepository extends BaseRepository<InsightEntity, InsightDocument> {
  constructor(@InjectModel('Insight') private insightModel: Model<InsightEntity>) {
    super(insightModel);
  }

  // You can add specific methods for insights here if needed,
  // for example, methods that include specific population logic.

  async findAllWithPopulate(filter: any = {}): Promise<InsightEntity[]> {
    return this.insightModel.find(filter)
      .populate('createdBy', 'firstName lastName email')
      .populate({
        path: 'relatedTo.id',
        select: 'title description image', // Add other fields if necessary
      })
      .sort({ createdAt: -1 })
      .exec();
  }

  async findOneByIdWithPopulate(id: string): Promise<InsightEntity | null> {
    return this.insightModel.findById(id)
      .populate('createdBy', 'firstName lastName email')
      .populate({
        path: 'relatedTo.id',
        select: 'title description image', // Add other fields if necessary
      })
      .exec();
  }

  async findByRelatedEntityWithPopulate(type: 'Book' | 'Course', entityId: string): Promise<InsightEntity[]> {
    return this.insightModel.find({
      'relatedTo.type': type,
      'relatedTo.id': new Types.ObjectId(entityId),
    })
      .populate('createdBy', 'firstName lastName email')
      // No need to populate relatedTo.id here as it's part of the query condition
      .sort({ createdAt: -1 })
      .exec();
  }
}
