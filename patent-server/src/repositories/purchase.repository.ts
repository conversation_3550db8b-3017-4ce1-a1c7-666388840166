import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { BaseRepository } from './base.repository';

// Define shipping address interface
interface ShippingAddress {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phoneNumber: string;
}

// Define Purchase interfaces for type safety
interface PurchaseDocument {
  userId: any;
  courseId?: any;
  bookId?: any;
  amount: number;
  date?: Date;
  status: string;
  paymentMethod: string;
  transactionId: string;
  requiresShipping?: boolean;
  shippingAddress?: ShippingAddress;
  createdAt?: Date;
  updatedAt?: Date;
}

interface PurchaseEntity extends PurchaseDocument {
  _id: string;
}

@Injectable()
export class PurchaseRepository extends BaseRepository<PurchaseEntity, PurchaseDocument> {
  constructor(@InjectModel('Purchase') private purchaseModel: Model<PurchaseEntity>) {
    super(purchaseModel);
  }
}