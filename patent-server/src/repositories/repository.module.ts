import { User, UserSchema } from '@database';
import { DynamicModule, Global, Module } from '@nestjs/common';
import { MongooseModule, getModelToken } from '@nestjs/mongoose';

import { BookSchema } from '../database/schemas/book.schema';
import { CourseSchema } from '../database/schemas/course.schema';
import { InsightSchema } from '../database/schemas/insight.schema';
import { PurchaseSchema } from '../database/schemas/purchase.schema';

import { BaseRepository } from './base.repository';
import { BookRepository } from './book.repository';
import { CourseRepository } from './course.repository';
import { InsightRepository } from './insight.repository';
import { PurchaseRepository } from './purchase.repository';
import { UserRepository } from './user.repository';

const models = [
  { name: User.name, schema: UserSchema },
  { name: 'Course', schema: CourseSchema },
  { name: 'Purchase', schema: PurchaseSchema },
  { name: 'Book', schema: BookSchema },
  { name: 'Insight', schema: InsightSchema }
];

@Global()
@Module({})
export class RepositoryModule {
  static forRoot(): DynamicModule {
    const providers = models.map(({ name }) => {
      if (name === 'Course') {
        return {
          provide: `${name}Repository`,
          useFactory: (model) => new CourseRepository(model),
          inject: [getModelToken(name)],
        };
      } else if (name === 'Purchase') {
        return {
          provide: `${name}Repository`,
          useFactory: (model) => new PurchaseRepository(model),
          inject: [getModelToken(name)],
        };
      } else if (name === User.name) {
        return {
          provide: `${name}Repository`,
          useFactory: (model) => new UserRepository(model),
          inject: [getModelToken(name)],
        };
      } else if (name === 'Book') {
        return {
          provide: `${name}Repository`,
          useFactory: (model) => new BookRepository(model),
          inject: [getModelToken(name)],
        };
      } else if (name === 'Insight') {
        return {
          provide: `${name}Repository`,
          useFactory: (model) => new InsightRepository(model),
          inject: [getModelToken(name)],
        };
      }
      return {
        provide: `${name}Repository`,
        useFactory: (model) => new BaseRepository(model),
        inject: [getModelToken(name)],
      };
    });

    return {
      module: RepositoryModule,
      imports: [MongooseModule.forFeature(models)],
      providers: providers,
      exports: providers,
    };
  }
}
