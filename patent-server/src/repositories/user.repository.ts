

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { BaseRepository } from './base.repository';

interface UserDocument {
  firstName: string; 
  lastName: string;  
  email: string;
  username?: string;
  password: string;
  avatar?: string;
  role?: string;
  isVerified?: boolean;
  status?: string;    
  isAdmin?: boolean; 
  stripeCustomerId?: string; 
  createdAt?: Date;
  updatedAt?: Date;
}

interface UserEntity extends UserDocument {
  _id: string;
}

@Injectable()
export class UserRepository extends BaseRepository<UserEntity, UserDocument> {
  constructor(@InjectModel('User') private userModel: Model<UserEntity>) {
    super(userModel);
  }
}
