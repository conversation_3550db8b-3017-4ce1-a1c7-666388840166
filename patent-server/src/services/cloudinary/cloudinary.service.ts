// import { envVariables } from '@app/config';
// import { Injectable, Logger } from '@nestjs/common';
// import { v2 as cloudinary } from 'cloudinary';
// import * as crypto from 'crypto';

// // Define the File interface that matches <PERSON><PERSON>'s file object structure
// interface MulterFile {
//   fieldname: string;
//   originalname: string;
//   encoding: string;
//   mimetype: string;
//   size: number;
//   destination?: string;
//   filename?: string;
//   path?: string;
//   buffer?: Buffer;
// }

// @Injectable()
// export class CloudinaryService {
//   private readonly logger = new Logger(CloudinaryService.name);

//   constructor() {
//     // Configure Cloudinary with environment variables
//     cloudinary.config({
//       cloud_name: envVariables.CLOUDINARY_CLOUD_NAME,
//       api_key: envVariables.CLOUDINARY_API_KEY,
//       api_secret: envVariables.CLOUDINARY_API_SECRET,
//     });
//   }

//   /**
//    * Upload a file to Cloudinary
//    * @param file Buffer or path of the file to upload
//    * @param folder Folder name in Cloudinary
//    * @returns Object containing URL, public ID, and resource type
//    */
//   async uploadFile(file: MulterFile | string, folder = 'courses', options = {}): Promise<any> {
//     try {
//       // If file is a path string, upload directly
//       if (typeof file === 'string' && (file.startsWith('/') || file.startsWith('http'))) {
//         const result = await cloudinary.uploader.upload(file, {
//           folder: folder,
//           resource_type: 'auto',
//         });

//         return {
//           url: result.secure_url,
//           public_id: result.public_id,
//           resource_type: result.resource_type,
//         };
//       }
//       // For Multer file objects
//       else if (file && (file as MulterFile).buffer) {
//         const multerFile = file as MulterFile;

//         return new Promise((resolve, reject) => {
//           // Determine resource type - use options if provided, otherwise auto-detect
//           const resourceType = options['resource_type'] || 'auto';

//           const uploadOptions = {
//             folder: folder,
//             resource_type: resourceType
//           };

//           // For PDF files, ensure we use raw resource type
//           if (multerFile.mimetype === 'application/pdf') {
//             uploadOptions.resource_type = 'raw';
//           }

//           const uploadStream = cloudinary.uploader.upload_stream(
//             uploadOptions,
//             (error, result) => {
//               if (error) return reject(error);

//               let url = result.secure_url;

//               // For PDF files, ensure the URL uses the raw delivery type
//               if (multerFile.mimetype === 'application/pdf' && url.includes('/image/')) {
//                 url = url.replace('/image/', '/raw/');
//               }

//               resolve({
//                 url: url,
//                 public_id: result.public_id,
//                 resource_type: result.resource_type,
//               });
//             }
//           );

//           uploadStream.end(multerFile.buffer);
//         });
//       }

//       throw new Error('Invalid file format for upload');
//     } catch (error) {
//       this.logger.error(`Error uploading to Cloudinary: ${error.message}`);
//       throw new Error(`Failed to upload file: ${error.message}`);
//     }
//   }

//   /**
//    * Delete a file from Cloudinary
//    * @param publicId Public ID of the file to delete
//    * @param resourceType Type of resource (image, video, raw)
//    * @returns Object indicating success or failure
//    */
//   async deleteFile(publicId: string, resourceType = 'image'): Promise<{ success: boolean; message?: string }> {
//     try {
//       if (!publicId) {
//         return { success: false, message: 'No public ID provided' };
//       }

//       await cloudinary.uploader.destroy(publicId, { resource_type: resourceType });
//       return { success: true };
//     } catch (error) {
//       this.logger.error(`Error deleting from Cloudinary: ${error.message}`);
//       return { success: false, message: error.message };
//     }
//   }

//   /**
//    * Generate a signed URL for a Cloudinary resource
//    * @param publicId Public ID of the resource
//    * @param resourceType Type of resource (image, video, raw)
//    * @param expiresAt Expiration time in seconds (default: 1 hour)
//    * @returns Signed URL for the resource
//    */
//   generateSignedUrl(publicId: string, resourceType = 'raw', expiresAt = Math.floor(Date.now() / 1000) + 3600): string {
//     try {
//       if (!publicId) {
//         this.logger.error('No public ID provided for signed URL generation');
//         return '';
//       }

//       // For PDFs, ensure we're using the correct format and delivery type
//       let format = '';
//       let deliveryType = 'upload';

//       if (resourceType === 'raw' && publicId.includes('books-pdf')) {
//         format = 'pdf';
//         // Use 'raw' delivery type for PDFs
//         deliveryType = 'raw';
//       }

//       // Generate the direct download URL for raw files (PDFs)
//       if (resourceType === 'raw') {
//         // Format: https://res.cloudinary.com/[cloud_name]/raw/upload/[public_id].pdf
//         const cloudName = envVariables.CLOUDINARY_CLOUD_NAME;

//         // Extract the filename from the public_id
//         const filename = publicId.split('/').pop();

//         // For PDFs in Cloudinary, we need to use the direct URL format
//         // The URL you provided is in this format: https://res.cloudinary.com/dldgiyz7c/image/upload/v1746609787/books-pdf/cburl4hq2yj3svp17y2x.pdf
//         // We need to convert it to: https://res.cloudinary.com/dldgiyz7c/raw/upload/v1746609787/books-pdf/cburl4hq2yj3svp17y2x.pdf

//         // First, check if we have a full URL stored in the database
//         if (publicId.startsWith('http')) {
//           // If it's a full URL, just replace 'image' with 'raw'
//           const directUrl = publicId.replace('/image/', '/raw/');
//           return directUrl;
//         }

//         // If we just have the public ID, construct the URL
//         // Try to extract version if available
//         const parts = publicId.split('/');
//         let version = '';
//         let path = publicId;

//         // Look for version number (v1234567890)
//         for (let i = 0; i < parts.length; i++) {
//           if (parts[i].startsWith('v') && /^v\d+$/.test(parts[i])) {
//             version = parts[i] + '/';
//             // Remove version from path
//             path = parts.slice(i + 1).join('/');
//             break;
//           }
//         }

//         const directUrl = `https://res.cloudinary.com/${cloudName}/raw/upload/${version}${path}.pdf`;

//         return directUrl;
//       }

//       // For other resource types, use the standard signed URL
//       const signedUrl = cloudinary.url(publicId, {
//         resource_type: resourceType,
//         format: format,
//         type: deliveryType,
//         secure: true,
//         sign_url: true,
//         expires_at: expiresAt,
//       });

//       return signedUrl;
//     } catch (error) {
//       this.logger.error(`Error generating signed URL: ${error.message}`);
//       return '';
//     }
//   }
// }