import * as fs from 'fs';
import * as path from 'path';

import { Injectable, LoggerService, LogLevel } from '@nestjs/common';

@Injectable()
export class AppLogger implements LoggerService {
  private buffer: string[] = []; // Stores all logs without limit
  private readonly logDir = path.join(process.cwd(), 'src/logs'); // Ensures logs are saved in src/logs/
  private readonly logFilePath = path.join(this.logDir, 'app.log');
  private prefix = this.colorize('Boilerplate', 'green', true); // Boilerplate in bold blue

  constructor() {
    this.ensureLogDirectoryExists();
  }

  /**
   * Ensures that the src/logs directory exists before writing logs.
   */
  private ensureLogDirectoryExists() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true }); // Create logs directory if it doesn't exist
    }
  }

  /**
   * Adds ANSI colors based on log levels.
   */
  private colorize(text: string, color: string, bold = false): string {
    const colors: { [key: string]: string } = {
      reset: '\x1b[0m',
      bold: '\x1b[1m',
      black: '\x1b[30m',
      red: '\x1b[31m',
      green: '\x1b[32m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      magenta: '\x1b[35m',
      cyan: '\x1b[36m',
      white: '\x1b[37m',
      gray: '\x1b[90m',
    };
    return `${bold ? colors.bold : ''}${colors[color] || ''}${text}${colors.reset}`;
  }

  private formatMessage(level: LogLevel, message: any, context?: string): string {
    const timestamp = this.colorize(new Date().toISOString(), 'cyan'); // Timestamp in gray
    // const levelTag = this.getColoredLevel(level); // Get colored log level
    const contextTag = context ? this.colorize(`[${context}]`, 'cyan') : ''; // Context in cyan
    return `${this.prefix} ${timestamp} ${contextTag} ${message}`;
  }

  private writeToFile(message: string) {
    const plainMessage = message.replace(/\x1B\[\d+m/g, ''); // Remove ANSI color codes before writing
    fs.appendFile(this.logFilePath, plainMessage + '\n', (err) => {
      if (err) {
        // eslint-disable-next-line no-console
        console.error(this.colorize('Failed to write to log file:', 'red'), err);
      }
    });
  }

  private logMessage(level: LogLevel, message: any, context?: string) {
    const formattedMessage = this.formatMessage(level, message, context);
    // eslint-disable-next-line no-console
    console[level]?.(formattedMessage);

    this.buffer.push(formattedMessage);
    this.writeToFile(formattedMessage);
  }

  log(message: any, context?: string) {
    this.logMessage('log', message, context);
  }

  warn(message: any, context?: string) {
    this.logMessage('warn', message, context);
  }

  error(message: any, trace?: string, context?: string) {
    this.logMessage('error', `${message} ${trace ? `\nTrace: ${trace}` : ''}`, context);
  }

  debug(message: any, context?: string) {
    this.logMessage('debug', message, context);
  }

  verbose(message: any, context?: string) {
    this.logMessage('verbose', message, context);
  }

  getBuffer(): string[] {
    return [...this.buffer];
  }
}
