import { envVariables } from '@app/config';
import { Injectable, Logger } from '@nestjs/common';
import { S3 } from 'aws-sdk';

interface MulterFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  destination?: string;
  filename?: string;
  path?: string;
  buffer?: Buffer;
}

@Injectable()
export class S3Service {
  private readonly s3: S3;
  private readonly logger = new Logger(S3Service.name);

  constructor() {
    this.s3 = new S3({
      accessKeyId: envVariables.AWS_S3_ACCESS_KEY_ID,
      secretAccessKey: envVariables.AWS_S3_SECRET_ACCESS_KEY,
      region: envVariables.AWS_S3_REGION,
    });
  }

  /**
   * Upload a file to S3
   * @param file Buffer or path of the file to upload
   * @param folder Folder name in S3
   * @returns Object containing URL and key
   */
  async uploadFile(file: MulterFile | string, folder = 'courses', options = {}): Promise<any> {
    try {
      let buffer: Buffer;
      let contentType: string;
      let fileName: string;

      if (typeof file === 'string') {
        buffer = Buffer.from(file);
        contentType = 'text/plain';
        fileName = `${Date.now()}.txt`;
      } else {
        buffer = file.buffer;
        contentType = file.mimetype;
        fileName = `${Date.now()}-${file.originalname}`;
      }

      const key = `${folder}/${fileName}`;

      const uploadParams = {
        Bucket: envVariables.AWS_S3_BUCKET_NAME,
        Key: key,
        Body: buffer,
        ContentType: contentType,
        ...options,
      };

      const result = await this.s3.upload(uploadParams).promise();

      return {
        url: result.Location,
        public_id: key,
        resource_type: this.getResourceType(contentType),
      };
    } catch (error) {
      this.logger.error(`Error uploading file to S3: ${error.message}`);
      throw error;
    }
  }

  /**
   * Delete a file from S3
   * @param key Key (path) of the file to delete
   * @returns Object indicating success or failure
   */
  async deleteFile(key: string): Promise<{ success: boolean; message?: string }> {
    try {
      await this.s3.deleteObject({
        Bucket: envVariables.AWS_S3_BUCKET_NAME,
        Key: key,
      }).promise();

      return { success: true };
    } catch (error) {
      this.logger.error(`Error deleting file from S3: ${error.message}`);
      return { success: false, message: error.message };
    }
  }

  /**
   * Generate a signed URL for an S3 object
   * @param key Key (path) of the file
   * @param resourceType Optional resource type (ignored for S3, used for compatibility)
   * @param expiresIn Expiration time in seconds (default: 1 hour)
   * @returns Signed URL for the file
   */
  generateSignedUrl(key: string, resourceType?: string, expiresIn = 3600): string {
    try {
      // Validate key
      if (!key) {
        this.logger.error('Cannot generate signed URL: No key provided');
        return '';
      }

      // Log the parameters for debugging
      this.logger.log(`Generating signed URL for key: ${key}, resourceType: ${resourceType}, expiresIn: ${expiresIn}`);

      // Ensure expiresIn is a number
      let expires = 3600; // Default 1 hour

      // If expiresIn is a number, use it
      if (typeof expiresIn === 'number') {
        expires = expiresIn;
      } else if (typeof expiresIn === 'string' && !isNaN(parseInt(expiresIn))) {
        // If expiresIn is a string that can be parsed as a number, use it
        expires = parseInt(expiresIn);
      }

      // Check if the object exists in S3 before generating URL
      this.logger.log(`Checking if object exists in S3: ${envVariables.AWS_S3_BUCKET_NAME}/${key}`);

      // Generate the signed URL
      const signedUrl = this.s3.getSignedUrl('getObject', {
        Bucket: envVariables.AWS_S3_BUCKET_NAME,
        Key: key,
        Expires: expires,
      });

      this.logger.log(`Generated signed URL: ${signedUrl.substring(0, 100)}...`);

      return signedUrl;
    } catch (error) {
      this.logger.error(`Error generating signed URL: ${error.message}`);
      if (error.stack) {
        this.logger.error(`Stack trace: ${error.stack}`);
      }
      return ''; // Return empty string instead of throwing to avoid breaking the download process
    }
  }

  /**
   * Check if a file exists in S3
   * @param key Key (path) of the file to check
   * @returns Promise resolving to true if the file exists, false otherwise
   */
  async fileExists(key: string): Promise<boolean> {
    try {
      this.logger.log(`Checking if file exists in S3: ${key}`);
      await this.s3.headObject({
        Bucket: envVariables.AWS_S3_BUCKET_NAME,
        Key: key,
      }).promise();
      this.logger.log(`File exists in S3: ${key}`);
      return true;
    } catch (error) {
      this.logger.error(`File does not exist in S3 or error checking: ${key}, Error: ${error.message}`);
      return false;
    }
  }

  private getResourceType(contentType: string): string {
    if (contentType.startsWith('image/')) return 'image';
    if (contentType.startsWith('video/')) return 'video';
    if (contentType === 'application/pdf') return 'pdf';
    return 'raw';
  }
}