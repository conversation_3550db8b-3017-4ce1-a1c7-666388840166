import { Type } from 'class-transformer';
import {
  IsDefined,
  IsDate,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  IsMongoId,
  Validate,
  IsNumber,
  IsString,
  MinLength,
} from 'class-validator';

class IdDto {
  @IsDefined()
  @IsMongoId()
  _id: string;
}

class UserIdDto {
  @IsDefined()
  @IsMongoId()
  userId: string;
}

@ValidatorConstraint({ name: 'DateRange', async: false })
class DateRangeValidator implements ValidatorConstraintInterface {
  validate(startDate: any, args: ValidationArguments) {
    const { object } = args;
    return (object as any)?.endDate && new Date(startDate) < new Date((object as any)?.endDate);
  }

  defaultMessage(args: ValidationArguments) {
    return 'startDate must be less than or equal to endDate';
  }
}

class DateRangeDto {
  @IsDefined()
  @Type(() => Date)
  @IsDate()
  @Validate(DateRangeValidator)
  startDate: string;

  @IsDefined()
  @Type(() => Date)
  @IsDate()
  endDate: string;
}

class ListDto {
  @IsDefined()
  @Type(() => Number)
  @IsNumber()
  page: number;

  @IsDefined()
  @Type(() => Number)
  @IsNumber()
  pageSize: number;
}

class SearchDto extends ListDto {
  @IsDefined()
  @IsString()
  @MinLength(2)
  key: string;
}
export { IdDto, UserIdDto, DateRangeDto, ListDto, SearchDto };
