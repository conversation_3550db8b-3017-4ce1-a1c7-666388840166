import { createDecipheriv, createHash } from 'crypto';

import { envVariables } from '@app/config';
import { AppLogger } from '@app/services/logger/AppLogger';

async function decryptData(encryptedData: string): Promise<any> {
  const key = createHash('sha256').update(envVariables.USER_ENCRYPT_KEY).digest('base64').substr(0, 32);
  const [ivBase64, encryptedBase64] = encryptedData.split(':');
  const iv = Buffer.from(ivBase64, 'base64');
  const encryptedText = Buffer.from(encryptedBase64, 'base64').toString('hex');
  const decipher = createDecipheriv('aes-256-ctr', key, iv);
  let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}

function hasEmptyValues(data: Record<string, any>, parentKey = '', logger: AppLogger): string[] {
  const emptyFields: string[] = [];

  const checkEmpty = (value: any): boolean => {
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      return hasEmptyValues(value, '', logger).length > 0;
    }
    return (
      value === undefined ||
      value === null ||
      value === 0 ||
      value === '' ||
      Number.isNaN(value) ||
      (Array.isArray(value) && value.length === 0)
    );
  };

  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      const element = data[key];
      const fullKey = parentKey ? `${parentKey}.${key}` : key;

      if (checkEmpty(element)) {
        emptyFields.push(fullKey);
      }

      // If it's an object, recursively check for empty values
      if (typeof element === 'object' && element !== null && !Array.isArray(element)) {
        const nestedEmptyFields = hasEmptyValues(element, fullKey, logger);
        emptyFields.push(...nestedEmptyFields);
      }
    }
  }
  if (emptyFields.length > 0) logger.error(JSON.stringify(emptyFields, null, 2));
  return emptyFields;
}

export { hasEmptyValues, decryptData };
