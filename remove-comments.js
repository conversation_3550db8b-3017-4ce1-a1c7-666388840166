const fs = require('fs');
const path = require('path');

// Function to remove comments from a file
function removeCommentsFromFile(filePath) {
  try {
    // Read the file content
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Store original content length for comparison
    const originalLength = content.length;
    
    // Remove multi-line comments (/* ... */) - but be careful with RegExp patterns
    content = content.replace(/\/\*[\s\S]*?\*\//g, '');
    
    // Remove single-line comments (// ...) but not URLs (http://)
    content = content.replace(/([^:])\/\/.*$/gm, '$1');
    
    // Remove empty lines that might be left after removing comments
    content = content.replace(/^\s*[\r\n]/gm, '');
    
    // Only write back if content changed
    if (content.length !== originalLength) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Removed comments from: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing file ${filePath}:`, error);
    return false;
  }
}

// Function to recursively process all JS/JSX files in a directory
function processDirectory(directory) {
  const items = fs.readdirSync(directory);
  let totalProcessed = 0;
  
  for (const item of items) {
    const itemPath = path.join(directory, item);
    const stats = fs.statSync(itemPath);
    
    if (stats.isDirectory()) {
      // Recursively process subdirectories
      totalProcessed += processDirectory(itemPath);
    } else if (stats.isFile() && (itemPath.endsWith('.js') || itemPath.endsWith('.jsx'))) {
      // Process JS/JSX files
      if (removeCommentsFromFile(itemPath)) {
        totalProcessed++;
      }
    }
  }
  
  return totalProcessed;
}

// Main function
function main() {
  const targetDir = process.argv[2] || './patent-client/src';
  
  if (!fs.existsSync(targetDir)) {
    console.error(`Directory not found: ${targetDir}`);
    process.exit(1);
  }
  
  console.log(`Removing comments from JavaScript/JSX files in: ${targetDir}`);
  const totalProcessed = processDirectory(targetDir);
  console.log(`Completed! Processed ${totalProcessed} files.`);
}

main();
